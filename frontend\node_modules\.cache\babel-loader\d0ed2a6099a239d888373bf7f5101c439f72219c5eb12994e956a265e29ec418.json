{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\NewWelcomeSplash.js\";\nimport React from 'react';\nimport WelcomeLayout from '../components/WelcomeLayout/WelcomeLayout';\nimport WelcomeMainContent from '../components/WelcomeLayout/WelcomeMainContent';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewWelcomeSplash = () => {\n  return /*#__PURE__*/_jsxDEV(WelcomeLayout, {\n    children: /*#__PURE__*/_jsxDEV(WelcomeMainContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = NewWelcomeSplash;\nexport default NewWelcomeSplash;\nvar _c;\n$RefreshReg$(_c, \"NewWelcomeSplash\");", "map": {"version": 3, "names": ["React", "WelcomeLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "NewWelcomeSplash", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/NewWelcomeSplash.js"], "sourcesContent": ["import React from 'react';\nimport WelcomeLayout from '../components/WelcomeLayout/WelcomeLayout';\nimport WelcomeMainContent from '../components/WelcomeLayout/WelcomeMainContent';\n\nconst NewWelcomeSplash = () => {\n  return (\n    <WelcomeLayout>\n      <WelcomeMainContent />\n    </WelcomeLayout>\n  );\n};\n\nexport default NewWelcomeSplash;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,2CAA2C;AACrE,OAAOC,kBAAkB,MAAM,gDAAgD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhF,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACED,OAAA,CAACH,aAAa;IAAAK,QAAA,eACZF,OAAA,CAACF,kBAAkB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEpB,CAAC;AAACC,EAAA,GANIN,gBAAgB;AAQtB,eAAeA,gBAAgB;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}