/* Fix: Force .welcome to be scrollable and block-level */
.welcome {
  background-color: #13141B;
  color: #fff;
  padding: 20px 0;
  width: 100%;
  margin: 0;
  position: relative;
  display: block;
}
/* Base Layout */
/* Removed duplicate .welcome flex definition to fix scroll */

/* Welcome splash specific styles */
.welcome-splash {
  padding-top: 80px; /* Account for fixed header */
}

.welcome__content {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Hero Section */
.hero {
  width: 100%;
  height: 300px;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 100%;
}

.hero__slider {
  width: 100%;
  height: 100%;
  position: relative;
}

.hero__image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
  opacity: 1;
  z-index: 1;
}

.hero__image.active {
  z-index: 2;
}

.hero__nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  z-index: 10;
  opacity: 0.7;
  transition: opacity 0.3s, background 0.3s;
}

.hero__nav:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.8);
}

.hero__nav--prev {
  left: 10px;
}

.hero__nav--next {
  right: 10px;
}

.hero__indicators {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.hero__indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: background 0.3s, transform 0.3s;
  padding: 0;
}

.hero__indicator.active {
  background: #409cff;
  transform: scale(1.2);
}

.hero__indicator:hover {
  background: rgba(255, 255, 255, 0.8);
}

/* Section Common Styles */
.section {
  background: rgba(26, 27, 35, 0.85);
  border-radius: 12px;
  padding: 10px 0;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 100%;
}

.challenges-section {
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 0;
  padding-right: 0;
  width: 100%;
}

.section__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 0 10px 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.challenges-section .section__header {
  margin-bottom: 4px;
  padding-bottom: 4px;
}

.section__title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.challenges-section .section__title {
  font-size: 1.1rem;
}

.section__actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-welcome {
  color: #fff;
  font-size: 0.9rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section__link {
  color: #ffffff;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  background: linear-gradient(135deg, #00ff87, #00cc6a);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section__link:hover {
  color: #ffffff;
  background: linear-gradient(135deg, #33ff9f, #00e676);
  transform: translateY(-1px);
}

.section__link.login-link {
  background: linear-gradient(135deg, #409cff, #3b7dff);
  color: #fff;
}

.section__link.login-link:hover {
  background: linear-gradient(135deg, #66b2ff, #5c99ff);
  color: #fff;
}

/* Challenge List - Redesigned */
.challenges-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  background: #1A1B23;
  padding: 0;
  border-radius: 8px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0;
}

.sport-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Challenge List Header - Redesigned */
.challenge-list-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: 8px 12px;
  color: #666;
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  background: #f0f0f0;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 10;
  margin: 0;
  width: 100%;
}

.header-match,
.header-odds,
.header-action {
  font-weight: 600;
  text-align: center;
}

/* Challenge Card - Redesigned */
.challenge-card {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  align-items: center;
  background: #ffffff;
  padding: 6px 10px;
  gap: 10px;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  width: 100%;
  position: relative;
  margin: 0;
}

.challenge-card:last-child {
  border-bottom: none;
}

.challenge-card:hover {
  background-color: #f8f9fa;
}

.challenge-card.live-match {
  background-color: rgba(255, 69, 58, 0.05);
}

/* Match Container */
.match-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
  padding-left: 8px;
}

.live-match .match-container {
  border-left: 3px solid #ff453a;
  padding-left: 10px;
}

.live-indicator {
  position: absolute;
  top: 5px;
  left: 5px;
  background-color: #ff453a;
  color: white;
  font-size: 0.6rem;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(255, 69, 58, 0.3);
  z-index: 5;
  display: flex;
  align-items: center;
  gap: 4px;
}

.live-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: white;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

/* Teams Display - Redesigned */
.teams-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  min-width: 0;
}

.team {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.team-a {
  text-align: right;
  justify-content: flex-end;
}

.team-b {
  text-align: left;
  justify-content: flex-start;
}

.vs-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
}

.vs-text {
  font-weight: 600;
  color: #888;
  font-size: 0.75rem;
}

.team-logo {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  object-fit: cover;
  background: #f0f0f0;
  padding: 2px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.team-name {
  font-size: 0.85rem;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 600;
  letter-spacing: 0.2px;
  line-height: 1.2;
  max-width: 100%;
}

/* Match Details */
.match-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: #666;
  padding: 4px 0;
}

.match-type {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  text-align: center;
  font-weight: 600;
  color: #666;
  background: #f0f0f0;
  border: 1px solid #e0e0e0;
}

.match-type.full_time, .match-type.FT {
  color: #00a651;
  background: rgba(0, 166, 81, 0.1);
  border: 1px solid rgba(0, 166, 81, 0.2);
}

.match-type.half_time, .match-type.HT {
  color: #0078d4;
  background: rgba(0, 120, 212, 0.1);
  border: 1px solid rgba(0, 120, 212, 0.2);
}

.match-time {
  font-size: 0.7rem;
  color: #666;
}

.in-progress {
  color: #ff453a;
  font-weight: 600;
}

.ended {
  color: #888;
  font-weight: 600;
}

/* Odds Display - Redesigned */
.odds-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
  background: #f9f9f9;
  border-radius: 6px;
  padding: 8px;
}

.odds-header {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.odds-label {
  font-size: 0.65rem;
  color: #666;
  text-align: center;
  flex: 1;
  font-weight: 600;
}

.odds-values {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.odds-item {
  text-align: center;
  flex: 1;
  padding: 0;
}

.odds-value {
  font-size: 0.85rem;
  padding: 6px 8px;
  display: block;
  border-radius: 4px;
  background: white;
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
  cursor: pointer;
  font-weight: 700;
}

.odds-value:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.odds-item.home .odds-value {
  color: #409cff;
}

.odds-item.draw .odds-value {
  color: #666;
}

.odds-item.away .odds-value {
  color: #ff6b63;
}

/* Action Button - Redesigned */
.action-container {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-button {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-weight: 700;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 120px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.action-button.place-bet {
  background: linear-gradient(135deg, #409cff, #3b7dff);
  color: #ffffff;
}

.action-button.place-bet:hover {
  background: linear-gradient(135deg, #3b7dff, #3366ff);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 125, 255, 0.3);
}

.action-button.login {
  background: linear-gradient(135deg, #ff6b63, #ff453a);
  color: #fff;
}

.action-button.login:hover {
  background: linear-gradient(135deg, #ff453a, #e03c32);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 69, 58, 0.3);
}

.status-badge.live {
  color: #ffffff;
  background-color: #ff453a;
  box-shadow: 0 0 0 2px rgba(255, 69, 58, 0.3);
}

.status-badge.expired, .status-badge.completed {
  color: #ffffff;
  background: #9e9e9e;
}

.countdown-completed {
  font-size: 0.75rem;
  color: #ffffff;
  background: #9e9e9e;
  padding: 4px 10px;
  border-radius: 4px;
  display: inline-block;
}

.status-badge.upcoming {
  color: #409cff;
}

/* Removed animation for better performance */

/* Countdown Display */
.countdown {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.countdown span {
  background: rgba(64, 156, 255, 0.1);
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 0.75rem;
  color: #409cff;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
  border: 1px solid rgba(64, 156, 255, 0.2);
}

/* Recent Bets Optimization */
.recent-bets-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  width: 100%;
  max-width: 100%;
}

.recent-bet-card {
  background: linear-gradient(145deg, #1E1F28, #252631);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.recent-bet-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 255, 135, 0.1);
}

.bet-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  gap: 8px;
}

.bet-ref {
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  color: #9e9e9e;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgba(255, 255, 255, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.bet-amount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.bet-amount span {
  font-size: 0.75rem;
  padding: 3px 8px;
  min-width: 70px;
  text-align: right;
  background: rgba(0, 255, 135, 0.1);
  color: #00ff87;
  border: 1px solid rgba(0, 255, 135, 0.3);
  border-radius: 4px;
  white-space: nowrap;
}

.bet-teams {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  padding: 10px 0;
}

.bet-team {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.bet-team-logo {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  background: #2A2B36;
  padding: 4px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bet-team-logo:hover {
  border-color: #00ff87;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 255, 135, 0.2);
}

.bet-team-name {
  font-size: 0.85rem;
  text-align: center;
  font-weight: 500;
  color: #fff;
  letter-spacing: 0.3px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bet-vs {
  color: #9e9e9e;
  font-weight: 600;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 6px 10px;
  border-radius: 6px;
  min-width: 40px;
  text-align: center;
  flex-shrink: 0;
}

.bet-vs-small {
  color: #9e9e9e;
  font-weight: 600;
  font-size: 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  min-width: 30px;
  text-align: center;
  flex-shrink: 0;
  flex: 0 0 auto;
}

.bet-odds {
  display: flex;
  justify-content: space-between;
  background: #2A2B36;
  padding: 8px;
  border-radius: 6px;
  margin: 5px 0;
}

.bet-odds-item {
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s;
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.bet-odds-item:hover {
  transform: scale(1.05);
}

.bet-odds-item:first-child .bet-odds-value {
  color: #00ff87; /* Green for team A win */
}

.bet-odds-item:nth-child(2) .bet-odds-value {
  color: #ffffff; /* White for draw */
}

.bet-odds-item:last-child .bet-odds-value {
  color: #ff453a; /* Red for team B win */
}

.bet-odds-value {
  display: block;
  font-size: 0.95rem;
  font-weight: 600;
  transition: opacity 0.2s;
}

.bet-odds-value:hover {
  opacity: 0.9;
}

.bet-odds-label {
  font-size: 0.7rem;
  color: #9e9e9e;
  margin-top: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bet-users {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  margin-top: 10px;
}

.bet-user-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
}

.bet-user {
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
  padding: 8px 15px;
  min-width: 100px;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  flex: 1;
}

.bet-user-left {
  align-items: flex-end;
  text-align: right;
}

.bet-user-right {
  align-items: flex-start;
  text-align: left;
}

.bet-username {
  font-size: 0.9rem;
  font-weight: 500;
  color: #ffffff;
  letter-spacing: 0.5px;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.bet-user-amount {
  font-size: 0.75rem;
  color: #00ff87;
  background: rgba(0, 255, 135, 0.1);
  border: 1px solid rgba(0, 255, 135, 0.3);
  border-radius: 4px;
  padding: 2px 6px;
  text-align: center;
}

.bet-amount {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.bet-amount span {
  font-size: 0.75rem;
  padding: 3px 8px;
  min-width: 70px;
  text-align: center;
  background: rgba(0, 255, 135, 0.1);
  color: #00ff87;
  border: 1px solid rgba(0, 255, 135, 0.3);
  border-radius: 4px;
  white-space: nowrap;
}

/* Removed the green dot indicator */

/* Media Queries for 13-14 inch screens */
@media (max-width: 1366px) {
    .challenge-card {
    grid-template-columns: minmax(200px, 1.5fr) minmax(180px, 1fr) auto 80px;
    padding: 20px;
    gap: 15px;
    }

    .teams-container {
  gap: 12px;
    padding: 0 8px;
    }

    .team {
    min-width: 100px;
    max-width: 150px;
    }

    .team-logo {
    width: 45px;
    height: 45px;
    }

    .team-name {
      font-size: 0.85rem;
    max-width: 80px;
}

.odds-container {
    padding: 10px;
  gap: 10px;
  }

  .odds-item {
    min-width: 50px;
    padding: 4px;
}

.odds-value {
  font-size: 0.9rem;
    padding: 6px 10px;
  }

  .recent-bets-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .recent-bet-card {
    padding: 15px;
  }

  .bet-amount span {
  font-size: 0.8rem;
    padding: 4px 8px;
    min-width: 70px;
  }
}

/* Media Queries for smaller screens */
@media (max-width: 1200px) {
  .recent-bets-grid {
    grid-template-columns: repeat(2, 1fr);
}

  .challenge-card {
    grid-template-columns: 1fr;
  gap: 15px;
  }

  .teams-container {
  justify-content: center;
}

.odds-container {
  justify-content: center;
  }

  .bet-amount span {
  text-align: center;
    min-width: 60px;
  }
}

@media (max-width: 768px) {
  .welcome__content {
    padding: 0 5px;
  }

  .section {
    padding: 10px 8px;
    margin-bottom: 10px;
  }

  .hero {
    height: 180px;
    margin-bottom: 12px;
  }

  .hero__nav {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }

  .hero__indicator {
    width: 6px;
    height: 6px;
  }

  .recent-bets-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .challenge-list-header {
    display: none;
  }

  /* Mobile Challenge Card */
  .challenge-card {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    padding: 12px 10px;
    gap: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  /* Mobile Match Container */
  .match-container {
    grid-row: 1;
    padding-left: 0;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }

  .live-match .match-container {
    border-left: none;
    padding-left: 0;
    position: relative;
  }

  .live-indicator {
    position: absolute;
    top: -12px;
    right: 0;
    left: auto;
  }

  /* Mobile Teams Display */
  .teams-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .team-logo {
    width: 32px;
    height: 32px;
  }

  .team-name {
    font-size: 0.8rem;
    max-width: 80px;
  }

  .vs-container {
    padding: 0 10px;
  }

  .vs-text {
    font-size: 0.8rem;
  }

  /* Mobile Match Details */
  .match-details {
    flex-direction: row;
    justify-content: space-between;
    padding: 0;
  }

  .match-type {
    font-size: 0.7rem;
    padding: 3px 6px;
  }

  .match-time {
    font-size: 0.7rem;
  }

  /* Mobile Odds Display */
  .odds-container {
    grid-row: 2;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 6px;
  }

  .odds-header {
    margin-bottom: 4px;
  }

  .odds-label {
    font-size: 0.7rem;
  }

  .odds-value {
    font-size: 0.9rem;
    padding: 8px;
  }

  /* Mobile Action Button */
  .action-container {
    grid-row: 3;
    padding: 0 20px;
  }

  .action-button {
    font-size: 0.8rem;
    padding: 10px 15px;
  }
}

/* Bet styles */
.bet-teams {
  gap: 5px;
  padding: 5px 0;
}

.bet-team-logo {
  width: 40px;
  height: 40px;
}

.bet-vs {
  font-size: 0.9rem;
  padding: 4px 8px;
  min-width: 35px;
}

.bet-vs-small {
  font-size: 0.75rem;
  padding: 3px 6px;
  min-width: 25px;
}

.bet-odds {
  padding: 6px;
}

.bet-odds-value {
  font-size: 0.9rem;
}

.bet-users {
  padding: 8px;
  margin-top: 8px;
}

.bet-user-container {
  gap: 8px;
}

.bet-user {
  padding: 6px 12px;
  min-width: 90px;
  background: transparent !important;
}

.bet-user-amount {
  font-size: 0.7rem;
  padding: 2px 5px;
}

.bet-user-left {
  align-items: flex-end;
  text-align: right;
}

.bet-user-right {
  align-items: flex-start;
  text-align: left;
}

.bet-header {
  flex-direction: column;
  align-items: center;
}

.bet-ref {
  text-align: center;
  width: auto;
}

.bet-amount {
  flex-direction: row;
  justify-content: center;
  gap: 10px;
  margin-top: 5px;
}

@media (max-width: 767px) {
  .challenge-card {
    padding: 15px;
  }

  .team {
    width: 100%;
  }

  .team-logo {
    width: 45px;
    height: 45px;
  }

  .vs-container {
    flex-direction: column;
    gap: 10px;
  }

  .match-type {
    width: 100%;
  }

  .status-container {
    flex-direction: column;
  }

  .bet-header {
    flex-direction: column;
    align-items: stretch;
  }

  .bet-ref {
    text-align: center;
  }

  .bet-amount {
    align-items: stretch;
  }

  .bet-amount span {
    text-align: center;
    min-width: auto;
    width: 100%;
  }

  .bet-users {
    flex-direction: column;
  }

  .bet-user {
    max-width: none;
  }

  .odds-container {
    padding: 12px;
  }

  .odds-item {
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .welcome__content {
    padding: 0 3px;
  }

  .section {
    padding: 12px 8px;
    margin-bottom: 12px;
  }

  .hero {
    height: 180px;
    margin-bottom: 12px;
  }

  .hero__nav {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }

  .hero__indicator {
    width: 6px;
    height: 6px;
  }

  .recent-bets-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .recent-bet-card {
    padding: 12px;
    gap: 10px;
  }

  .bet-header {
    padding-bottom: 8px;
    gap: 5px;
  }

  .bet-ref {
    font-size: 0.7rem;
    padding: 3px 6px;
  }

  .bet-amount span {
    font-size: 0.7rem;
    padding: 2px 6px;
    min-width: 60px;
  }

  .team-logo {
    width: 25px;
    height: 25px;
  }

  .team-name {
    font-size: 0.8rem;
  }

  .bet-team-logo {
    width: 36px;
    height: 36px;
    padding: 3px;
  }

  .bet-team-name {
    font-size: 0.75rem;
  }

  .bet-vs {
    font-size: 0.8rem;
    padding: 3px 6px;
    min-width: 30px;
  }

  .bet-vs-small {
    font-size: 0.7rem;
    padding: 2px 5px;
    min-width: 22px;
  }

  .bet-odds {
    padding: 5px;
  }

  .bet-odds-value {
    font-size: 0.8rem;
  }

  .bet-odds-label {
    font-size: 0.65rem;
  }

  .bet-users {
    padding: 6px;
    margin-top: 6px;
  }

  .bet-user-container {
    gap: 6px;
  }

  .bet-user {
    padding: 5px 10px;
    min-width: 80px;
    background: transparent !important;
  }

  .bet-username {
    font-size: 0.8rem;
  }

  .bet-user-amount {
    font-size: 0.65rem;
    padding: 1px 4px;
  }

  .bet-user-left {
    align-items: flex-end;
    text-align: right;
  }

  .bet-user-right {
    align-items: flex-start;
    text-align: left;
  }

  .bet-amount span {
    font-size: 0.7rem;
    padding: 2px 6px;
    min-width: 60px;
  }

  .vs-indicator {
    padding: 3px 6px;
    font-size: 0.8rem;
    min-width: 30px;
  }

  .match-type {
    font-size: 0.65rem;
    padding: 2px 6px;
  }

  .odds-value {
    font-size: 0.8rem;
    padding: 5px 8px;
  }

  .action-button {
    font-size: 0.7rem;
    padding: 6px 10px;
  }

  .status-badge {
    font-size: 0.7rem;
    padding: 3px 8px;
  }

  .countdown span {
    font-size: 0.7rem;
    padding: 2px 5px;
    min-width: 20px;
  }

  .teams-vs-container {
    gap: 3px;
  }

  .match-info {
    margin-top: 8px;
    gap: 8px;
  }
}

/* Empty State Styling */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: rgba(26, 27, 35, 0.5);
  border-radius: 12px;
  text-align: center;
  width: 100%;
  min-height: 200px;
}

.empty-state__image {
  max-width: 120px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.empty-state__message {
  font-size: 1.1rem;
  color: #fff;
  opacity: 0.8;
  max-width: 400px;
  margin: 0 auto;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: rgba(26, 27, 35, 0.5);
  border-radius: 12px;
  text-align: center;
  width: 100%;
  min-height: 200px;
  color: #fff;
  font-size: 1.1rem;
  opacity: 0.8;
}

/* Responsive Design Improvements */
@media (min-width: 1920px) {
    /* 24-inch and larger screens */
    .welcome__content {
        width: 100%;
        max-width: 100%;
        padding: 0 40px;
    }

    .challenge-card {
        padding: 30px;
        gap: 30px;
    }

    .team-logo {
        width: 80px;
        height: 80px;
    }

    .team-name {
        font-size: 1.1rem;
  }
}

/* New Bet Card Layout */
.bet-match-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 10px;
  padding: 10px 0;
}

.bet-team-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  flex: 1;
}

/* Update team name styles for better truncation */
.bet-team-column .bet-team-name {
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.85rem;
}

.bet-vs-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 0 5px;
}

.bet-user-simple {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  width: 100%;
  margin-top: 5px;
}

.bet-username-simple {
  font-size: 0.8rem;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5px;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Media Queries for New Layout */
@media (max-width: 768px) {
  .bet-match-container {
    gap: 5px;
  }

  .bet-team-column, .bet-vs-column {
    gap: 8px;
  }

  .bet-username-simple {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .bet-match-container {
    gap: 3px;
  }

  .bet-team-column, .bet-vs-column {
    gap: 6px;
  }

  .bet-username-simple {
    font-size: 0.8rem;
  }

  .bet-user-simple {
    gap: 3px;
    margin-top: 3px;
  }
}