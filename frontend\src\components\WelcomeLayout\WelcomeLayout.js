import React, { useState, useEffect } from 'react';
import axios from '../../utils/axiosConfig';
import { API_BASE_URL } from '../../config';
import WelcomeHeader from './WelcomeHeader';
import WelcomeSidebar from './WelcomeSidebar';
import WelcomeFooter from './WelcomeFooter';
import './WelcomeLayout.css';

const WelcomeLayout = ({ children }) => {
  const [leagues, setLeagues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    fetchTopLeagues();
  }, []);

  const fetchTopLeagues = async () => {
    try {
      setLoading(true);
      const response = await axios.get('get_leagues.php');
      if (response.data.success) {
        // Get top 7 leagues
        const topLeagues = response.data.leagues.slice(0, 7);
        setLeagues(topLeagues);
      } else {
        console.error('Failed to fetch leagues:', response.data.message);
        setMockLeagues();
      }
    } catch (error) {
      console.error('Error fetching leagues:', error);
      // Fallback: use mock data for demonstration
      setMockLeagues();
    } finally {
      setLoading(false);
    }
  };

  const setMockLeagues = () => {
    const mockLeagues = [
      {
        league_id: 1,
        name: 'Premier League',
        min_bet_amount: 1000,
        max_bet_amount: 10000,
        member_count: 150,
        icon_path: null
      },
      {
        league_id: 2,
        name: 'Champions League',
        min_bet_amount: 5000,
        max_bet_amount: 50000,
        member_count: 89,
        icon_path: null
      },
      {
        league_id: 3,
        name: 'Europa League',
        min_bet_amount: 2000,
        max_bet_amount: 20000,
        member_count: 67,
        icon_path: null
      }
    ];
    setLeagues(mockLeagues);
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="welcome-layout">
      <WelcomeHeader onToggleSidebar={toggleSidebar} />
      
      <div className="welcome-layout__body">
        <WelcomeSidebar 
          leagues={leagues}
          loading={loading}
          collapsed={sidebarCollapsed}
          onToggle={toggleSidebar}
        />
        
        <main className={`welcome-layout__main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
          <div className="welcome-layout__content">
            {children}
          </div>
        </main>
      </div>
      
      <WelcomeFooter />
    </div>
  );
};

export default WelcomeLayout;
