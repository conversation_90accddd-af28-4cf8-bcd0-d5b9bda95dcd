import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaBars, FaTimes, FaUser, FaSignInAlt, FaUserPlus } from 'react-icons/fa';
import './WelcomeHeader.css';

const WelcomeHeader = ({ onToggleSidebar }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userName, setUserName] = useState('');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    checkLoginStatus();
  }, []);

  const checkLoginStatus = () => {
    const userId = localStorage.getItem('userId');
    const storedUserName = localStorage.getItem('userName');
    setIsLoggedIn(!!userId);
    if (storedUserName) {
      setUserName(storedUserName);
    }
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleNavClick = () => {
    setMobileMenuOpen(false);
  };

  return (
    <header className="welcome-header">
      <div className="welcome-header__container">
        {/* Left Section - Logo and Sidebar Toggle */}
        <div className="welcome-header__left">
          <button 
            className="welcome-header__sidebar-toggle"
            onClick={onToggleSidebar}
            aria-label="Toggle sidebar"
          >
            <FaBars />
          </button>
          
          <Link to="/" className="welcome-header__logo">
            <span className="welcome-header__logo-text">FanBet247</span>
          </Link>
        </div>

        {/* Center Section - Navigation */}
        <nav className={`welcome-header__nav ${mobileMenuOpen ? 'mobile-open' : ''}`}>
          <Link to="/" className="welcome-header__nav-link" onClick={handleNavClick}>
            Home
          </Link>
          <Link to="/live-challenges" className="welcome-header__nav-link" onClick={handleNavClick}>
            Live
          </Link>
          <Link to={isLoggedIn ? "/user/leagues" : "/login"} className="welcome-header__nav-link" onClick={handleNavClick}>
            Leagues
          </Link>
          <Link to="/leaderboard" className="welcome-header__nav-link" onClick={handleNavClick}>
            Leaders
          </Link>
          <Link to="/about" className="welcome-header__nav-link" onClick={handleNavClick}>
            About
          </Link>
        </nav>

        {/* Right Section - User Actions */}
        <div className="welcome-header__right">
          {isLoggedIn ? (
            <div className="welcome-header__user">
              <FaUser className="welcome-header__user-icon" />
              <span className="welcome-header__username">{userName}</span>
              <Link to="/user/dashboard" className="welcome-header__dashboard-btn">
                Dashboard
              </Link>
            </div>
          ) : (
            <div className="welcome-header__auth">
              <Link to="/login" className="welcome-header__login-btn">
                <FaSignInAlt />
                <span>Login</span>
              </Link>
              <Link to="/register" className="welcome-header__register-btn">
                <FaUserPlus />
                <span>Register</span>
              </Link>
            </div>
          )}

          {/* Mobile Menu Toggle */}
          <button 
            className="welcome-header__mobile-toggle"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            {mobileMenuOpen ? <FaTimes /> : <FaBars />}
          </button>
        </div>
      </div>
    </header>
  );
};

export default WelcomeHeader;
