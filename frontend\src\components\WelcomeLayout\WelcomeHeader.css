/* Welcome Header Styles */
.welcome-header {
  background: #1a1b23;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 70px;
  display: flex;
  align-items: center;
}

.welcome-header__container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

/* Left Section */
.welcome-header__left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.welcome-header__sidebar-toggle {
  background: none;
  border: none;
  color: #00ff87;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.welcome-header__sidebar-toggle:hover {
  background: rgba(0, 255, 135, 0.1);
}

.welcome-header__logo {
  text-decoration: none;
  color: #fff;
}

.welcome-header__logo-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #00ff87;
}

/* Center Navigation */
.welcome-header__nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.welcome-header__nav-link {
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
}

.welcome-header__nav-link:hover {
  color: #00ff87;
  background: rgba(0, 255, 135, 0.1);
}

.welcome-header__nav-link.active {
  color: #00ff87;
}

/* Right Section */
.welcome-header__right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* User Section */
.welcome-header__user {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(0, 255, 135, 0.1);
  border-radius: 25px;
  border: 1px solid rgba(0, 255, 135, 0.2);
}

.welcome-header__user-icon {
  color: #00ff87;
  font-size: 1rem;
}

.welcome-header__username {
  color: #fff;
  font-weight: 500;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.welcome-header__dashboard-btn {
  color: #00ff87;
  text-decoration: none;
  font-size: 0.9rem;
  padding: 0.25rem 0.75rem;
  background: rgba(0, 255, 135, 0.2);
  border-radius: 15px;
  transition: background-color 0.2s ease;
}

.welcome-header__dashboard-btn:hover {
  background: rgba(0, 255, 135, 0.3);
}

/* Auth Buttons */
.welcome-header__auth {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.welcome-header__login-btn,
.welcome-header__register-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.welcome-header__login-btn {
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-header__login-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.welcome-header__register-btn {
  color: #fff;
  background: #00ff87;
  color: #000;
}

.welcome-header__register-btn:hover {
  background: #00e676;
}

/* Mobile Menu Toggle */
.welcome-header__mobile-toggle {
  display: none;
  background: none;
  border: none;
  color: #fff;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-header__container {
    padding: 0 1rem;
  }
  
  .welcome-header__nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #1a1b23;
    flex-direction: column;
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }
  
  .welcome-header__nav.mobile-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .welcome-header__nav-link {
    width: 100%;
    text-align: center;
    padding: 1rem;
  }
  
  .welcome-header__mobile-toggle {
    display: block;
  }
  
  .welcome-header__auth {
    display: none;
  }
  
  .welcome-header__user {
    padding: 0.25rem 0.5rem;
  }
  
  .welcome-header__username {
    display: none;
  }
}

@media (max-width: 480px) {
  .welcome-header__container {
    padding: 0 0.5rem;
  }
  
  .welcome-header__logo-text {
    font-size: 1.2rem;
  }
  
  .welcome-header__sidebar-toggle {
    font-size: 1rem;
    padding: 0.25rem;
  }
}
