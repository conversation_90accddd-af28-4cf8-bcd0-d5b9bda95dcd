import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from '../../utils/axiosConfig';
import { API_BASE_URL } from '../../config';
import { handleError, retryOperation } from '../../utils/errorHandler';
import HeroSlider from '../WelcomePage/HeroSlider';
import ChallengesList from '../WelcomePage/ChallengesList';
import RecentBets from '../WelcomePage/RecentBets';
import ErrorAlert from '../ErrorAlert';
import './WelcomeMainContent.css';

const WelcomeMainContent = () => {
  const [recentChallenges, setRecentChallenges] = useState([]);
  const [recentBets, setRecentBets] = useState([]);
  const [loading, setLoading] = useState({
    challenges: true,
    bets: true
  });
  const [error, setError] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userName, setUserName] = useState('');

  const sliderImages = [
    process.env.PUBLIC_URL + '/slider/Slider1.png',
    process.env.PUBLIC_URL + '/slider/Slider2.png'
  ];

  useEffect(() => {
    const fetchData = async () => {
      await Promise.all([
        fetchRecentChallenges(),
        fetchRecentBets()
      ]);
    };

    fetchData();

    const userId = localStorage.getItem('userId');
    setIsLoggedIn(!!userId);

    if (userId) {
      const storedUserName = localStorage.getItem('userName');
      if (storedUserName) {
        setUserName(storedUserName);
      } else {
        fetchUserName(userId);
      }
    }
  }, []);

  const fetchRecentChallenges = async () => {
    try {
      setLoading(prev => ({ ...prev, challenges: true }));
      const response = await retryOperation(
        () => axios.get(`${API_BASE_URL}/handlers/get_recent_challenges.php`),
        3,
        1000
      );

      if (response.data.success) {
        setRecentChallenges(response.data.challenges || []);
      } else {
        console.error('Failed to fetch challenges:', response.data.message);
      }
    } catch (error) {
      console.error('Error fetching recent challenges:', error);
      setError(handleError(error, 'Failed to load challenges'));
    } finally {
      setLoading(prev => ({ ...prev, challenges: false }));
    }
  };

  const fetchRecentBets = async () => {
    try {
      setLoading(prev => ({ ...prev, bets: true }));
      const response = await retryOperation(
        () => axios.get(`${API_BASE_URL}/handlers/get_recent_bets.php`),
        3,
        1000
      );

      if (response.data.success) {
        setRecentBets(response.data.bets || []);
      } else {
        console.error('Failed to fetch recent bets:', response.data.message);
      }
    } catch (error) {
      console.error('Error fetching recent bets:', error);
      setError(handleError(error, 'Failed to load recent bets'));
    } finally {
      setLoading(prev => ({ ...prev, bets: false }));
    }
  };

  const fetchUserName = async (userId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/handlers/get_user_profile.php?user_id=${userId}`);
      if (response.data.success && response.data.user) {
        const name = response.data.user.username || response.data.user.full_name || 'User';
        setUserName(name);
        localStorage.setItem('userName', name);
      }
    } catch (error) {
      console.error('Error fetching user name:', error);
    }
  };

  return (
    <div className="welcome-main-content">
      {error && <ErrorAlert error={error} onClose={() => setError(null)} />}

      {/* Hero Section */}
      <section className="welcome-main-content__hero">
        <HeroSlider sliderImages={sliderImages} />
      </section>

      {/* Live Challenges Section */}
      <section className="welcome-main-content__section">
        <div className="welcome-main-content__section-header">
          <h2 className="welcome-main-content__section-title">LIVE CHALLENGES</h2>
          <div className="welcome-main-content__section-actions">
            {isLoggedIn ? (
              <>
                <span className="welcome-main-content__user-welcome">Welcome, {userName}</span>
                <Link to="/user/dashboard" className="welcome-main-content__section-link">Dashboard</Link>
              </>
            ) : (
              <Link to="/login" className="welcome-main-content__section-link login-link">Login</Link>
            )}
          </div>
        </div>
        <ChallengesList
          recentChallenges={recentChallenges}
          loading={loading.challenges}
          isLoggedIn={isLoggedIn}
          API_BASE_URL={API_BASE_URL}
        />
      </section>

      {/* Recent Bets Section */}
      <section className="welcome-main-content__section">
        <div className="welcome-main-content__section-header">
          <h2 className="welcome-main-content__section-title">RECENT BETS</h2>
          <Link to="/user/recent-bets" className="welcome-main-content__section-link">View All</Link>
        </div>
        <RecentBets
          recentBets={recentBets}
          loading={loading.bets}
          API_BASE_URL={API_BASE_URL}
        />
      </section>
    </div>
  );
};

export default WelcomeMainContent;
