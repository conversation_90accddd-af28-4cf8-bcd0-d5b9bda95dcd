{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\WelcomeLayout\\\\WelcomeSidebar.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTrophy, Fa<PERSON>sers, FaChevronRight, FaSpinner } from 'react-icons/fa';\nimport { API_BASE_URL } from '../../config';\nimport './WelcomeSidebar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeSidebar = ({\n  leagues,\n  loading,\n  collapsed,\n  onToggle\n}) => {\n  const getLeagueIcon = league => {\n    if (league.icon_url) {\n      return `${API_BASE_URL}${league.icon_url}`;\n    } else if (league.league_icon) {\n      return `${API_BASE_URL}/uploads/leagues/icons/${league.league_icon}`;\n    } else if (league.icon_path) {\n      return `${API_BASE_URL}/uploads/leagues/icons/${league.icon_path}`;\n    }\n    return null;\n  };\n  const formatBetRange = (min, max) => {\n    const formatAmount = amount => {\n      if (amount >= 1000000) {\n        return `${(amount / 1000000).toFixed(1)}M`;\n      } else if (amount >= 1000) {\n        return `${(amount / 1000).toFixed(0)}K`;\n      }\n      return amount.toString();\n    };\n    return `${formatAmount(min)} - ${formatAmount(max)} FC`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    className: `welcome-sidebar ${collapsed ? 'collapsed' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-sidebar__header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-sidebar__title\",\n        children: [/*#__PURE__*/_jsxDEV(FaTrophy, {\n          className: \"welcome-sidebar__title-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Top Leagues\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 26\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-sidebar__content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-sidebar__loading\",\n        children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n          className: \"spinning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Loading leagues...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 28\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this) : leagues.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-sidebar__empty\",\n        children: !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"No leagues available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 28\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-sidebar__leagues\",\n        children: leagues.map(league => /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"welcome-sidebar__league-item\",\n          title: collapsed ? league.name : '',\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-sidebar__league-icon\",\n            children: [getLeagueIcon(league) ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: getLeagueIcon(league),\n              alt: `${league.name} icon`,\n              onError: e => {\n                e.target.style.display = 'none';\n                e.target.nextSibling.style.display = 'flex';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 21\n            }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"welcome-sidebar__league-fallback\",\n              children: /*#__PURE__*/_jsxDEV(FaTrophy, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 17\n          }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-sidebar__league-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"welcome-sidebar__league-name\",\n              children: league.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"welcome-sidebar__league-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"welcome-sidebar__league-range\",\n                children: formatBetRange(league.min_bet_amount, league.max_bet_amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"welcome-sidebar__league-members\",\n                children: [/*#__PURE__*/_jsxDEV(FaUsers, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: league.member_count || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 19\n          }, this), !collapsed && /*#__PURE__*/_jsxDEV(FaChevronRight, {\n            className: \"welcome-sidebar__league-arrow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 19\n          }, this)]\n        }, league.league_id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-sidebar__footer\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        className: \"welcome-sidebar__view-all\",\n        children: \"View All Leagues\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_c = WelcomeSidebar;\nexport default WelcomeSidebar;\nvar _c;\n$RefreshReg$(_c, \"WelcomeSidebar\");", "map": {"version": 3, "names": ["React", "Link", "FaTrophy", "FaUsers", "FaChevronRight", "FaSpinner", "API_BASE_URL", "jsxDEV", "_jsxDEV", "WelcomeSidebar", "leagues", "loading", "collapsed", "onToggle", "getLeagueIcon", "league", "icon_url", "league_icon", "icon_path", "formatBetRange", "min", "max", "formatAmount", "amount", "toFixed", "toString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "to", "title", "name", "src", "alt", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "min_bet_amount", "max_bet_amount", "member_count", "league_id", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/WelcomeLayout/WelcomeSidebar.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTrophy, FaUsers, FaChevronRight, FaSpinner } from 'react-icons/fa';\nimport { API_BASE_URL } from '../../config';\nimport './WelcomeSidebar.css';\n\nconst WelcomeSidebar = ({ leagues, loading, collapsed, onToggle }) => {\n  const getLeagueIcon = (league) => {\n    if (league.icon_url) {\n      return `${API_BASE_URL}${league.icon_url}`;\n    } else if (league.league_icon) {\n      return `${API_BASE_URL}/uploads/leagues/icons/${league.league_icon}`;\n    } else if (league.icon_path) {\n      return `${API_BASE_URL}/uploads/leagues/icons/${league.icon_path}`;\n    }\n    return null;\n  };\n\n  const formatBetRange = (min, max) => {\n    const formatAmount = (amount) => {\n      if (amount >= 1000000) {\n        return `${(amount / 1000000).toFixed(1)}M`;\n      } else if (amount >= 1000) {\n        return `${(amount / 1000).toFixed(0)}K`;\n      }\n      return amount.toString();\n    };\n    \n    return `${formatAmount(min)} - ${formatAmount(max)} FC`;\n  };\n\n  return (\n    <aside className={`welcome-sidebar ${collapsed ? 'collapsed' : ''}`}>\n      <div className=\"welcome-sidebar__header\">\n        <div className=\"welcome-sidebar__title\">\n          <FaTrophy className=\"welcome-sidebar__title-icon\" />\n          {!collapsed && <span>Top Leagues</span>}\n        </div>\n      </div>\n\n      <div className=\"welcome-sidebar__content\">\n        {loading ? (\n          <div className=\"welcome-sidebar__loading\">\n            <FaSpinner className=\"spinning\" />\n            {!collapsed && <span>Loading leagues...</span>}\n          </div>\n        ) : leagues.length === 0 ? (\n          <div className=\"welcome-sidebar__empty\">\n            {!collapsed && <span>No leagues available</span>}\n          </div>\n        ) : (\n          <div className=\"welcome-sidebar__leagues\">\n            {leagues.map((league) => (\n              <Link\n                key={league.league_id}\n                to=\"/login\"\n                className=\"welcome-sidebar__league-item\"\n                title={collapsed ? league.name : ''}\n              >\n                <div className=\"welcome-sidebar__league-icon\">\n                  {getLeagueIcon(league) ? (\n                    <img\n                      src={getLeagueIcon(league)}\n                      alt={`${league.name} icon`}\n                      onError={(e) => {\n                        e.target.style.display = 'none';\n                        e.target.nextSibling.style.display = 'flex';\n                      }}\n                    />\n                  ) : null}\n                  <div className=\"welcome-sidebar__league-fallback\">\n                    <FaTrophy />\n                  </div>\n                </div>\n\n                {!collapsed && (\n                  <div className=\"welcome-sidebar__league-info\">\n                    <h4 className=\"welcome-sidebar__league-name\">{league.name}</h4>\n                    <div className=\"welcome-sidebar__league-meta\">\n                      <span className=\"welcome-sidebar__league-range\">\n                        {formatBetRange(league.min_bet_amount, league.max_bet_amount)}\n                      </span>\n                      <div className=\"welcome-sidebar__league-members\">\n                        <FaUsers />\n                        <span>{league.member_count || 0}</span>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {!collapsed && (\n                  <FaChevronRight className=\"welcome-sidebar__league-arrow\" />\n                )}\n              </Link>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {!collapsed && (\n        <div className=\"welcome-sidebar__footer\">\n          <Link to=\"/login\" className=\"welcome-sidebar__view-all\">\n            View All Leagues\n          </Link>\n        </div>\n      )}\n    </aside>\n  );\n};\n\nexport default WelcomeSidebar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,cAAc,EAAEC,SAAS,QAAQ,gBAAgB;AAC7E,SAASC,YAAY,QAAQ,cAAc;AAC3C,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC,SAAS;EAAEC;AAAS,CAAC,KAAK;EACpE,MAAMC,aAAa,GAAIC,MAAM,IAAK;IAChC,IAAIA,MAAM,CAACC,QAAQ,EAAE;MACnB,OAAO,GAAGV,YAAY,GAAGS,MAAM,CAACC,QAAQ,EAAE;IAC5C,CAAC,MAAM,IAAID,MAAM,CAACE,WAAW,EAAE;MAC7B,OAAO,GAAGX,YAAY,0BAA0BS,MAAM,CAACE,WAAW,EAAE;IACtE,CAAC,MAAM,IAAIF,MAAM,CAACG,SAAS,EAAE;MAC3B,OAAO,GAAGZ,YAAY,0BAA0BS,MAAM,CAACG,SAAS,EAAE;IACpE;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,cAAc,GAAGA,CAACC,GAAG,EAAEC,GAAG,KAAK;IACnC,MAAMC,YAAY,GAAIC,MAAM,IAAK;MAC/B,IAAIA,MAAM,IAAI,OAAO,EAAE;QACrB,OAAO,GAAG,CAACA,MAAM,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;MAC5C,CAAC,MAAM,IAAID,MAAM,IAAI,IAAI,EAAE;QACzB,OAAO,GAAG,CAACA,MAAM,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;MACzC;MACA,OAAOD,MAAM,CAACE,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED,OAAO,GAAGH,YAAY,CAACF,GAAG,CAAC,MAAME,YAAY,CAACD,GAAG,CAAC,KAAK;EACzD,CAAC;EAED,oBACEb,OAAA;IAAOkB,SAAS,EAAE,mBAAmBd,SAAS,GAAG,WAAW,GAAG,EAAE,EAAG;IAAAe,QAAA,gBAClEnB,OAAA;MAAKkB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCnB,OAAA;QAAKkB,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCnB,OAAA,CAACN,QAAQ;UAACwB,SAAS,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACnD,CAACnB,SAAS,iBAAIJ,OAAA;UAAAmB,QAAA,EAAM;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvB,OAAA;MAAKkB,SAAS,EAAC,0BAA0B;MAAAC,QAAA,EACtChB,OAAO,gBACNH,OAAA;QAAKkB,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvCnB,OAAA,CAACH,SAAS;UAACqB,SAAS,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACjC,CAACnB,SAAS,iBAAIJ,OAAA;UAAAmB,QAAA,EAAM;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,GACJrB,OAAO,CAACsB,MAAM,KAAK,CAAC,gBACtBxB,OAAA;QAAKkB,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EACpC,CAACf,SAAS,iBAAIJ,OAAA;UAAAmB,QAAA,EAAM;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,gBAENvB,OAAA;QAAKkB,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EACtCjB,OAAO,CAACuB,GAAG,CAAElB,MAAM,iBAClBP,OAAA,CAACP,IAAI;UAEHiC,EAAE,EAAC,QAAQ;UACXR,SAAS,EAAC,8BAA8B;UACxCS,KAAK,EAAEvB,SAAS,GAAGG,MAAM,CAACqB,IAAI,GAAG,EAAG;UAAAT,QAAA,gBAEpCnB,OAAA;YAAKkB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,GAC1Cb,aAAa,CAACC,MAAM,CAAC,gBACpBP,OAAA;cACE6B,GAAG,EAAEvB,aAAa,CAACC,MAAM,CAAE;cAC3BuB,GAAG,EAAE,GAAGvB,MAAM,CAACqB,IAAI,OAAQ;cAC3BG,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;gBAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;cAC7C;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACA,IAAI,eACRvB,OAAA;cAAKkB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,eAC/CnB,OAAA,CAACN,QAAQ;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEL,CAACnB,SAAS,iBACTJ,OAAA;YAAKkB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3CnB,OAAA;cAAIkB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAEZ,MAAM,CAACqB;YAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/DvB,OAAA;cAAKkB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3CnB,OAAA;gBAAMkB,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAC5CR,cAAc,CAACJ,MAAM,CAAC8B,cAAc,EAAE9B,MAAM,CAAC+B,cAAc;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACPvB,OAAA;gBAAKkB,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CnB,OAAA,CAACL,OAAO;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXvB,OAAA;kBAAAmB,QAAA,EAAOZ,MAAM,CAACgC,YAAY,IAAI;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA,CAACnB,SAAS,iBACTJ,OAAA,CAACJ,cAAc;YAACsB,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC5D;QAAA,GAtCIhB,MAAM,CAACiC,SAAS;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuCjB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAACnB,SAAS,iBACTJ,OAAA;MAAKkB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCnB,OAAA,CAACP,IAAI;QAACiC,EAAE,EAAC,QAAQ;QAACR,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAExD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEZ,CAAC;AAACkB,EAAA,GAtGIxC,cAAc;AAwGpB,eAAeA,cAAc;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}