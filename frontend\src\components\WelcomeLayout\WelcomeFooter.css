/* Welcome Footer Styles */
.welcome-footer {
  background: #0f1015;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
  margin-top: auto;
}

.welcome-footer__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Main Footer Content */
.welcome-footer__main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

/* Brand Section */
.welcome-footer__brand {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.welcome-footer__logo h3 {
  color: #00ff87;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
}

.welcome-footer__tagline {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.welcome-footer__age {
  background: #ff6b35;
  color: #fff;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.8rem;
}

.welcome-footer__tagline p {
  margin: 0;
  color: #888;
  font-size: 0.9rem;
}

.welcome-footer__features {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #888;
  font-size: 0.9rem;
}

.welcome-footer__features span:nth-child(even) {
  color: #00ff87;
}

/* Links Section */
.welcome-footer__links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.welcome-footer__link-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.welcome-footer__link {
  color: #ccc;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.welcome-footer__link:hover {
  color: #00ff87;
}

/* Bottom Section */
.welcome-footer__bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  flex-wrap: wrap;
  gap: 1rem;
}

/* Social Links */
.welcome-footer__social {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.welcome-footer__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #ccc;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 1.1rem;
}

.welcome-footer__social-link:hover {
  background: #00ff87;
  color: #000;
  transform: translateY(-2px);
}

/* Security Badges */
.welcome-footer__security {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.welcome-footer__badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #00ff87;
  font-size: 0.85rem;
  font-weight: 500;
}

.welcome-footer__badge svg {
  font-size: 1rem;
}

/* Copyright */
.welcome-footer__copyright {
  color: #888;
  font-size: 0.85rem;
}

.welcome-footer__copyright p {
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-footer__container {
    padding: 1.5rem 1rem;
  }
  
  .welcome-footer__main {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
  }
  
  .welcome-footer__links {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .welcome-footer__bottom {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1.5rem;
  }
  
  .welcome-footer__security {
    order: -1;
  }
}

@media (max-width: 480px) {
  .welcome-footer__container {
    padding: 1rem 0.5rem;
  }
  
  .welcome-footer__tagline {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .welcome-footer__features {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .welcome-footer__social {
    gap: 0.75rem;
  }
  
  .welcome-footer__social-link {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .welcome-footer__security {
    flex-direction: column;
    gap: 1rem;
  }
}
