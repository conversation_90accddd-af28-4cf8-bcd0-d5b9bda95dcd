{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\SimpleSoccerHome.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport './SimpleSoccerHome.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleSoccerHome = () => {\n  _s();\n  const [data, setData] = useState({\n    leagues: [],\n    challenges: [],\n    recentBets: []\n  });\n  const [loading, setLoading] = useState({\n    leagues: true,\n    challenges: true,\n    bets: true\n  });\n\n  // Fetch top leagues\n  const fetchLeagues = async () => {\n    try {\n      const response = await axios.get('/handlers/get_leagues.php');\n      if (response.data.success && response.data.leagues) {\n        setData(prev => ({\n          ...prev,\n          leagues: response.data.leagues.slice(0, 6)\n        }));\n      }\n    } catch (err) {\n      console.error('Error fetching leagues:', err);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        leagues: false\n      }));\n    }\n  };\n\n  // Fetch live challenges\n  const fetchChallenges = async () => {\n    try {\n      const response = await axios.get('/handlers/recent_challenges.php');\n      if (response.data.success && response.data.challenges) {\n        setData(prev => ({\n          ...prev,\n          challenges: response.data.challenges.slice(0, 4)\n        }));\n      }\n    } catch (err) {\n      console.error('Error fetching challenges:', err);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        challenges: false\n      }));\n    }\n  };\n\n  // Fetch recent bets\n  const fetchRecentBets = async () => {\n    try {\n      const response = await axios.get('/handlers/welcome_recent_bets.php');\n      if (response.data.success && response.data.bets) {\n        setData(prev => ({\n          ...prev,\n          recentBets: response.data.bets.slice(0, 4)\n        }));\n      }\n    } catch (err) {\n      console.error('Error fetching recent bets:', err);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        bets: false\n      }));\n    }\n  };\n  useEffect(() => {\n    fetchLeagues();\n    fetchChallenges();\n    fetchRecentBets();\n  }, []);\n  const getTeamLogo = (teamName, logoPath = null) => {\n    // For now, use a placeholder image service since team logos are causing 404s\n    if (teamName) {\n      // Use a soccer-themed placeholder with team name\n      return `https://via.placeholder.com/40x40/00c851/ffffff?text=${teamName.charAt(0)}`;\n    }\n    return 'https://via.placeholder.com/40x40/cccccc/ffffff?text=⚽';\n  };\n  const formatCurrency = amount => {\n    return parseFloat(amount || 0).toLocaleString('en-US', {\n      minimumFractionDigits: 2\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"soccer-home\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"soccer-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"logo\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"FanBet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"nav-menu\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"nav-link active\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/live-challenges\",\n            className: \"nav-link\",\n            children: \"Live\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/leagues\",\n            className: \"nav-link\",\n            children: \"Leagues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/leaderboard\",\n            className: \"nav-link\",\n            children: \"Leaders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"nav-link\",\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"btn-login\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"btn-register\",\n            children: \"Register\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hero-title\",\n          children: [\"Welcome to \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"highlight\",\n            children: \"FanBet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 24\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hero-subtitle\",\n          children: \"The ultimate soccer betting platform for passionate fans\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"btn-primary\",\n            children: \"Get Started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/live-challenges\",\n            className: \"btn-secondary\",\n            children: \"View Live Matches\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Top Leagues\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/leagues\",\n              className: \"view-all\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"leagues-grid\",\n            children: loading.leagues ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading\",\n              children: \"Loading leagues...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this) : data.leagues.map((league, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"league-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"league-rank\",\n                children: [\"#\", index + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"league-icon\",\n                children: league.logo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `${API_BASE_URL}/uploads/leagues/${league.logo}`,\n                  alt: league.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"league-emoji\",\n                  children: \"\\u26BD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"league-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: league.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [league.member_count || 0, \" members\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"league-range\",\n                  children: [formatCurrency(league.min_bet), \" - \", formatCurrency(league.max_bet), \" FC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 21\n              }, this)]\n            }, league.league_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Live Challenges\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/live-challenges\",\n              className: \"view-all\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"challenges-grid\",\n            children: loading.challenges ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading\",\n              children: \"Loading challenges...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this) : data.challenges.map(challenge => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"challenge-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"match-teams\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"team\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(challenge.team_a, challenge.team_a_logo),\n                    alt: challenge.team_a,\n                    onError: e => e.target.src = '/default-team.png'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: challenge.team_a\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"vs\",\n                  children: \"VS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"team\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(challenge.team_b, challenge.team_b_logo),\n                    alt: challenge.team_b,\n                    onError: e => e.target.src = '/default-team.png'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: challenge.team_b\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"odds-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"odd\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Home\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"odd-value\",\n                    children: challenge.odds_team_a\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"odd\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Draw\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"odd-value\",\n                    children: challenge.odds_draw\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"odd\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Away\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"odd-value\",\n                    children: challenge.odds_team_b\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"bet-button\",\n                children: \"Login to Bet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this)]\n            }, challenge.challenge_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Bets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/recent-bets\",\n              className: \"view-all\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bets-list\",\n            children: loading.bets ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading\",\n              children: \"Loading recent bets...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this) : data.recentBets.map(bet => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bet-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bet-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bet-ref\",\n                  children: [\"REF: \", bet.unique_code || bet.bet_id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `bet-status ${bet.bet_status}`,\n                  children: bet.bet_status.toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this), bet.team_a && bet.team_b && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bet-match\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [bet.team_a, \" vs \", bet.team_b]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bet-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bet-user\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"username\",\n                    children: bet.user1_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"amount\",\n                    children: [formatCurrency(bet.amount_user1 || bet.amount), \" FC\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"choice\",\n                    children: bet.bet_choice_user1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"vs\",\n                  children: \"VS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bet-user\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"username\",\n                    children: bet.user2_name || 'Open'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"amount\",\n                    children: [bet.user2_name ? formatCurrency(bet.amount_user2 || bet.amount) : 'Waiting', \" FC\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"choice\",\n                    children: bet.bet_choice_user2 || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 21\n              }, this)]\n            }, bet.bet_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"soccer-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-brand\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"FanBet247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The ultimate soccer betting platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"18+ Bet Responsibly\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 18\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-column\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Quick Links\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/live-challenges\",\n                children: \"Live Matches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/leagues\",\n                children: \"All Leagues\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/leaderboard\",\n                children: \"Leaderboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-column\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/help\",\n                children: \"Help Center\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/terms\",\n                children: \"Terms\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/privacy\",\n                children: \"Privacy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-column\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Connect\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"https://twitter.com/fanbet247\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"Twitter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"https://facebook.com/fanbet247\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"Facebook\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"mailto:<EMAIL>\",\n                children: \"Support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\xA9 2024 FanBet247. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-badges\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2713 SSL Secured\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2713 Licensed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleSoccerHome, \"46N/USLZbZp1HqIxpoTlFdDw1ik=\");\n_c = SimpleSoccerHome;\nexport default SimpleSoccerHome;\nvar _c;\n$RefreshReg$(_c, \"SimpleSoccerHome\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "axios", "API_BASE_URL", "jsxDEV", "_jsxDEV", "SimpleSoccerHome", "_s", "data", "setData", "leagues", "challenges", "recentBets", "loading", "setLoading", "bets", "fetchLeagues", "response", "get", "success", "prev", "slice", "err", "console", "error", "fetchChallenges", "fetchRecentBets", "getTeamLogo", "teamName", "logoPath", "char<PERSON>t", "formatCurrency", "amount", "parseFloat", "toLocaleString", "minimumFractionDigits", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "league", "index", "logo", "src", "alt", "name", "member_count", "min_bet", "max_bet", "league_id", "challenge", "team_a", "team_a_logo", "onError", "e", "target", "team_b", "team_b_logo", "odds_team_a", "odds_draw", "odds_team_b", "challenge_id", "bet", "unique_code", "bet_id", "bet_status", "toUpperCase", "user1_name", "amount_user1", "bet_choice_user1", "user2_name", "amount_user2", "bet_choice_user2", "href", "rel", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/SimpleSoccerHome.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport './SimpleSoccerHome.css';\n\nconst SimpleSoccerHome = () => {\n  const [data, setData] = useState({\n    leagues: [],\n    challenges: [],\n    recentBets: []\n  });\n  const [loading, setLoading] = useState({\n    leagues: true,\n    challenges: true,\n    bets: true\n  });\n\n  // Fetch top leagues\n  const fetchLeagues = async () => {\n    try {\n      const response = await axios.get('/handlers/get_leagues.php');\n      if (response.data.success && response.data.leagues) {\n        setData(prev => ({ ...prev, leagues: response.data.leagues.slice(0, 6) }));\n      }\n    } catch (err) {\n      console.error('Error fetching leagues:', err);\n    } finally {\n      setLoading(prev => ({ ...prev, leagues: false }));\n    }\n  };\n\n  // Fetch live challenges\n  const fetchChallenges = async () => {\n    try {\n      const response = await axios.get('/handlers/recent_challenges.php');\n      if (response.data.success && response.data.challenges) {\n        setData(prev => ({ ...prev, challenges: response.data.challenges.slice(0, 4) }));\n      }\n    } catch (err) {\n      console.error('Error fetching challenges:', err);\n    } finally {\n      setLoading(prev => ({ ...prev, challenges: false }));\n    }\n  };\n\n  // Fetch recent bets\n  const fetchRecentBets = async () => {\n    try {\n      const response = await axios.get('/handlers/welcome_recent_bets.php');\n      if (response.data.success && response.data.bets) {\n        setData(prev => ({ ...prev, recentBets: response.data.bets.slice(0, 4) }));\n      }\n    } catch (err) {\n      console.error('Error fetching recent bets:', err);\n    } finally {\n      setLoading(prev => ({ ...prev, bets: false }));\n    }\n  };\n\n  useEffect(() => {\n    fetchLeagues();\n    fetchChallenges();\n    fetchRecentBets();\n  }, []);\n\n  const getTeamLogo = (teamName, logoPath = null) => {\n    // For now, use a placeholder image service since team logos are causing 404s\n    if (teamName) {\n      // Use a soccer-themed placeholder with team name\n      return `https://via.placeholder.com/40x40/00c851/ffffff?text=${teamName.charAt(0)}`;\n    }\n    return 'https://via.placeholder.com/40x40/cccccc/ffffff?text=⚽';\n  };\n\n  const formatCurrency = (amount) => {\n    return parseFloat(amount || 0).toLocaleString('en-US', { minimumFractionDigits: 2 });\n  };\n\n  return (\n    <div className=\"soccer-home\">\n      {/* Header */}\n      <header className=\"soccer-header\">\n        <div className=\"container\">\n          <Link to=\"/\" className=\"logo\">\n            <span className=\"logo-text\">FanBet247</span>\n          </Link>\n          <nav className=\"nav-menu\">\n            <Link to=\"/\" className=\"nav-link active\">Home</Link>\n            <Link to=\"/live-challenges\" className=\"nav-link\">Live</Link>\n            <Link to=\"/leagues\" className=\"nav-link\">Leagues</Link>\n            <Link to=\"/leaderboard\" className=\"nav-link\">Leaders</Link>\n            <Link to=\"/about\" className=\"nav-link\">About</Link>\n          </nav>\n          <div className=\"auth-buttons\">\n            <Link to=\"/login\" className=\"btn-login\">Login</Link>\n            <Link to=\"/register\" className=\"btn-register\">Register</Link>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"hero-section\">\n        <div className=\"hero-content\">\n          <h1 className=\"hero-title\">\n            Welcome to <span className=\"highlight\">FanBet247</span>\n          </h1>\n          <p className=\"hero-subtitle\">\n            The ultimate soccer betting platform for passionate fans\n          </p>\n          <div className=\"hero-actions\">\n            <Link to=\"/register\" className=\"btn-primary\">Get Started</Link>\n            <Link to=\"/live-challenges\" className=\"btn-secondary\">View Live Matches</Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        <div className=\"container\">\n          \n          {/* Top Leagues Section */}\n          <section className=\"section\">\n            <div className=\"section-header\">\n              <h2>Top Leagues</h2>\n              <Link to=\"/leagues\" className=\"view-all\">View All</Link>\n            </div>\n            <div className=\"leagues-grid\">\n              {loading.leagues ? (\n                <div className=\"loading\">Loading leagues...</div>\n              ) : (\n                data.leagues.map((league, index) => (\n                  <div key={league.league_id} className=\"league-card\">\n                    <div className=\"league-rank\">#{index + 1}</div>\n                    <div className=\"league-icon\">\n                      {league.logo ? (\n                        <img src={`${API_BASE_URL}/uploads/leagues/${league.logo}`} alt={league.name} />\n                      ) : (\n                        <span className=\"league-emoji\">⚽</span>\n                      )}\n                    </div>\n                    <div className=\"league-info\">\n                      <h3>{league.name}</h3>\n                      <p>{league.member_count || 0} members</p>\n                      <span className=\"league-range\">\n                        {formatCurrency(league.min_bet)} - {formatCurrency(league.max_bet)} FC\n                      </span>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </section>\n\n          {/* Live Challenges Section */}\n          <section className=\"section\">\n            <div className=\"section-header\">\n              <h2>Live Challenges</h2>\n              <Link to=\"/live-challenges\" className=\"view-all\">View All</Link>\n            </div>\n            <div className=\"challenges-grid\">\n              {loading.challenges ? (\n                <div className=\"loading\">Loading challenges...</div>\n              ) : (\n                data.challenges.map(challenge => (\n                  <div key={challenge.challenge_id} className=\"challenge-card\">\n                    <div className=\"match-teams\">\n                      <div className=\"team\">\n                        <img \n                          src={getTeamLogo(challenge.team_a, challenge.team_a_logo)} \n                          alt={challenge.team_a}\n                          onError={(e) => e.target.src = '/default-team.png'}\n                        />\n                        <span>{challenge.team_a}</span>\n                      </div>\n                      <div className=\"vs\">VS</div>\n                      <div className=\"team\">\n                        <img \n                          src={getTeamLogo(challenge.team_b, challenge.team_b_logo)} \n                          alt={challenge.team_b}\n                          onError={(e) => e.target.src = '/default-team.png'}\n                        />\n                        <span>{challenge.team_b}</span>\n                      </div>\n                    </div>\n                    <div className=\"odds-row\">\n                      <div className=\"odd\">\n                        <span>Home</span>\n                        <span className=\"odd-value\">{challenge.odds_team_a}</span>\n                      </div>\n                      <div className=\"odd\">\n                        <span>Draw</span>\n                        <span className=\"odd-value\">{challenge.odds_draw}</span>\n                      </div>\n                      <div className=\"odd\">\n                        <span>Away</span>\n                        <span className=\"odd-value\">{challenge.odds_team_b}</span>\n                      </div>\n                    </div>\n                    <Link to=\"/login\" className=\"bet-button\">Login to Bet</Link>\n                  </div>\n                ))\n              )}\n            </div>\n          </section>\n\n          {/* Recent Bets Section */}\n          <section className=\"section\">\n            <div className=\"section-header\">\n              <h2>Recent Bets</h2>\n              <Link to=\"/user/recent-bets\" className=\"view-all\">View All</Link>\n            </div>\n            <div className=\"bets-list\">\n              {loading.bets ? (\n                <div className=\"loading\">Loading recent bets...</div>\n              ) : (\n                data.recentBets.map(bet => (\n                  <div key={bet.bet_id} className=\"bet-card\">\n                    <div className=\"bet-header\">\n                      <span className=\"bet-ref\">REF: {bet.unique_code || bet.bet_id}</span>\n                      <span className={`bet-status ${bet.bet_status}`}>\n                        {bet.bet_status.toUpperCase()}\n                      </span>\n                    </div>\n                    {bet.team_a && bet.team_b && (\n                      <div className=\"bet-match\">\n                        <span>{bet.team_a} vs {bet.team_b}</span>\n                      </div>\n                    )}\n                    <div className=\"bet-details\">\n                      <div className=\"bet-user\">\n                        <span className=\"username\">{bet.user1_name}</span>\n                        <span className=\"amount\">{formatCurrency(bet.amount_user1 || bet.amount)} FC</span>\n                        <span className=\"choice\">{bet.bet_choice_user1}</span>\n                      </div>\n                      <div className=\"vs\">VS</div>\n                      <div className=\"bet-user\">\n                        <span className=\"username\">{bet.user2_name || 'Open'}</span>\n                        <span className=\"amount\">\n                          {bet.user2_name ? formatCurrency(bet.amount_user2 || bet.amount) : 'Waiting'} FC\n                        </span>\n                        <span className=\"choice\">{bet.bet_choice_user2 || 'N/A'}</span>\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </section>\n\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"soccer-footer\">\n        <div className=\"container\">\n          <div className=\"footer-content\">\n            <div className=\"footer-brand\">\n              <h3>FanBet247</h3>\n              <p>The ultimate soccer betting platform</p>\n              <p><strong>18+ Bet Responsibly</strong></p>\n            </div>\n            <div className=\"footer-links\">\n              <div className=\"footer-column\">\n                <h4>Quick Links</h4>\n                <Link to=\"/live-challenges\">Live Matches</Link>\n                <Link to=\"/leagues\">All Leagues</Link>\n                <Link to=\"/leaderboard\">Leaderboard</Link>\n              </div>\n              <div className=\"footer-column\">\n                <h4>Support</h4>\n                <Link to=\"/help\">Help Center</Link>\n                <Link to=\"/terms\">Terms</Link>\n                <Link to=\"/privacy\">Privacy</Link>\n              </div>\n              <div className=\"footer-column\">\n                <h4>Connect</h4>\n                <a href=\"https://twitter.com/fanbet247\" target=\"_blank\" rel=\"noopener noreferrer\">Twitter</a>\n                <a href=\"https://facebook.com/fanbet247\" target=\"_blank\" rel=\"noopener noreferrer\">Facebook</a>\n                <a href=\"mailto:<EMAIL>\">Support</a>\n              </div>\n            </div>\n          </div>\n          <div className=\"footer-bottom\">\n            <p>&copy; 2024 FanBet247. All rights reserved.</p>\n            <div className=\"footer-badges\">\n              <span>✓ SSL Secured</span>\n              <span>✓ Licensed</span>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default SimpleSoccerHome;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC;IAC/BW,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC;IACrCW,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBI,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMf,KAAK,CAACgB,GAAG,CAAC,2BAA2B,CAAC;MAC7D,IAAID,QAAQ,CAACT,IAAI,CAACW,OAAO,IAAIF,QAAQ,CAACT,IAAI,CAACE,OAAO,EAAE;QAClDD,OAAO,CAACW,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEV,OAAO,EAAEO,QAAQ,CAACT,IAAI,CAACE,OAAO,CAACW,KAAK,CAAC,CAAC,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC;MAC5E;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEF,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRR,UAAU,CAACM,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEV,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMf,KAAK,CAACgB,GAAG,CAAC,iCAAiC,CAAC;MACnE,IAAID,QAAQ,CAACT,IAAI,CAACW,OAAO,IAAIF,QAAQ,CAACT,IAAI,CAACG,UAAU,EAAE;QACrDF,OAAO,CAACW,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAET,UAAU,EAAEM,QAAQ,CAACT,IAAI,CAACG,UAAU,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC;MAClF;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;IAClD,CAAC,SAAS;MACRR,UAAU,CAACM,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAET,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMf,KAAK,CAACgB,GAAG,CAAC,mCAAmC,CAAC;MACrE,IAAID,QAAQ,CAACT,IAAI,CAACW,OAAO,IAAIF,QAAQ,CAACT,IAAI,CAACO,IAAI,EAAE;QAC/CN,OAAO,CAACW,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAER,UAAU,EAAEK,QAAQ,CAACT,IAAI,CAACO,IAAI,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC;MAC5E;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEF,GAAG,CAAC;IACnD,CAAC,SAAS;MACRR,UAAU,CAACM,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;EAEDf,SAAS,CAAC,MAAM;IACdgB,YAAY,CAAC,CAAC;IACdS,eAAe,CAAC,CAAC;IACjBC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,WAAW,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,GAAG,IAAI,KAAK;IACjD;IACA,IAAID,QAAQ,EAAE;MACZ;MACA,OAAO,wDAAwDA,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,EAAE;IACrF;IACA,OAAO,wDAAwD;EACjE,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAOC,UAAU,CAACD,MAAM,IAAI,CAAC,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;MAAEC,qBAAqB,EAAE;IAAE,CAAC,CAAC;EACtF,CAAC;EAED,oBACE9B,OAAA;IAAK+B,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAE1BhC,OAAA;MAAQ+B,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC/BhC,OAAA;QAAK+B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBhC,OAAA,CAACJ,IAAI;UAACqC,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,MAAM;UAAAC,QAAA,eAC3BhC,OAAA;YAAM+B,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACPrC,OAAA;UAAK+B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhC,OAAA,CAACJ,IAAI;YAACqC,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDrC,OAAA,CAACJ,IAAI;YAACqC,EAAE,EAAC,kBAAkB;YAACF,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5DrC,OAAA,CAACJ,IAAI;YAACqC,EAAE,EAAC,UAAU;YAACF,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvDrC,OAAA,CAACJ,IAAI;YAACqC,EAAE,EAAC,cAAc;YAACF,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DrC,OAAA,CAACJ,IAAI;YAACqC,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNrC,OAAA;UAAK+B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhC,OAAA,CAACJ,IAAI;YAACqC,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDrC,OAAA,CAACJ,IAAI;YAACqC,EAAE,EAAC,WAAW;YAACF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTrC,OAAA;MAAS+B,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC/BhC,OAAA;QAAK+B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhC,OAAA;UAAI+B,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,aACd,eAAAhC,OAAA;YAAM+B,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACLrC,OAAA;UAAG+B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrC,OAAA;UAAK+B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhC,OAAA,CAACJ,IAAI;YAACqC,EAAE,EAAC,WAAW;YAACF,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/DrC,OAAA,CAACJ,IAAI;YAACqC,EAAE,EAAC,kBAAkB;YAACF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVrC,OAAA;MAAM+B,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC5BhC,OAAA;QAAK+B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAGxBhC,OAAA;UAAS+B,SAAS,EAAC,SAAS;UAAAC,QAAA,gBAC1BhC,OAAA;YAAK+B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhC,OAAA;cAAAgC,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBrC,OAAA,CAACJ,IAAI;cAACqC,EAAE,EAAC,UAAU;cAACF,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNrC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BxB,OAAO,CAACH,OAAO,gBACdL,OAAA;cAAK+B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEjDlC,IAAI,CAACE,OAAO,CAACiC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC7BxC,OAAA;cAA4B+B,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACjDhC,OAAA;gBAAK+B,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAC,GAAC,EAACQ,KAAK,GAAG,CAAC;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CrC,OAAA;gBAAK+B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBO,MAAM,CAACE,IAAI,gBACVzC,OAAA;kBAAK0C,GAAG,EAAE,GAAG5C,YAAY,oBAAoByC,MAAM,CAACE,IAAI,EAAG;kBAACE,GAAG,EAAEJ,MAAM,CAACK;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEhFrC,OAAA;kBAAM+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACvC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNrC,OAAA;gBAAK+B,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhC,OAAA;kBAAAgC,QAAA,EAAKO,MAAM,CAACK;gBAAI;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtBrC,OAAA;kBAAAgC,QAAA,GAAIO,MAAM,CAACM,YAAY,IAAI,CAAC,EAAC,UAAQ;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACzCrC,OAAA;kBAAM+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAC3BN,cAAc,CAACa,MAAM,CAACO,OAAO,CAAC,EAAC,KAAG,EAACpB,cAAc,CAACa,MAAM,CAACQ,OAAO,CAAC,EAAC,KACrE;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAfEE,MAAM,CAACS,SAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBrB,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGVrC,OAAA;UAAS+B,SAAS,EAAC,SAAS;UAAAC,QAAA,gBAC1BhC,OAAA;YAAK+B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhC,OAAA;cAAAgC,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBrC,OAAA,CAACJ,IAAI;cAACqC,EAAE,EAAC,kBAAkB;cAACF,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNrC,OAAA;YAAK+B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BxB,OAAO,CAACF,UAAU,gBACjBN,OAAA;cAAK+B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAEpDlC,IAAI,CAACG,UAAU,CAACgC,GAAG,CAACW,SAAS,iBAC3BjD,OAAA;cAAkC+B,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC1DhC,OAAA;gBAAK+B,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhC,OAAA;kBAAK+B,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBhC,OAAA;oBACE0C,GAAG,EAAEpB,WAAW,CAAC2B,SAAS,CAACC,MAAM,EAAED,SAAS,CAACE,WAAW,CAAE;oBAC1DR,GAAG,EAAEM,SAAS,CAACC,MAAO;oBACtBE,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACZ,GAAG,GAAG;kBAAoB;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACFrC,OAAA;oBAAAgC,QAAA,EAAOiB,SAAS,CAACC;kBAAM;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACNrC,OAAA;kBAAK+B,SAAS,EAAC,IAAI;kBAAAC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5BrC,OAAA;kBAAK+B,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBhC,OAAA;oBACE0C,GAAG,EAAEpB,WAAW,CAAC2B,SAAS,CAACM,MAAM,EAAEN,SAAS,CAACO,WAAW,CAAE;oBAC1Db,GAAG,EAAEM,SAAS,CAACM,MAAO;oBACtBH,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACZ,GAAG,GAAG;kBAAoB;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACFrC,OAAA;oBAAAgC,QAAA,EAAOiB,SAAS,CAACM;kBAAM;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrC,OAAA;gBAAK+B,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBhC,OAAA;kBAAK+B,SAAS,EAAC,KAAK;kBAAAC,QAAA,gBAClBhC,OAAA;oBAAAgC,QAAA,EAAM;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBrC,OAAA;oBAAM+B,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEiB,SAAS,CAACQ;kBAAW;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACNrC,OAAA;kBAAK+B,SAAS,EAAC,KAAK;kBAAAC,QAAA,gBAClBhC,OAAA;oBAAAgC,QAAA,EAAM;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBrC,OAAA;oBAAM+B,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEiB,SAAS,CAACS;kBAAS;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNrC,OAAA;kBAAK+B,SAAS,EAAC,KAAK;kBAAAC,QAAA,gBAClBhC,OAAA;oBAAAgC,QAAA,EAAM;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBrC,OAAA;oBAAM+B,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEiB,SAAS,CAACU;kBAAW;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrC,OAAA,CAACJ,IAAI;gBAACqC,EAAE,EAAC,QAAQ;gBAACF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAlCpDY,SAAS,CAACW,YAAY;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmC3B,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGVrC,OAAA;UAAS+B,SAAS,EAAC,SAAS;UAAAC,QAAA,gBAC1BhC,OAAA;YAAK+B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhC,OAAA;cAAAgC,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBrC,OAAA,CAACJ,IAAI;cAACqC,EAAE,EAAC,mBAAmB;cAACF,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNrC,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBxB,OAAO,CAACE,IAAI,gBACXV,OAAA;cAAK+B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAErDlC,IAAI,CAACI,UAAU,CAAC+B,GAAG,CAACuB,GAAG,iBACrB7D,OAAA;cAAsB+B,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACxChC,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhC,OAAA;kBAAM+B,SAAS,EAAC,SAAS;kBAAAC,QAAA,GAAC,OAAK,EAAC6B,GAAG,CAACC,WAAW,IAAID,GAAG,CAACE,MAAM;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrErC,OAAA;kBAAM+B,SAAS,EAAE,cAAc8B,GAAG,CAACG,UAAU,EAAG;kBAAAhC,QAAA,EAC7C6B,GAAG,CAACG,UAAU,CAACC,WAAW,CAAC;gBAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EACLwB,GAAG,CAACX,MAAM,IAAIW,GAAG,CAACN,MAAM,iBACvBvD,OAAA;gBAAK+B,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBhC,OAAA;kBAAAgC,QAAA,GAAO6B,GAAG,CAACX,MAAM,EAAC,MAAI,EAACW,GAAG,CAACN,MAAM;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CACN,eACDrC,OAAA;gBAAK+B,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhC,OAAA;kBAAK+B,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBhC,OAAA;oBAAM+B,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAE6B,GAAG,CAACK;kBAAU;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClDrC,OAAA;oBAAM+B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,GAAEN,cAAc,CAACmC,GAAG,CAACM,YAAY,IAAIN,GAAG,CAAClC,MAAM,CAAC,EAAC,KAAG;kBAAA;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnFrC,OAAA;oBAAM+B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAE6B,GAAG,CAACO;kBAAgB;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACNrC,OAAA;kBAAK+B,SAAS,EAAC,IAAI;kBAAAC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5BrC,OAAA;kBAAK+B,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBhC,OAAA;oBAAM+B,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAE6B,GAAG,CAACQ,UAAU,IAAI;kBAAM;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5DrC,OAAA;oBAAM+B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,GACrB6B,GAAG,CAACQ,UAAU,GAAG3C,cAAc,CAACmC,GAAG,CAACS,YAAY,IAAIT,GAAG,CAAClC,MAAM,CAAC,GAAG,SAAS,EAAC,KAC/E;kBAAA;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPrC,OAAA;oBAAM+B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAE6B,GAAG,CAACU,gBAAgB,IAAI;kBAAK;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA1BEwB,GAAG,CAACE,MAAM;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2Bf,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPrC,OAAA;MAAQ+B,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC/BhC,OAAA;QAAK+B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBhC,OAAA;UAAK+B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhC,OAAA;cAAAgC,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBrC,OAAA;cAAAgC,QAAA,EAAG;YAAoC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3CrC,OAAA;cAAAgC,QAAA,eAAGhC,OAAA;gBAAAgC,QAAA,EAAQ;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNrC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhC,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BhC,OAAA;gBAAAgC,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBrC,OAAA,CAACJ,IAAI;gBAACqC,EAAE,EAAC,kBAAkB;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CrC,OAAA,CAACJ,IAAI;gBAACqC,EAAE,EAAC,UAAU;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCrC,OAAA,CAACJ,IAAI;gBAACqC,EAAE,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNrC,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BhC,OAAA;gBAAAgC,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBrC,OAAA,CAACJ,IAAI;gBAACqC,EAAE,EAAC,OAAO;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnCrC,OAAA,CAACJ,IAAI;gBAACqC,EAAE,EAAC,QAAQ;gBAAAD,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9BrC,OAAA,CAACJ,IAAI;gBAACqC,EAAE,EAAC,UAAU;gBAAAD,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACNrC,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BhC,OAAA;gBAAAgC,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBrC,OAAA;gBAAGwE,IAAI,EAAC,+BAA+B;gBAAClB,MAAM,EAAC,QAAQ;gBAACmB,GAAG,EAAC,qBAAqB;gBAAAzC,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7FrC,OAAA;gBAAGwE,IAAI,EAAC,gCAAgC;gBAAClB,MAAM,EAAC,QAAQ;gBAACmB,GAAG,EAAC,qBAAqB;gBAAAzC,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/FrC,OAAA;gBAAGwE,IAAI,EAAC,8BAA8B;gBAAAxC,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrC,OAAA;UAAK+B,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BhC,OAAA;YAAAgC,QAAA,EAAG;UAA2C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDrC,OAAA;YAAK+B,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BhC,OAAA;cAAAgC,QAAA,EAAM;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BrC,OAAA;cAAAgC,QAAA,EAAM;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnC,EAAA,CAhSID,gBAAgB;AAAAyE,EAAA,GAAhBzE,gBAAgB;AAkStB,eAAeA,gBAAgB;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}