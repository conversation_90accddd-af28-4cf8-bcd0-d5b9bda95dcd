{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\WelcomeLayout\\\\WelcomeHeader.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { FaBars, FaTimes, FaUser, FaSignInAlt, FaUserPlus } from 'react-icons/fa';\nimport './WelcomeHeader.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeHeader = ({\n  onToggleSidebar\n}) => {\n  _s();\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [userName, setUserName] = useState('');\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const navigate = useNavigate();\n  useEffect(() => {\n    checkLoginStatus();\n  }, []);\n  const checkLoginStatus = () => {\n    const userId = localStorage.getItem('userId');\n    const storedUserName = localStorage.getItem('userName');\n    setIsLoggedIn(!!userId);\n    if (storedUserName) {\n      setUserName(storedUserName);\n    }\n  };\n  const toggleMobileMenu = () => {\n    setMobileMenuOpen(!mobileMenuOpen);\n  };\n  const handleNavClick = () => {\n    setMobileMenuOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"welcome-header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-header__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-header__left\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"welcome-header__sidebar-toggle\",\n          onClick: onToggleSidebar,\n          \"aria-label\": \"Toggle sidebar\",\n          children: /*#__PURE__*/_jsxDEV(FaBars, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"welcome-header__logo\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"welcome-header__logo-text\",\n            children: \"FanBet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: `welcome-header__nav ${mobileMenuOpen ? 'mobile-open' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"welcome-header__nav-link\",\n          onClick: handleNavClick,\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/live-challenges\",\n          className: \"welcome-header__nav-link\",\n          onClick: handleNavClick,\n          children: \"Live\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: isLoggedIn ? \"/user/leagues\" : \"/login\",\n          className: \"welcome-header__nav-link\",\n          onClick: handleNavClick,\n          children: \"Leagues\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/leaderboard\",\n          className: \"welcome-header__nav-link\",\n          onClick: handleNavClick,\n          children: \"Leaders\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/about\",\n          className: \"welcome-header__nav-link\",\n          onClick: handleNavClick,\n          children: \"About\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-header__right\",\n        children: [isLoggedIn ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-header__user\",\n          children: [/*#__PURE__*/_jsxDEV(FaUser, {\n            className: \"welcome-header__user-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"welcome-header__username\",\n            children: userName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/user/dashboard\",\n            className: \"welcome-header__dashboard-btn\",\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-header__auth\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"welcome-header__login-btn\",\n            children: [/*#__PURE__*/_jsxDEV(FaSignInAlt, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"welcome-header__register-btn\",\n            children: [/*#__PURE__*/_jsxDEV(FaUserPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"welcome-header__mobile-toggle\",\n          onClick: toggleMobileMenu,\n          \"aria-label\": \"Toggle mobile menu\",\n          children: mobileMenuOpen ? /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(FaBars, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(WelcomeHeader, \"eES8mOcIrTRzcO03VnlBUQWhQZM=\", false, function () {\n  return [useNavigate];\n});\n_c = WelcomeHeader;\nexport default WelcomeHeader;\nvar _c;\n$RefreshReg$(_c, \"WelcomeHeader\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "FaBars", "FaTimes", "FaUser", "FaSignInAlt", "FaUserPlus", "jsxDEV", "_jsxDEV", "Welcome<PERSON><PERSON>er", "onToggleSidebar", "_s", "isLoggedIn", "setIsLoggedIn", "userName", "setUserName", "mobileMenuOpen", "setMobileMenuOpen", "navigate", "checkLoginStatus", "userId", "localStorage", "getItem", "storedUserName", "toggleMobileMenu", "handleNavClick", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/WelcomeLayout/WelcomeHeader.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { FaBars, FaTimes, FaUser, FaSignInAlt, FaUserPlus } from 'react-icons/fa';\nimport './WelcomeHeader.css';\n\nconst WelcomeHeader = ({ onToggleSidebar }) => {\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [userName, setUserName] = useState('');\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    checkLoginStatus();\n  }, []);\n\n  const checkLoginStatus = () => {\n    const userId = localStorage.getItem('userId');\n    const storedUserName = localStorage.getItem('userName');\n    setIsLoggedIn(!!userId);\n    if (storedUserName) {\n      setUserName(storedUserName);\n    }\n  };\n\n  const toggleMobileMenu = () => {\n    setMobileMenuOpen(!mobileMenuOpen);\n  };\n\n  const handleNavClick = () => {\n    setMobileMenuOpen(false);\n  };\n\n  return (\n    <header className=\"welcome-header\">\n      <div className=\"welcome-header__container\">\n        {/* Left Section - Logo and Sidebar Toggle */}\n        <div className=\"welcome-header__left\">\n          <button \n            className=\"welcome-header__sidebar-toggle\"\n            onClick={onToggleSidebar}\n            aria-label=\"Toggle sidebar\"\n          >\n            <FaBars />\n          </button>\n          \n          <Link to=\"/\" className=\"welcome-header__logo\">\n            <span className=\"welcome-header__logo-text\">FanBet247</span>\n          </Link>\n        </div>\n\n        {/* Center Section - Navigation */}\n        <nav className={`welcome-header__nav ${mobileMenuOpen ? 'mobile-open' : ''}`}>\n          <Link to=\"/\" className=\"welcome-header__nav-link\" onClick={handleNavClick}>\n            Home\n          </Link>\n          <Link to=\"/live-challenges\" className=\"welcome-header__nav-link\" onClick={handleNavClick}>\n            Live\n          </Link>\n          <Link to={isLoggedIn ? \"/user/leagues\" : \"/login\"} className=\"welcome-header__nav-link\" onClick={handleNavClick}>\n            Leagues\n          </Link>\n          <Link to=\"/leaderboard\" className=\"welcome-header__nav-link\" onClick={handleNavClick}>\n            Leaders\n          </Link>\n          <Link to=\"/about\" className=\"welcome-header__nav-link\" onClick={handleNavClick}>\n            About\n          </Link>\n        </nav>\n\n        {/* Right Section - User Actions */}\n        <div className=\"welcome-header__right\">\n          {isLoggedIn ? (\n            <div className=\"welcome-header__user\">\n              <FaUser className=\"welcome-header__user-icon\" />\n              <span className=\"welcome-header__username\">{userName}</span>\n              <Link to=\"/user/dashboard\" className=\"welcome-header__dashboard-btn\">\n                Dashboard\n              </Link>\n            </div>\n          ) : (\n            <div className=\"welcome-header__auth\">\n              <Link to=\"/login\" className=\"welcome-header__login-btn\">\n                <FaSignInAlt />\n                <span>Login</span>\n              </Link>\n              <Link to=\"/register\" className=\"welcome-header__register-btn\">\n                <FaUserPlus />\n                <span>Register</span>\n              </Link>\n            </div>\n          )}\n\n          {/* Mobile Menu Toggle */}\n          <button \n            className=\"welcome-header__mobile-toggle\"\n            onClick={toggleMobileMenu}\n            aria-label=\"Toggle mobile menu\"\n          >\n            {mobileMenuOpen ? <FaTimes /> : <FaBars />}\n          </button>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default WelcomeHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AACjF,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMoB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACdoB,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACvDT,aAAa,CAAC,CAAC,CAACO,MAAM,CAAC;IACvB,IAAIG,cAAc,EAAE;MAClBR,WAAW,CAACQ,cAAc,CAAC;IAC7B;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BP,iBAAiB,CAAC,CAACD,cAAc,CAAC;EACpC,CAAC;EAED,MAAMS,cAAc,GAAGA,CAAA,KAAM;IAC3BR,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,oBACET,OAAA;IAAQkB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAChCnB,OAAA;MAAKkB,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBAExCnB,OAAA;QAAKkB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCnB,OAAA;UACEkB,SAAS,EAAC,gCAAgC;UAC1CE,OAAO,EAAElB,eAAgB;UACzB,cAAW,gBAAgB;UAAAiB,QAAA,eAE3BnB,OAAA,CAACN,MAAM;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAETxB,OAAA,CAACR,IAAI;UAACiC,EAAE,EAAC,GAAG;UAACP,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eAC3CnB,OAAA;YAAMkB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNxB,OAAA;QAAKkB,SAAS,EAAE,uBAAuBV,cAAc,GAAG,aAAa,GAAG,EAAE,EAAG;QAAAW,QAAA,gBAC3EnB,OAAA,CAACR,IAAI;UAACiC,EAAE,EAAC,GAAG;UAACP,SAAS,EAAC,0BAA0B;UAACE,OAAO,EAAEH,cAAe;UAAAE,QAAA,EAAC;QAE3E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPxB,OAAA,CAACR,IAAI;UAACiC,EAAE,EAAC,kBAAkB;UAACP,SAAS,EAAC,0BAA0B;UAACE,OAAO,EAAEH,cAAe;UAAAE,QAAA,EAAC;QAE1F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPxB,OAAA,CAACR,IAAI;UAACiC,EAAE,EAAErB,UAAU,GAAG,eAAe,GAAG,QAAS;UAACc,SAAS,EAAC,0BAA0B;UAACE,OAAO,EAAEH,cAAe;UAAAE,QAAA,EAAC;QAEjH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPxB,OAAA,CAACR,IAAI;UAACiC,EAAE,EAAC,cAAc;UAACP,SAAS,EAAC,0BAA0B;UAACE,OAAO,EAAEH,cAAe;UAAAE,QAAA,EAAC;QAEtF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPxB,OAAA,CAACR,IAAI;UAACiC,EAAE,EAAC,QAAQ;UAACP,SAAS,EAAC,0BAA0B;UAACE,OAAO,EAAEH,cAAe;UAAAE,QAAA,EAAC;QAEhF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNxB,OAAA;QAAKkB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GACnCf,UAAU,gBACTJ,OAAA;UAAKkB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCnB,OAAA,CAACJ,MAAM;YAACsB,SAAS,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDxB,OAAA;YAAMkB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAEb;UAAQ;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5DxB,OAAA,CAACR,IAAI;YAACiC,EAAE,EAAC,iBAAiB;YAACP,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAErE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAENxB,OAAA;UAAKkB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCnB,OAAA,CAACR,IAAI;YAACiC,EAAE,EAAC,QAAQ;YAACP,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACrDnB,OAAA,CAACH,WAAW;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACfxB,OAAA;cAAAmB,QAAA,EAAM;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACPxB,OAAA,CAACR,IAAI;YAACiC,EAAE,EAAC,WAAW;YAACP,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3DnB,OAAA,CAACF,UAAU;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACdxB,OAAA;cAAAmB,QAAA,EAAM;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAGDxB,OAAA;UACEkB,SAAS,EAAC,+BAA+B;UACzCE,OAAO,EAAEJ,gBAAiB;UAC1B,cAAW,oBAAoB;UAAAG,QAAA,EAE9BX,cAAc,gBAAGR,OAAA,CAACL,OAAO;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGxB,OAAA,CAACN,MAAM;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACrB,EAAA,CAnGIF,aAAa;EAAA,QAIAR,WAAW;AAAA;AAAAiC,EAAA,GAJxBzB,aAAa;AAqGnB,eAAeA,aAAa;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}