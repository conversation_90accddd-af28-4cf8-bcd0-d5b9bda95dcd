{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserRegistration.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useCurrency } from '../contexts/CurrencyContext';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport { userService, currencyService, apiService } from '../services';\nimport useApiService from '../hooks/useApiService';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction UserRegistration() {\n  _s();\n  const [teams, setTeams] = useState([]);\n  const [newUser, setNewUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    password: '',\n    favorite_team: '',\n    preferred_currency_id: '' // Require user selection\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  // Currency system integration\n  const {\n    currencies,\n    loading: currenciesLoading,\n    error: currencyError\n  } = useCurrency();\n  const {\n    execute\n  } = useApiService();\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get('/handlers/team_management.php');\n      console.log('Teams API response:', response.data); // Debug log\n      if (response.data && response.data.data) {\n        setTeams(response.data.data);\n      } else if (response.data && Array.isArray(response.data)) {\n        setTeams(response.data);\n      } else {\n        console.error('Unexpected teams response format:', response.data);\n        setError('Failed to fetch teams - unexpected response format');\n      }\n    } catch (err) {\n      console.error('Error fetching teams:', err);\n      setError('Failed to fetch teams');\n    }\n  };\n\n  // Handle currency error from context\n  useEffect(() => {\n    if (currencyError) {\n      setError('Failed to load currencies. Please refresh the page.');\n    }\n  }, [currencyError]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewUser(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n\n    // Validate currency selection\n    if (!newUser.preferred_currency_id) {\n      setError('Please select your preferred currency');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await axios.post('/handlers/user_registration.php', newUser);\n      console.log('Registration API response:', response.data); // Debug log\n\n      if (response.data && response.data.success) {\n        const selectedCurrency = currencies.find(c => c.id === parseInt(newUser.preferred_currency_id));\n        setSuccess(`Registration successful! Your preferred currency is set to ${(selectedCurrency === null || selectedCurrency === void 0 ? void 0 : selectedCurrency.currency_name) || (selectedCurrency === null || selectedCurrency === void 0 ? void 0 : selectedCurrency.currency_code) || 'USD'}. Please check your email for verification instructions.`);\n        setTimeout(() => navigate('/login'), 3000);\n      } else {\n        var _response$data;\n        setError(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || 'Registration failed');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Registration error:', err);\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message || 'Registration failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n    title: \"Create Account\",\n    subtitle: \"Join the FanBet247 community today\",\n    variant: \"registration\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"user-auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"username\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"username\",\n            type: \"text\",\n            name: \"username\",\n            value: newUser.username,\n            onChange: handleInputChange,\n            placeholder: \"Choose a unique username\",\n            required: true,\n            minLength: \"3\",\n            maxLength: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"full_name\",\n          children: \"Full Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"full_name\",\n            type: \"text\",\n            name: \"full_name\",\n            value: newUser.full_name,\n            onChange: handleInputChange,\n            placeholder: \"Enter your full name\",\n            required: true,\n            minLength: \"2\",\n            maxLength: \"50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-id-card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"Email Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"email\",\n            type: \"email\",\n            name: \"email\",\n            value: newUser.email,\n            onChange: handleInputChange,\n            placeholder: \"Enter your email address\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-envelope\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"password\",\n            type: \"password\",\n            name: \"password\",\n            value: newUser.password,\n            onChange: handleInputChange,\n            placeholder: \"Create a strong password\",\n            required: true,\n            minLength: \"6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-lock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"form-help-text\",\n          children: \"Password must be at least 6 characters long\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"favorite_team\",\n          children: \"Favorite Team\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"favorite_team\",\n            name: \"favorite_team\",\n            value: newUser.favorite_team,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select your favorite team\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 33\n            }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: team.name,\n              children: team.name\n            }, team.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 37\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-futbol\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"preferred_currency_id\",\n          children: \"Preferred Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"preferred_currency_id\",\n            name: \"preferred_currency_id\",\n            value: newUser.preferred_currency_id,\n            onChange: handleInputChange,\n            required: true,\n            disabled: currenciesLoading,\n            children: currenciesLoading ? /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Loading currencies...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 37\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select your preferred currency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 41\n              }, this), currencies.map(currency => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: currency.id,\n                children: [currency.currency_code, \" - \", currency.currency_name, \" (\", currency.currency_symbol, \")\"]\n              }, currency.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 45\n              }, this)), currencies.length === 0 && /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"1\",\n                children: \"USD - US Dollar (Default)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-coins\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"form-help-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Required:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 29\n          }, this), \" This will be used to display FanCoin amounts in your local currency. You can change this later in your profile settings.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 21\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 27\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-success-message\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 29\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"user-auth-button\",\n        disabled: loading || currenciesLoading,\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-auth-loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 29\n          }, this), \"Creating Account...\"]\n        }, void 0, true) : 'Create Account'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"user-auth-link\",\n            children: \"Sign in here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 49\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 9\n  }, this);\n}\n_s(UserRegistration, \"qpmY8uF3VoyFM7vqdpkxUNJUArI=\", false, function () {\n  return [useNavigate, useCurrency, useApiService];\n});\n_c = UserRegistration;\nexport default UserRegistration;\nvar _c;\n$RefreshReg$(_c, \"UserRegistration\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Link", "useCurrency", "UserAuthLayout", "userService", "currencyService", "apiService", "useApiService", "axios", "API_BASE_URL", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserRegistration", "_s", "teams", "setTeams", "newUser", "setNewUser", "username", "full_name", "email", "password", "favorite_team", "preferred_currency_id", "error", "setError", "success", "setSuccess", "loading", "setLoading", "navigate", "currencies", "currenciesLoading", "currencyError", "execute", "fetchTeams", "response", "get", "console", "log", "data", "Array", "isArray", "err", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "post", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "c", "id", "parseInt", "currency_name", "currency_code", "setTimeout", "_response$data", "message", "_err$response", "_err$response$data", "title", "subtitle", "variant", "children", "onSubmit", "className", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "placeholder", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "map", "team", "disabled", "currency", "currency_symbol", "length", "to", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserRegistration.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useCurrency } from '../contexts/CurrencyContext';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport { userService, currencyService, apiService } from '../services';\nimport useApiService from '../hooks/useApiService';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\n\nfunction UserRegistration() {\n    const [teams, setTeams] = useState([]);\n    const [newUser, setNewUser] = useState({\n        username: '',\n        full_name: '',\n        email: '',\n        password: '',\n        favorite_team: '',\n        preferred_currency_id: '' // Require user selection\n    });\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [loading, setLoading] = useState(false);\n    const navigate = useNavigate();\n\n    // Currency system integration\n    const { currencies, loading: currenciesLoading, error: currencyError } = useCurrency();\n    const { execute } = useApiService();\n\n    useEffect(() => {\n        fetchTeams();\n    }, []);\n\n    const fetchTeams = async () => {\n        try {\n            const response = await axios.get('/handlers/team_management.php');\n            console.log('Teams API response:', response.data); // Debug log\n            if (response.data && response.data.data) {\n                setTeams(response.data.data);\n            } else if (response.data && Array.isArray(response.data)) {\n                setTeams(response.data);\n            } else {\n                console.error('Unexpected teams response format:', response.data);\n                setError('Failed to fetch teams - unexpected response format');\n            }\n        } catch (err) {\n            console.error('Error fetching teams:', err);\n            setError('Failed to fetch teams');\n        }\n    };\n\n    // Handle currency error from context\n    useEffect(() => {\n        if (currencyError) {\n            setError('Failed to load currencies. Please refresh the page.');\n        }\n    }, [currencyError]);\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setNewUser(prev => ({ ...prev, [name]: value }));\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setSuccess('');\n\n        // Validate currency selection\n        if (!newUser.preferred_currency_id) {\n            setError('Please select your preferred currency');\n            return;\n        }\n\n        setLoading(true);\n\n        try {\n            const response = await axios.post('/handlers/user_registration.php', newUser);\n            console.log('Registration API response:', response.data); // Debug log\n\n            if (response.data && response.data.success) {\n                const selectedCurrency = currencies.find(c => c.id === parseInt(newUser.preferred_currency_id));\n                setSuccess(\n                    `Registration successful! Your preferred currency is set to ${selectedCurrency?.currency_name || selectedCurrency?.currency_code || 'USD'}. Please check your email for verification instructions.`\n                );\n                setTimeout(() => navigate('/login'), 3000);\n            } else {\n                setError(response.data?.message || 'Registration failed');\n            }\n        } catch (err) {\n            console.error('Registration error:', err);\n            setError(err.response?.data?.message || err.message || 'Registration failed');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    return (\n        <UserAuthLayout\n            title=\"Create Account\"\n            subtitle=\"Join the FanBet247 community today\"\n            variant=\"registration\"\n        >\n            <form onSubmit={handleSubmit} className=\"user-auth-form\">\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"username\">Username</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"username\"\n                                type=\"text\"\n                                name=\"username\"\n                                value={newUser.username}\n                                onChange={handleInputChange}\n                                placeholder=\"Choose a unique username\"\n                                required\n                                minLength=\"3\"\n                                maxLength=\"20\"\n                            />\n                            <i className=\"fas fa-user\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"full_name\">Full Name</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"full_name\"\n                                type=\"text\"\n                                name=\"full_name\"\n                                value={newUser.full_name}\n                                onChange={handleInputChange}\n                                placeholder=\"Enter your full name\"\n                                required\n                                minLength=\"2\"\n                                maxLength=\"50\"\n                            />\n                            <i className=\"fas fa-id-card\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"email\">Email Address</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"email\"\n                                type=\"email\"\n                                name=\"email\"\n                                value={newUser.email}\n                                onChange={handleInputChange}\n                                placeholder=\"Enter your email address\"\n                                required\n                            />\n                            <i className=\"fas fa-envelope\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"password\">Password</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"password\"\n                                type=\"password\"\n                                name=\"password\"\n                                value={newUser.password}\n                                onChange={handleInputChange}\n                                placeholder=\"Create a strong password\"\n                                required\n                                minLength=\"6\"\n                            />\n                            <i className=\"fas fa-lock\"></i>\n                        </div>\n                        <small className=\"form-help-text\">\n                            Password must be at least 6 characters long\n                        </small>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"favorite_team\">Favorite Team</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <select\n                                id=\"favorite_team\"\n                                name=\"favorite_team\"\n                                value={newUser.favorite_team}\n                                onChange={handleInputChange}\n                                required\n                            >\n                                <option value=\"\">Select your favorite team</option>\n                                {teams.map(team => (\n                                    <option key={team.id} value={team.name}>{team.name}</option>\n                                ))}\n                            </select>\n                            <i className=\"fas fa-futbol\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"preferred_currency_id\">Preferred Currency</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <select\n                                id=\"preferred_currency_id\"\n                                name=\"preferred_currency_id\"\n                                value={newUser.preferred_currency_id}\n                                onChange={handleInputChange}\n                                required\n                                disabled={currenciesLoading}\n                            >\n                                {currenciesLoading ? (\n                                    <option value=\"\">Loading currencies...</option>\n                                ) : (\n                                    <>\n                                        <option value=\"\">Select your preferred currency</option>\n                                        {currencies.map(currency => (\n                                            <option key={currency.id} value={currency.id}>\n                                                {currency.currency_code} - {currency.currency_name} ({currency.currency_symbol})\n                                            </option>\n                                        ))}\n                                        {currencies.length === 0 && (\n                                            <option value=\"1\">USD - US Dollar (Default)</option>\n                                        )}\n                                    </>\n                                )}\n                            </select>\n                            <i className=\"fas fa-coins\"></i>\n                        </div>\n                        <small className=\"form-help-text\">\n                            <strong>Required:</strong> This will be used to display FanCoin amounts in your local currency.\n                            You can change this later in your profile settings.\n                        </small>\n                    </div>\n\n                {error && <div className=\"user-auth-error-message\">{error}</div>}\n                {success && <div className=\"user-auth-success-message\">{success}</div>}\n\n                <button\n                    type=\"submit\"\n                    className=\"user-auth-button\"\n                    disabled={loading || currenciesLoading}\n                >\n                    {loading ? (\n                        <>\n                            <span className=\"user-auth-loading-spinner\"></span>\n                            Creating Account...\n                        </>\n                    ) : (\n                        'Create Account'\n                    )}\n                </button>\n\n                <div className=\"user-auth-footer\">\n                    <p>Already have an account? <Link to=\"/login\" className=\"user-auth-link\">Sign in here</Link></p>\n                </div>\n            </form>\n        </UserAuthLayout>\n    );\n}\n\nexport default UserRegistration;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAASC,WAAW,EAAEC,eAAe,EAAEC,UAAU,QAAQ,aAAa;AACtE,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,wBAAwB;AAC/B,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC;IACnCsB,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,qBAAqB,EAAE,EAAE,CAAC;EAC9B,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMkC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM;IAAEiC,UAAU;IAAEH,OAAO,EAAEI,iBAAiB;IAAER,KAAK,EAAES;EAAc,CAAC,GAAGjC,WAAW,CAAC,CAAC;EACtF,MAAM;IAAEkC;EAAQ,CAAC,GAAG7B,aAAa,CAAC,CAAC;EAEnCR,SAAS,CAAC,MAAM;IACZsC,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAM9B,KAAK,CAAC+B,GAAG,CAAC,+BAA+B,CAAC;MACjEC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC;MACnD,IAAIJ,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACA,IAAI,EAAE;QACrCzB,QAAQ,CAACqB,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MAChC,CAAC,MAAM,IAAIJ,QAAQ,CAACI,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACI,IAAI,CAAC,EAAE;QACtDzB,QAAQ,CAACqB,QAAQ,CAACI,IAAI,CAAC;MAC3B,CAAC,MAAM;QACHF,OAAO,CAACd,KAAK,CAAC,mCAAmC,EAAEY,QAAQ,CAACI,IAAI,CAAC;QACjEf,QAAQ,CAAC,oDAAoD,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACVL,OAAO,CAACd,KAAK,CAAC,uBAAuB,EAAEmB,GAAG,CAAC;MAC3ClB,QAAQ,CAAC,uBAAuB,CAAC;IACrC;EACJ,CAAC;;EAED;EACA5B,SAAS,CAAC,MAAM;IACZ,IAAIoC,aAAa,EAAE;MACfR,QAAQ,CAAC,qDAAqD,CAAC;IACnE;EACJ,CAAC,EAAE,CAACQ,aAAa,CAAC,CAAC;EAEnB,MAAMW,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC/B,UAAU,CAACgC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOL,CAAC,IAAK;IAC9BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClB1B,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;;IAEd;IACA,IAAI,CAACX,OAAO,CAACO,qBAAqB,EAAE;MAChCE,QAAQ,CAAC,uCAAuC,CAAC;MACjD;IACJ;IAEAI,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACA,MAAMO,QAAQ,GAAG,MAAM9B,KAAK,CAAC8C,IAAI,CAAC,iCAAiC,EAAEpC,OAAO,CAAC;MAC7EsB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC;;MAE1D,IAAIJ,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACd,OAAO,EAAE;QACxC,MAAM2B,gBAAgB,GAAGtB,UAAU,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAACzC,OAAO,CAACO,qBAAqB,CAAC,CAAC;QAC/FI,UAAU,CACN,8DAA8D,CAAA0B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEK,aAAa,MAAIL,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEM,aAAa,KAAI,KAAK,0DAC7I,CAAC;QACDC,UAAU,CAAC,MAAM9B,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;MAC9C,CAAC,MAAM;QAAA,IAAA+B,cAAA;QACHpC,QAAQ,CAAC,EAAAoC,cAAA,GAAAzB,QAAQ,CAACI,IAAI,cAAAqB,cAAA,uBAAbA,cAAA,CAAeC,OAAO,KAAI,qBAAqB,CAAC;MAC7D;IACJ,CAAC,CAAC,OAAOnB,GAAG,EAAE;MAAA,IAAAoB,aAAA,EAAAC,kBAAA;MACV1B,OAAO,CAACd,KAAK,CAAC,qBAAqB,EAAEmB,GAAG,CAAC;MACzClB,QAAQ,CAAC,EAAAsC,aAAA,GAAApB,GAAG,CAACP,QAAQ,cAAA2B,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcvB,IAAI,cAAAwB,kBAAA,uBAAlBA,kBAAA,CAAoBF,OAAO,KAAInB,GAAG,CAACmB,OAAO,IAAI,qBAAqB,CAAC;IACjF,CAAC,SAAS;MACNjC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,oBACIpB,OAAA,CAACR,cAAc;IACXgE,KAAK,EAAC,gBAAgB;IACtBC,QAAQ,EAAC,oCAAoC;IAC7CC,OAAO,EAAC,cAAc;IAAAC,QAAA,eAEtB3D,OAAA;MAAM4D,QAAQ,EAAEnB,YAAa;MAACoB,SAAS,EAAC,gBAAgB;MAAAF,QAAA,gBAChD3D,OAAA;QAAK6D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjC3D,OAAA;UAAO8D,OAAO,EAAC,UAAU;UAAAH,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1ClE,OAAA;UAAK6D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpC3D,OAAA;YACI+C,EAAE,EAAC,UAAU;YACboB,IAAI,EAAC,MAAM;YACX9B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAE/B,OAAO,CAACE,QAAS;YACxB2D,QAAQ,EAAEjC,iBAAkB;YAC5BkC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ;YACRC,SAAS,EAAC,GAAG;YACbC,SAAS,EAAC;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFlE,OAAA;YAAG6D,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENlE,OAAA;QAAK6D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjC3D,OAAA;UAAO8D,OAAO,EAAC,WAAW;UAAAH,QAAA,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5ClE,OAAA;UAAK6D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpC3D,OAAA;YACI+C,EAAE,EAAC,WAAW;YACdoB,IAAI,EAAC,MAAM;YACX9B,IAAI,EAAC,WAAW;YAChBC,KAAK,EAAE/B,OAAO,CAACG,SAAU;YACzB0D,QAAQ,EAAEjC,iBAAkB;YAC5BkC,WAAW,EAAC,sBAAsB;YAClCC,QAAQ;YACRC,SAAS,EAAC,GAAG;YACbC,SAAS,EAAC;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFlE,OAAA;YAAG6D,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENlE,OAAA;QAAK6D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjC3D,OAAA;UAAO8D,OAAO,EAAC,OAAO;UAAAH,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5ClE,OAAA;UAAK6D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpC3D,OAAA;YACI+C,EAAE,EAAC,OAAO;YACVoB,IAAI,EAAC,OAAO;YACZ9B,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE/B,OAAO,CAACI,KAAM;YACrByD,QAAQ,EAAEjC,iBAAkB;YAC5BkC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACFlE,OAAA;YAAG6D,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENlE,OAAA;QAAK6D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjC3D,OAAA;UAAO8D,OAAO,EAAC,UAAU;UAAAH,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1ClE,OAAA;UAAK6D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpC3D,OAAA;YACI+C,EAAE,EAAC,UAAU;YACboB,IAAI,EAAC,UAAU;YACf9B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAE/B,OAAO,CAACK,QAAS;YACxBwD,QAAQ,EAAEjC,iBAAkB;YAC5BkC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ;YACRC,SAAS,EAAC;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFlE,OAAA;YAAG6D,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACNlE,OAAA;UAAO6D,SAAS,EAAC,gBAAgB;UAAAF,QAAA,EAAC;QAElC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAENlE,OAAA;QAAK6D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjC3D,OAAA;UAAO8D,OAAO,EAAC,eAAe;UAAAH,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpDlE,OAAA;UAAK6D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpC3D,OAAA;YACI+C,EAAE,EAAC,eAAe;YAClBV,IAAI,EAAC,eAAe;YACpBC,KAAK,EAAE/B,OAAO,CAACM,aAAc;YAC7BuD,QAAQ,EAAEjC,iBAAkB;YAC5BmC,QAAQ;YAAAX,QAAA,gBAER3D,OAAA;cAAQsC,KAAK,EAAC,EAAE;cAAAqB,QAAA,EAAC;YAAyB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClD7D,KAAK,CAACoE,GAAG,CAACC,IAAI,iBACX1E,OAAA;cAAsBsC,KAAK,EAAEoC,IAAI,CAACrC,IAAK;cAAAsB,QAAA,EAAEe,IAAI,CAACrC;YAAI,GAArCqC,IAAI,CAAC3B,EAAE;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuC,CAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACTlE,OAAA;YAAG6D,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENlE,OAAA;QAAK6D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjC3D,OAAA;UAAO8D,OAAO,EAAC,uBAAuB;UAAAH,QAAA,EAAC;QAAkB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjElE,OAAA;UAAK6D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpC3D,OAAA;YACI+C,EAAE,EAAC,uBAAuB;YAC1BV,IAAI,EAAC,uBAAuB;YAC5BC,KAAK,EAAE/B,OAAO,CAACO,qBAAsB;YACrCsD,QAAQ,EAAEjC,iBAAkB;YAC5BmC,QAAQ;YACRK,QAAQ,EAAEpD,iBAAkB;YAAAoC,QAAA,EAE3BpC,iBAAiB,gBACdvB,OAAA;cAAQsC,KAAK,EAAC,EAAE;cAAAqB,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAE/ClE,OAAA,CAAAE,SAAA;cAAAyD,QAAA,gBACI3D,OAAA;gBAAQsC,KAAK,EAAC,EAAE;gBAAAqB,QAAA,EAAC;cAA8B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvD5C,UAAU,CAACmD,GAAG,CAACG,QAAQ,iBACpB5E,OAAA;gBAA0BsC,KAAK,EAAEsC,QAAQ,CAAC7B,EAAG;gBAAAY,QAAA,GACxCiB,QAAQ,CAAC1B,aAAa,EAAC,KAAG,EAAC0B,QAAQ,CAAC3B,aAAa,EAAC,IAAE,EAAC2B,QAAQ,CAACC,eAAe,EAAC,GACnF;cAAA,GAFaD,QAAQ,CAAC7B,EAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACX,CAAC,EACD5C,UAAU,CAACwD,MAAM,KAAK,CAAC,iBACpB9E,OAAA;gBAAQsC,KAAK,EAAC,GAAG;gBAAAqB,QAAA,EAAC;cAAyB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACtD;YAAA,eACH;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACTlE,OAAA;YAAG6D,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNlE,OAAA;UAAO6D,SAAS,EAAC,gBAAgB;UAAAF,QAAA,gBAC7B3D,OAAA;YAAA2D,QAAA,EAAQ;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,6HAE9B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAETnD,KAAK,iBAAIf,OAAA;QAAK6D,SAAS,EAAC,yBAAyB;QAAAF,QAAA,EAAE5C;MAAK;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC/DjD,OAAO,iBAAIjB,OAAA;QAAK6D,SAAS,EAAC,2BAA2B;QAAAF,QAAA,EAAE1C;MAAO;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtElE,OAAA;QACImE,IAAI,EAAC,QAAQ;QACbN,SAAS,EAAC,kBAAkB;QAC5Bc,QAAQ,EAAExD,OAAO,IAAII,iBAAkB;QAAAoC,QAAA,EAEtCxC,OAAO,gBACJnB,OAAA,CAAAE,SAAA;UAAAyD,QAAA,gBACI3D,OAAA;YAAM6D,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,uBAEvD;QAAA,eAAE,CAAC,GAEH;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAETlE,OAAA;QAAK6D,SAAS,EAAC,kBAAkB;QAAAF,QAAA,eAC7B3D,OAAA;UAAA2D,QAAA,GAAG,2BAAyB,eAAA3D,OAAA,CAACV,IAAI;YAACyF,EAAE,EAAC,QAAQ;YAAClB,SAAS,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEzB;AAAC9D,EAAA,CApPQD,gBAAgB;EAAA,QAaJd,WAAW,EAG6CE,WAAW,EAChEK,aAAa;AAAA;AAAAoF,EAAA,GAjB5B7E,gBAAgB;AAsPzB,eAAeA,gBAAgB;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}