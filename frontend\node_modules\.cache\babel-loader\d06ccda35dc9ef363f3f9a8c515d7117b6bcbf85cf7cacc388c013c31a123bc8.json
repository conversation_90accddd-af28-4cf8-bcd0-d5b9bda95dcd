{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';\nimport './utils/axiosConfig'; // Import axios configuration\nimport { UserProvider } from './context/UserContext';\nimport { ErrorProvider } from './contexts/ErrorContext';\nimport { SiteConfigProvider } from './contexts/SiteConfigContext';\nimport { CurrencyProvider } from './contexts/CurrencyContext'; // FIXED: No longer causing infinite loops\nimport './styles/SmoothScroll.css'; // Import smooth scrolling styles\n\n// Layouts\nimport AdminLayout from './components/AdminLayout';\nimport UserLayout from './components/UserLayout';\n\n// Components\nimport Messages from './components/Messages/Messages';\n\n// Public Pages\nimport SimpleSoccerHome from './pages/SimpleSoccerHome';\nimport AdminLoginPage from './pages/AdminLoginPage';\nimport UserLogin from './pages/UserLogin';\nimport TestLogin from './pages/TestLogin';\nimport UserRegistration from './pages/UserRegistration';\nimport ForgotPassword from './pages/ForgotPassword';\n\n// Admin Pages\nimport AdminDashboard from './pages/AdminDashboard';\nimport ChallengeSystem from './pages/ChallengeSystem';\nimport UserManagement from './pages/UserManagement';\nimport UserDetails from './pages/UserDetails';\nimport BetManagement from './pages/BetManagement';\nimport TransactionManagement from './pages/TransactionManagement';\nimport LeaderboardManagement from './pages/LeaderboardManagement';\nimport SystemSettings from './pages/SystemSettings';\nimport SMTPSettings from './pages/SMTPSettings';\nimport SecuritySettings from './pages/SecuritySettings';\nimport Admin2FASettings from './pages/Admin2FASettings';\nimport GeneralSettings from './pages/GeneralSettings';\nimport NotificationSettings from './pages/NotificationSettings';\nimport ReportsAnalytics from './pages/ReportsAnalytics';\nimport AdminLeaderboard from './pages/AdminLeaderboard';\nimport AdminReports from './pages/AdminReports';\nimport AddUser from './pages/AddUser';\nimport PaymentMethods from './pages/PaymentMethods';\nimport CreditUser from './pages/CreditUser';\nimport DebitUser from './pages/DebitUser';\nimport TeamManagement from './pages/TeamManagement';\nimport ChallengeManagement from './pages/ChallengeManagement';\nimport CreditChallenge from './pages/CreditChallenge';\nimport LeagueManagement from './pages/LeagueManagement';\nimport LeagueSeasonManagement from './pages/LeagueSeasonManagement';\nimport CreateLeague from './pages/CreateLeague';\nimport LeagueDetails from './pages/LeagueDetails';\nimport LeagueUserManagement from './pages/LeagueUserManagement';\nimport CurrencyManagement from './pages/CurrencyManagement';\n\n// User Pages\nimport UserDashboard from './pages/UserDashboard';\nimport JoinChallenge from './pages/JoinChallenge';\nimport JoinChallenge2 from './pages/JoinChallenge2';\nimport ViewBets from './pages/ViewBets';\nimport IncomingBets from './pages/IncomingBets';\nimport Profile from './pages/Profile';\nimport AcceptedBets from './pages/AcceptedBets';\nimport PaymentHistory from './pages/PaymentHistory';\nimport Leaderboard from './pages/Leaderboard';\nimport ChangePassword from './pages/ChangePassword';\nimport UserSettings from './pages/UserSettingsFixed';\nimport Deposit from './pages/Deposit';\nimport Withdraw from './pages/Withdraw';\nimport Friends from './pages/Friends';\nimport FriendRequests from './pages/FriendRequests';\nimport LeagueHome from './pages/LeagueHome';\nimport LeagueSelection from './pages/LeagueSelection';\nimport UserAchievements from './pages/UserAchievements';\nimport SeasonHistory from './pages/SeasonHistory';\nimport MyLeagues from './pages/MyLeagues';\nimport Transfer from './pages/Transfer';\nimport CreditWallet from './pages/CreditWallet';\nimport CreditHistory from './pages/CreditHistory';\nimport Challenges from './pages/Challenges';\nimport RecentBets from './pages/RecentBets';\n\n// Protected Route Component for Users\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  const isAuthenticated = localStorage.getItem('userId');\n  if (!isAuthenticated) {\n    // Save the current path for redirect after login\n    const currentPath = window.location.pathname;\n    if (currentPath !== '/login') {\n      sessionStorage.setItem('redirectAfterLogin', currentPath);\n    }\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n\n// Protected Route Component for Admin\n_c = ProtectedRoute;\nconst AdminProtectedRoute = ({\n  children\n}) => {\n  const isAdminAuthenticated = localStorage.getItem('adminId');\n  if (!isAdminAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/admin/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_c2 = AdminProtectedRoute;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(UserProvider, {\n    children: /*#__PURE__*/_jsxDEV(ErrorProvider, {\n      children: /*#__PURE__*/_jsxDEV(SiteConfigProvider, {\n        children: /*#__PURE__*/_jsxDEV(CurrencyProvider, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"App\",\n            children: /*#__PURE__*/_jsxDEV(Router, {\n              children: /*#__PURE__*/_jsxDEV(Routes, {\n                children: [/*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/\",\n                  element: /*#__PURE__*/_jsxDEV(NewWelcomePage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 40\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/login\",\n                  element: /*#__PURE__*/_jsxDEV(UserLogin, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/test-login\",\n                  element: /*#__PURE__*/_jsxDEV(TestLogin, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 50\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/register\",\n                  element: /*#__PURE__*/_jsxDEV(UserRegistration, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 48\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/forgot-password\",\n                  element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 55\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/admin/login\",\n                  element: /*#__PURE__*/_jsxDEV(AdminLoginPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 51\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/user-dashboard\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    children: /*#__PURE__*/_jsxDEV(UserLayout, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 130,\n                      columnNumber: 19\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/admin\",\n                  element: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                    children: /*#__PURE__*/_jsxDEV(AdminLayout, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 19\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 17\n                  }, this),\n                  children: [/*#__PURE__*/_jsxDEV(Route, {\n                    index: true,\n                    element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 39\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"dashboard\",\n                    element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 50\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"challenge-system\",\n                    element: /*#__PURE__*/_jsxDEV(ChallengeSystem, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 142,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"challenge-management\",\n                    element: /*#__PURE__*/_jsxDEV(ChallengeManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 143,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"credit-challenge\",\n                    element: /*#__PURE__*/_jsxDEV(CreditChallenge, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"team-management\",\n                    element: /*#__PURE__*/_jsxDEV(TeamManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 56\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"league-management\",\n                    element: /*#__PURE__*/_jsxDEV(LeagueManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 58\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"league-management/create\",\n                    element: /*#__PURE__*/_jsxDEV(CreateLeague, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 65\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"league-seasons\",\n                    element: /*#__PURE__*/_jsxDEV(LeagueSeasonManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 55\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"league-divisions\",\n                    element: /*#__PURE__*/_jsxDEV(LeagueManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 150,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"league-rewards\",\n                    element: /*#__PURE__*/_jsxDEV(LeagueManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 151,\n                      columnNumber: 55\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"league-management/:leagueId/seasons\",\n                    element: /*#__PURE__*/_jsxDEV(LeagueSeasonManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 152,\n                      columnNumber: 76\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"league-users\",\n                    element: /*#__PURE__*/_jsxDEV(LeagueUserManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 153,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"users\",\n                    element: /*#__PURE__*/_jsxDEV(UserManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 46\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"users/:userId\",\n                    element: /*#__PURE__*/_jsxDEV(UserDetails, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 54\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"add-user\",\n                    element: /*#__PURE__*/_jsxDEV(AddUser, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"credit-user\",\n                    element: /*#__PURE__*/_jsxDEV(CreditUser, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 158,\n                      columnNumber: 52\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"debit-user\",\n                    element: /*#__PURE__*/_jsxDEV(DebitUser, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 51\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"payment-methods\",\n                    element: /*#__PURE__*/_jsxDEV(PaymentMethods, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 56\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"currency-management\",\n                    element: /*#__PURE__*/_jsxDEV(CurrencyManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 60\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"bets\",\n                    element: /*#__PURE__*/_jsxDEV(BetManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"transactions\",\n                    element: /*#__PURE__*/_jsxDEV(TransactionManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"leaderboard\",\n                    element: /*#__PURE__*/_jsxDEV(AdminLeaderboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 52\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"leaderboard-management\",\n                    element: /*#__PURE__*/_jsxDEV(LeaderboardManagement, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 63\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"reports\",\n                    element: /*#__PURE__*/_jsxDEV(AdminReports, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 48\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"reports-analytics\",\n                    element: /*#__PURE__*/_jsxDEV(ReportsAnalytics, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 167,\n                      columnNumber: 58\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"settings\",\n                    element: /*#__PURE__*/_jsxDEV(SystemSettings, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"general-settings\",\n                    element: /*#__PURE__*/_jsxDEV(GeneralSettings, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"smtp-settings\",\n                    element: /*#__PURE__*/_jsxDEV(SMTPSettings, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 54\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"security-settings\",\n                    element: /*#__PURE__*/_jsxDEV(SecuritySettings, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 58\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"2fa-settings\",\n                    element: /*#__PURE__*/_jsxDEV(Admin2FASettings, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 172,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"notification-settings\",\n                    element: /*#__PURE__*/_jsxDEV(NotificationSettings, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 62\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/user\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    children: /*#__PURE__*/_jsxDEV(UserLayout, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 19\n                  }, this),\n                  children: [/*#__PURE__*/_jsxDEV(Route, {\n                    index: true,\n                    element: /*#__PURE__*/_jsxDEV(UserDashboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 39\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"dashboard\",\n                    element: /*#__PURE__*/_jsxDEV(UserDashboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 50\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"bets\",\n                    element: /*#__PURE__*/_jsxDEV(ViewBets, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"bets/outgoing\",\n                    element: /*#__PURE__*/_jsxDEV(ViewBets, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 188,\n                      columnNumber: 54\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"bets/incoming\",\n                    element: /*#__PURE__*/_jsxDEV(IncomingBets, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 54\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"bets/accepted\",\n                    element: /*#__PURE__*/_jsxDEV(AcceptedBets, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 54\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"payment-history\",\n                    element: /*#__PURE__*/_jsxDEV(PaymentHistory, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 56\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"leaderboard\",\n                    element: /*#__PURE__*/_jsxDEV(Leaderboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 52\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"profile\",\n                    element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 48\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"profile/:username\",\n                    element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 58\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"change-password\",\n                    element: /*#__PURE__*/_jsxDEV(ChangePassword, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 56\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"settings\",\n                    element: /*#__PURE__*/_jsxDEV(UserSettings, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"deposit\",\n                    element: /*#__PURE__*/_jsxDEV(Deposit, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 48\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"withdraw\",\n                    element: /*#__PURE__*/_jsxDEV(Withdraw, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"friends\",\n                    element: /*#__PURE__*/_jsxDEV(Friends, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 48\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"friend-requests\",\n                    element: /*#__PURE__*/_jsxDEV(FriendRequests, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 56\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"join-challenge/:challengeId\",\n                    element: /*#__PURE__*/_jsxDEV(JoinChallenge, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 68\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"join-challenge2/:challengeId/:betId/:uniqueCode/:user1Id\",\n                    element: /*#__PURE__*/_jsxDEV(JoinChallenge2, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 97\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"messages\",\n                    element: /*#__PURE__*/_jsxDEV(Messages, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"challenges\",\n                    element: /*#__PURE__*/_jsxDEV(Challenges, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 51\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"recent-bets\",\n                    element: /*#__PURE__*/_jsxDEV(RecentBets, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 52\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"leagues\",\n                    element: /*#__PURE__*/_jsxDEV(LeagueHome, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 48\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"my-leagues\",\n                    element: /*#__PURE__*/_jsxDEV(MyLeagues, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 51\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"leagues/:leagueId\",\n                    element: /*#__PURE__*/_jsxDEV(LeagueDetails, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 58\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"leagues/achievements\",\n                    element: /*#__PURE__*/_jsxDEV(UserAchievements, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"leagues/seasons\",\n                    element: /*#__PURE__*/_jsxDEV(SeasonHistory, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 56\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"league\",\n                    element: /*#__PURE__*/_jsxDEV(Navigate, {\n                      to: \"/user/leagues\",\n                      replace: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 47\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"league/:leagueId/selection\",\n                    element: /*#__PURE__*/_jsxDEV(Navigate, {\n                      to: \"/user/leagues/:leagueId\",\n                      replace: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 67\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"league/:leagueId/leaderboard\",\n                    element: /*#__PURE__*/_jsxDEV(Navigate, {\n                      to: \"/user/leagues/:leagueId/leaderboard\",\n                      replace: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 69\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"achievements\",\n                    element: /*#__PURE__*/_jsxDEV(Navigate, {\n                      to: \"/user/leagues/achievements\",\n                      replace: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"season-history\",\n                    element: /*#__PURE__*/_jsxDEV(Navigate, {\n                      to: \"/user/leagues/seasons\",\n                      replace: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 55\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"transfer\",\n                    element: /*#__PURE__*/_jsxDEV(Transfer, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"wallet\",\n                    element: /*#__PURE__*/_jsxDEV(CreditWallet, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 47\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"credit-history\",\n                    element: /*#__PURE__*/_jsxDEV(CreditHistory, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 55\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"*\",\n                  element: localStorage.getItem('adminId') ? /*#__PURE__*/_jsxDEV(Navigate, {\n                    to: \"/admin/dashboard\",\n                    replace: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 21\n                  }, this) : localStorage.getItem('userId') && localStorage.getItem('userToken') ? /*#__PURE__*/_jsxDEV(Navigate, {\n                    to: \"/user/dashboard\",\n                    replace: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 21\n                  }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n                    to: \"/login\",\n                    replace: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 13\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"AdminProtectedRoute\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "Navigate", "UserProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SiteConfigProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AdminLayout", "UserLayout", "Messages", "SimpleSoccerHome", "AdminLoginPage", "UserLogin", "TestLogin", "UserRegistration", "ForgotPassword", "AdminDashboard", "ChallengeSystem", "UserManagement", "UserDetails", "BetManagement", "TransactionManagement", "LeaderboardManagement", "SystemSettings", "SMTPSettings", "SecuritySettings", "Admin2FASettings", "GeneralSettings", "NotificationSettings", "ReportsAnalytics", "AdminLeaderboard", "AdminReports", "AddUser", "PaymentMethods", "CreditUser", "DebitUser", "TeamManagement", "ChallengeManagement", "CreditChallenge", "LeagueManagement", "LeagueSeasonManagement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LeagueDetails", "LeagueUserManagement", "CurrencyManagement", "UserDashboard", "JoinChall<PERSON>e", "JoinChallenge2", "ViewBets", "IncomingBets", "Profile", "AcceptedBets", "PaymentHistory", "Leaderboard", "ChangePassword", "UserSettings", "<PERSON><PERSON><PERSON><PERSON>", "Withdraw", "Friends", "FriendRequests", "LeagueHome", "LeagueSelection", "UserAchievements", "SeasonHistory", "MyLeagues", "Transfer", "CreditWallet", "CreditHistory", "Challenges", "RecentBets", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "isAuthenticated", "localStorage", "getItem", "currentPath", "window", "location", "pathname", "sessionStorage", "setItem", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AdminProtectedRoute", "isAdminAuthenticated", "_c2", "App", "className", "path", "element", "NewWelcomePage", "index", "_c3", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';\nimport './utils/axiosConfig';  // Import axios configuration\nimport { UserProvider } from './context/UserContext';\nimport { ErrorProvider } from './contexts/ErrorContext';\nimport { SiteConfigProvider } from './contexts/SiteConfigContext';\nimport { CurrencyProvider } from './contexts/CurrencyContext'; // FIXED: No longer causing infinite loops\nimport './styles/SmoothScroll.css'; // Import smooth scrolling styles\n\n// Layouts\nimport AdminLayout from './components/AdminLayout';\nimport UserLayout from './components/UserLayout';\n\n// Components\nimport Messages from './components/Messages/Messages';\n\n// Public Pages\nimport SimpleSoccerHome from './pages/SimpleSoccerHome';\nimport AdminLoginPage from './pages/AdminLoginPage';\nimport UserLogin from './pages/UserLogin';\nimport TestLogin from './pages/TestLogin';\nimport UserRegistration from './pages/UserRegistration';\nimport ForgotPassword from './pages/ForgotPassword';\n\n// Admin Pages\nimport AdminDashboard from './pages/AdminDashboard';\nimport ChallengeSystem from './pages/ChallengeSystem';\nimport UserManagement from './pages/UserManagement';\nimport UserDetails from './pages/UserDetails';\nimport BetManagement from './pages/BetManagement';\nimport TransactionManagement from './pages/TransactionManagement';\nimport LeaderboardManagement from './pages/LeaderboardManagement';\nimport SystemSettings from './pages/SystemSettings';\nimport SMTPSettings from './pages/SMTPSettings';\nimport SecuritySettings from './pages/SecuritySettings';\nimport Admin2FASettings from './pages/Admin2FASettings';\nimport GeneralSettings from './pages/GeneralSettings';\nimport NotificationSettings from './pages/NotificationSettings';\nimport ReportsAnalytics from './pages/ReportsAnalytics';\nimport AdminLeaderboard from './pages/AdminLeaderboard';\nimport AdminReports from './pages/AdminReports';\nimport AddUser from './pages/AddUser';\nimport PaymentMethods from './pages/PaymentMethods';\nimport CreditUser from './pages/CreditUser';\nimport DebitUser from './pages/DebitUser';\nimport TeamManagement from './pages/TeamManagement';\nimport ChallengeManagement from './pages/ChallengeManagement';\nimport CreditChallenge from './pages/CreditChallenge';\nimport LeagueManagement from './pages/LeagueManagement';\nimport LeagueSeasonManagement from './pages/LeagueSeasonManagement';\nimport CreateLeague from './pages/CreateLeague';\nimport LeagueDetails from './pages/LeagueDetails';\nimport LeagueUserManagement from './pages/LeagueUserManagement';\nimport CurrencyManagement from './pages/CurrencyManagement';\n\n// User Pages\nimport UserDashboard from './pages/UserDashboard';\nimport JoinChallenge from './pages/JoinChallenge';\nimport JoinChallenge2 from './pages/JoinChallenge2';\nimport ViewBets from './pages/ViewBets';\nimport IncomingBets from './pages/IncomingBets';\nimport Profile from './pages/Profile';\nimport AcceptedBets from './pages/AcceptedBets';\nimport PaymentHistory from './pages/PaymentHistory';\nimport Leaderboard from './pages/Leaderboard';\nimport ChangePassword from './pages/ChangePassword';\nimport UserSettings from './pages/UserSettingsFixed';\nimport Deposit from './pages/Deposit';\nimport Withdraw from './pages/Withdraw';\nimport Friends from './pages/Friends';\nimport FriendRequests from './pages/FriendRequests';\nimport LeagueHome from './pages/LeagueHome';\nimport LeagueSelection from './pages/LeagueSelection';\nimport UserAchievements from './pages/UserAchievements';\nimport SeasonHistory from './pages/SeasonHistory';\nimport MyLeagues from './pages/MyLeagues';\nimport Transfer from './pages/Transfer';\nimport CreditWallet from './pages/CreditWallet';\nimport CreditHistory from './pages/CreditHistory';\nimport Challenges from './pages/Challenges';\nimport RecentBets from './pages/RecentBets';\n\n// Protected Route Component for Users\nconst ProtectedRoute = ({ children }) => {\n  const isAuthenticated = localStorage.getItem('userId');\n\n  if (!isAuthenticated) {\n    // Save the current path for redirect after login\n    const currentPath = window.location.pathname;\n    if (currentPath !== '/login') {\n      sessionStorage.setItem('redirectAfterLogin', currentPath);\n    }\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  return children;\n};\n\n// Protected Route Component for Admin\nconst AdminProtectedRoute = ({ children }) => {\n  const isAdminAuthenticated = localStorage.getItem('adminId');\n\n  if (!isAdminAuthenticated) {\n    return <Navigate to=\"/admin/login\" replace />;\n  }\n\n  return children;\n};\n\nfunction App() {\n  return (\n    <UserProvider>\n      <ErrorProvider>\n        <SiteConfigProvider>\n          <CurrencyProvider>\n            <div className=\"App\">\n              <Router>\n            <Routes>\n              {/* Public Routes */}\n              <Route path=\"/\" element={<NewWelcomePage />} />\n              <Route path=\"/login\" element={<UserLogin />} />\n              <Route path=\"/test-login\" element={<TestLogin />} />\n              <Route path=\"/register\" element={<UserRegistration />} />\n              <Route path=\"/forgot-password\" element={<ForgotPassword />} />\n              <Route path=\"/admin/login\" element={<AdminLoginPage />} />\n\n              {/* Direct UserDashboard route for easy testing */}\n              <Route path=\"/user-dashboard\" element={\n                <ProtectedRoute>\n                  <UserLayout />\n                </ProtectedRoute>\n              } />\n\n              {/* Admin Routes */}\n              <Route path=\"/admin\" element={\n                <AdminProtectedRoute>\n                  <AdminLayout />\n                </AdminProtectedRoute>\n              }>\n                <Route index element={<AdminDashboard />} />\n                <Route path=\"dashboard\" element={<AdminDashboard />} />\n                <Route path=\"challenge-system\" element={<ChallengeSystem />} />\n                <Route path=\"challenge-management\" element={<ChallengeManagement />} />\n                <Route path=\"credit-challenge\" element={<CreditChallenge />} />\n                <Route path=\"team-management\" element={<TeamManagement />} />\n                {/* League Management Routes */}\n                <Route path=\"league-management\" element={<LeagueManagement />} />\n                <Route path=\"league-management/create\" element={<CreateLeague />} />\n                <Route path=\"league-seasons\" element={<LeagueSeasonManagement />} />\n                <Route path=\"league-divisions\" element={<LeagueManagement />} />\n                <Route path=\"league-rewards\" element={<LeagueManagement />} />\n                <Route path=\"league-management/:leagueId/seasons\" element={<LeagueSeasonManagement />} />\n                <Route path=\"league-users\" element={<LeagueUserManagement />} />\n                {/* Other Admin Routes */}\n                <Route path=\"users\" element={<UserManagement />} />\n                <Route path=\"users/:userId\" element={<UserDetails />} />\n                <Route path=\"add-user\" element={<AddUser />} />\n                <Route path=\"credit-user\" element={<CreditUser />} />\n                <Route path=\"debit-user\" element={<DebitUser />} />\n                <Route path=\"payment-methods\" element={<PaymentMethods />} />\n                <Route path=\"currency-management\" element={<CurrencyManagement />} />\n                <Route path=\"bets\" element={<BetManagement />} />\n                <Route path=\"transactions\" element={<TransactionManagement />} />\n                <Route path=\"leaderboard\" element={<AdminLeaderboard />} />\n                <Route path=\"leaderboard-management\" element={<LeaderboardManagement />} />\n                <Route path=\"reports\" element={<AdminReports />} />\n                <Route path=\"reports-analytics\" element={<ReportsAnalytics />} />\n                <Route path=\"settings\" element={<SystemSettings />} />\n                <Route path=\"general-settings\" element={<GeneralSettings />} />\n                <Route path=\"smtp-settings\" element={<SMTPSettings />} />\n                <Route path=\"security-settings\" element={<SecuritySettings />} />\n                <Route path=\"2fa-settings\" element={<Admin2FASettings />} />\n                <Route path=\"notification-settings\" element={<NotificationSettings />} />\n              </Route>\n\n              {/* User Routes */}\n              <Route\n                path=\"/user\"\n                element={\n                  <ProtectedRoute>\n                    <UserLayout />\n                  </ProtectedRoute>\n                }\n              >\n                <Route index element={<UserDashboard />} />\n                <Route path=\"dashboard\" element={<UserDashboard />} />\n                <Route path=\"bets\" element={<ViewBets />} />\n                <Route path=\"bets/outgoing\" element={<ViewBets />} />\n                <Route path=\"bets/incoming\" element={<IncomingBets />} />\n                <Route path=\"bets/accepted\" element={<AcceptedBets />} />\n                <Route path=\"payment-history\" element={<PaymentHistory />} />\n                <Route path=\"leaderboard\" element={<Leaderboard />} />\n                <Route path=\"profile\" element={<Profile />} />\n                <Route path=\"profile/:username\" element={<Profile />} />\n                <Route path=\"change-password\" element={<ChangePassword />} />\n                <Route path=\"settings\" element={<UserSettings />} />\n                <Route path=\"deposit\" element={<Deposit />} />\n                <Route path=\"withdraw\" element={<Withdraw />} />\n                <Route path=\"friends\" element={<Friends />} />\n                <Route path=\"friend-requests\" element={<FriendRequests />} />\n                <Route path=\"join-challenge/:challengeId\" element={<JoinChallenge />} />\n                <Route path=\"join-challenge2/:challengeId/:betId/:uniqueCode/:user1Id\" element={<JoinChallenge2 />} />\n                <Route path=\"messages\" element={<Messages />} />\n                <Route path=\"challenges\" element={<Challenges />} />\n                <Route path=\"recent-bets\" element={<RecentBets />} />\n                {/* League Routes */}\n                <Route path=\"leagues\" element={<LeagueHome />} />\n                <Route path=\"my-leagues\" element={<MyLeagues />} />\n                <Route path=\"leagues/:leagueId\" element={<LeagueDetails />} />\n                <Route path=\"leagues/achievements\" element={<UserAchievements />} />\n                <Route path=\"leagues/seasons\" element={<SeasonHistory />} />\n                {/* Legacy League Routes - Keep for backward compatibility */}\n                <Route path=\"league\" element={<Navigate to=\"/user/leagues\" replace />} />\n                <Route path=\"league/:leagueId/selection\" element={<Navigate to=\"/user/leagues/:leagueId\" replace />} />\n                <Route path=\"league/:leagueId/leaderboard\" element={<Navigate to=\"/user/leagues/:leagueId/leaderboard\" replace />} />\n                <Route path=\"achievements\" element={<Navigate to=\"/user/leagues/achievements\" replace />} />\n                <Route path=\"season-history\" element={<Navigate to=\"/user/leagues/seasons\" replace />} />\n                <Route path=\"transfer\" element={<Transfer />} />\n                <Route path=\"wallet\" element={<CreditWallet />} />\n                <Route path=\"credit-history\" element={<CreditHistory />} />\n              </Route>\n\n              {/* Catch all route - redirect to appropriate dashboard if authenticated, otherwise to login */}\n              <Route\n                path=\"*\"\n                element={\n                  localStorage.getItem('adminId') ? (\n                    <Navigate to=\"/admin/dashboard\" replace />\n                  ) : localStorage.getItem('userId') && localStorage.getItem('userToken') ? (\n                    <Navigate to=\"/user/dashboard\" replace />\n                  ) : (\n                    <Navigate to=\"/login\" replace />\n                  )\n                }\n              />\n            </Routes>\n          </Router>\n        </div>\n      </CurrencyProvider>\n    </SiteConfigProvider>\n  </ErrorProvider>\n</UserProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAO,qBAAqB,CAAC,CAAE;AAC/B,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,gBAAgB,QAAQ,4BAA4B,CAAC,CAAC;AAC/D,OAAO,2BAA2B,CAAC,CAAC;;AAEpC;AACA,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,yBAAyB;;AAEhD;AACA,OAAOC,QAAQ,MAAM,gCAAgC;;AAErD;AACA,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,cAAc,MAAM,wBAAwB;;AAEnD;AACA,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,qBAAqB,MAAM,+BAA+B;AACjE,OAAOC,qBAAqB,MAAM,+BAA+B;AACjE,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,oBAAoB,MAAM,8BAA8B;AAC/D,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,sBAAsB,MAAM,gCAAgC;AACnE,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,oBAAoB,MAAM,8BAA8B;AAC/D,OAAOC,kBAAkB,MAAM,4BAA4B;;AAE3D;AACA,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,UAAU,MAAM,oBAAoB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACvC,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;EAEtD,IAAI,CAACF,eAAe,EAAE;IACpB;IACA,MAAMG,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;IAC5C,IAAIH,WAAW,KAAK,QAAQ,EAAE;MAC5BI,cAAc,CAACC,OAAO,CAAC,oBAAoB,EAAEL,WAAW,CAAC;IAC3D;IACA,oBAAON,OAAA,CAACrE,QAAQ;MAACiF,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEA,OAAOf,QAAQ;AACjB,CAAC;;AAED;AAAAgB,EAAA,GAfMjB,cAAc;AAgBpB,MAAMkB,mBAAmB,GAAGA,CAAC;EAAEjB;AAAS,CAAC,KAAK;EAC5C,MAAMkB,oBAAoB,GAAGhB,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAE5D,IAAI,CAACe,oBAAoB,EAAE;IACzB,oBAAOpB,OAAA,CAACrE,QAAQ;MAACiF,EAAE,EAAC,cAAc;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC/C;EAEA,OAAOf,QAAQ;AACjB,CAAC;AAACmB,GAAA,GARIF,mBAAmB;AAUzB,SAASG,GAAGA,CAAA,EAAG;EACb,oBACEtB,OAAA,CAACpE,YAAY;IAAAsE,QAAA,eACXF,OAAA,CAACnE,aAAa;MAAAqE,QAAA,eACZF,OAAA,CAAClE,kBAAkB;QAAAoE,QAAA,eACjBF,OAAA,CAACjE,gBAAgB;UAAAmE,QAAA,eACfF,OAAA;YAAKuB,SAAS,EAAC,KAAK;YAAArB,QAAA,eAClBF,OAAA,CAACxE,MAAM;cAAA0E,QAAA,eACTF,OAAA,CAACtE,MAAM;gBAAAwE,QAAA,gBAELF,OAAA,CAACvE,KAAK;kBAAC+F,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAEzB,OAAA,CAAC0B,cAAc;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CjB,OAAA,CAACvE,KAAK;kBAAC+F,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAAEzB,OAAA,CAAC3D,SAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CjB,OAAA,CAACvE,KAAK;kBAAC+F,IAAI,EAAC,aAAa;kBAACC,OAAO,eAAEzB,OAAA,CAAC1D,SAAS;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDjB,OAAA,CAACvE,KAAK;kBAAC+F,IAAI,EAAC,WAAW;kBAACC,OAAO,eAAEzB,OAAA,CAACzD,gBAAgB;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzDjB,OAAA,CAACvE,KAAK;kBAAC+F,IAAI,EAAC,kBAAkB;kBAACC,OAAO,eAAEzB,OAAA,CAACxD,cAAc;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9DjB,OAAA,CAACvE,KAAK;kBAAC+F,IAAI,EAAC,cAAc;kBAACC,OAAO,eAAEzB,OAAA,CAAC5D,cAAc;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAG1DjB,OAAA,CAACvE,KAAK;kBAAC+F,IAAI,EAAC,iBAAiB;kBAACC,OAAO,eACnCzB,OAAA,CAACC,cAAc;oBAAAC,QAAA,eACbF,OAAA,CAAC/D,UAAU;sBAAA6E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAGJjB,OAAA,CAACvE,KAAK;kBAAC+F,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAC1BzB,OAAA,CAACmB,mBAAmB;oBAAAjB,QAAA,eAClBF,OAAA,CAAChE,WAAW;sBAAA8E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CACtB;kBAAAf,QAAA,gBACCF,OAAA,CAACvE,KAAK;oBAACkG,KAAK;oBAACF,OAAO,eAAEzB,OAAA,CAACvD,cAAc;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,WAAW;oBAACC,OAAO,eAAEzB,OAAA,CAACvD,cAAc;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,kBAAkB;oBAACC,OAAO,eAAEzB,OAAA,CAACtD,eAAe;sBAAAoE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,sBAAsB;oBAACC,OAAO,eAAEzB,OAAA,CAAClC,mBAAmB;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,kBAAkB;oBAACC,OAAO,eAAEzB,OAAA,CAACjC,eAAe;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,iBAAiB;oBAACC,OAAO,eAAEzB,OAAA,CAACnC,cAAc;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAE7DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,mBAAmB;oBAACC,OAAO,eAAEzB,OAAA,CAAChC,gBAAgB;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,0BAA0B;oBAACC,OAAO,eAAEzB,OAAA,CAAC9B,YAAY;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,gBAAgB;oBAACC,OAAO,eAAEzB,OAAA,CAAC/B,sBAAsB;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,kBAAkB;oBAACC,OAAO,eAAEzB,OAAA,CAAChC,gBAAgB;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,gBAAgB;oBAACC,OAAO,eAAEzB,OAAA,CAAChC,gBAAgB;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,qCAAqC;oBAACC,OAAO,eAAEzB,OAAA,CAAC/B,sBAAsB;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzFjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,cAAc;oBAACC,OAAO,eAAEzB,OAAA,CAAC5B,oBAAoB;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEhEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,OAAO;oBAACC,OAAO,eAAEzB,OAAA,CAACrD,cAAc;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,eAAe;oBAACC,OAAO,eAAEzB,OAAA,CAACpD,WAAW;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,UAAU;oBAACC,OAAO,eAAEzB,OAAA,CAACvC,OAAO;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/CjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,aAAa;oBAACC,OAAO,eAAEzB,OAAA,CAACrC,UAAU;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,YAAY;oBAACC,OAAO,eAAEzB,OAAA,CAACpC,SAAS;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,iBAAiB;oBAACC,OAAO,eAAEzB,OAAA,CAACtC,cAAc;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,qBAAqB;oBAACC,OAAO,eAAEzB,OAAA,CAAC3B,kBAAkB;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,MAAM;oBAACC,OAAO,eAAEzB,OAAA,CAACnD,aAAa;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,cAAc;oBAACC,OAAO,eAAEzB,OAAA,CAAClD,qBAAqB;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,aAAa;oBAACC,OAAO,eAAEzB,OAAA,CAACzC,gBAAgB;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,wBAAwB;oBAACC,OAAO,eAAEzB,OAAA,CAACjD,qBAAqB;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3EjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,SAAS;oBAACC,OAAO,eAAEzB,OAAA,CAACxC,YAAY;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,mBAAmB;oBAACC,OAAO,eAAEzB,OAAA,CAAC1C,gBAAgB;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,UAAU;oBAACC,OAAO,eAAEzB,OAAA,CAAChD,cAAc;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,kBAAkB;oBAACC,OAAO,eAAEzB,OAAA,CAAC5C,eAAe;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,eAAe;oBAACC,OAAO,eAAEzB,OAAA,CAAC/C,YAAY;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,mBAAmB;oBAACC,OAAO,eAAEzB,OAAA,CAAC9C,gBAAgB;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,cAAc;oBAACC,OAAO,eAAEzB,OAAA,CAAC7C,gBAAgB;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,uBAAuB;oBAACC,OAAO,eAAEzB,OAAA,CAAC3C,oBAAoB;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,eAGRjB,OAAA,CAACvE,KAAK;kBACJ+F,IAAI,EAAC,OAAO;kBACZC,OAAO,eACLzB,OAAA,CAACC,cAAc;oBAAAC,QAAA,eACbF,OAAA,CAAC/D,UAAU;sBAAA6E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CACjB;kBAAAf,QAAA,gBAEDF,OAAA,CAACvE,KAAK;oBAACkG,KAAK;oBAACF,OAAO,eAAEzB,OAAA,CAAC1B,aAAa;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3CjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,WAAW;oBAACC,OAAO,eAAEzB,OAAA,CAAC1B,aAAa;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,MAAM;oBAACC,OAAO,eAAEzB,OAAA,CAACvB,QAAQ;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,eAAe;oBAACC,OAAO,eAAEzB,OAAA,CAACvB,QAAQ;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,eAAe;oBAACC,OAAO,eAAEzB,OAAA,CAACtB,YAAY;sBAAAoC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,eAAe;oBAACC,OAAO,eAAEzB,OAAA,CAACpB,YAAY;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,iBAAiB;oBAACC,OAAO,eAAEzB,OAAA,CAACnB,cAAc;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,aAAa;oBAACC,OAAO,eAAEzB,OAAA,CAAClB,WAAW;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,SAAS;oBAACC,OAAO,eAAEzB,OAAA,CAACrB,OAAO;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,mBAAmB;oBAACC,OAAO,eAAEzB,OAAA,CAACrB,OAAO;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,iBAAiB;oBAACC,OAAO,eAAEzB,OAAA,CAACjB,cAAc;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,UAAU;oBAACC,OAAO,eAAEzB,OAAA,CAAChB,YAAY;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,SAAS;oBAACC,OAAO,eAAEzB,OAAA,CAACf,OAAO;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,UAAU;oBAACC,OAAO,eAAEzB,OAAA,CAACd,QAAQ;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,SAAS;oBAACC,OAAO,eAAEzB,OAAA,CAACb,OAAO;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,iBAAiB;oBAACC,OAAO,eAAEzB,OAAA,CAACZ,cAAc;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,6BAA6B;oBAACC,OAAO,eAAEzB,OAAA,CAACzB,aAAa;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,0DAA0D;oBAACC,OAAO,eAAEzB,OAAA,CAACxB,cAAc;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtGjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,UAAU;oBAACC,OAAO,eAAEzB,OAAA,CAAC9D,QAAQ;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,YAAY;oBAACC,OAAO,eAAEzB,OAAA,CAACH,UAAU;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,aAAa;oBAACC,OAAO,eAAEzB,OAAA,CAACF,UAAU;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAErDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,SAAS;oBAACC,OAAO,eAAEzB,OAAA,CAACX,UAAU;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,YAAY;oBAACC,OAAO,eAAEzB,OAAA,CAACP,SAAS;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,mBAAmB;oBAACC,OAAO,eAAEzB,OAAA,CAAC7B,aAAa;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,sBAAsB;oBAACC,OAAO,eAAEzB,OAAA,CAACT,gBAAgB;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,iBAAiB;oBAACC,OAAO,eAAEzB,OAAA,CAACR,aAAa;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAE5DjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,QAAQ;oBAACC,OAAO,eAAEzB,OAAA,CAACrE,QAAQ;sBAACiF,EAAE,EAAC,eAAe;sBAACC,OAAO;oBAAA;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzEjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,4BAA4B;oBAACC,OAAO,eAAEzB,OAAA,CAACrE,QAAQ;sBAACiF,EAAE,EAAC,yBAAyB;sBAACC,OAAO;oBAAA;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvGjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,8BAA8B;oBAACC,OAAO,eAAEzB,OAAA,CAACrE,QAAQ;sBAACiF,EAAE,EAAC,qCAAqC;sBAACC,OAAO;oBAAA;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrHjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,cAAc;oBAACC,OAAO,eAAEzB,OAAA,CAACrE,QAAQ;sBAACiF,EAAE,EAAC,4BAA4B;sBAACC,OAAO;oBAAA;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5FjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,gBAAgB;oBAACC,OAAO,eAAEzB,OAAA,CAACrE,QAAQ;sBAACiF,EAAE,EAAC,uBAAuB;sBAACC,OAAO;oBAAA;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzFjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,UAAU;oBAACC,OAAO,eAAEzB,OAAA,CAACN,QAAQ;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,QAAQ;oBAACC,OAAO,eAAEzB,OAAA,CAACL,YAAY;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClDjB,OAAA,CAACvE,KAAK;oBAAC+F,IAAI,EAAC,gBAAgB;oBAACC,OAAO,eAAEzB,OAAA,CAACJ,aAAa;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eAGRjB,OAAA,CAACvE,KAAK;kBACJ+F,IAAI,EAAC,GAAG;kBACRC,OAAO,EACLrB,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,gBAC7BL,OAAA,CAACrE,QAAQ;oBAACiF,EAAE,EAAC,kBAAkB;oBAACC,OAAO;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,GACxCb,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,gBACrEL,OAAA,CAACrE,QAAQ;oBAACiF,EAAE,EAAC,iBAAiB;oBAACC,OAAO;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEzCjB,OAAA,CAACrE,QAAQ;oBAACiF,EAAE,EAAC,QAAQ;oBAACC,OAAO;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAElC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEf;AAACW,GAAA,GAtIQN,GAAG;AAwIZ,eAAeA,GAAG;AAAC,IAAAJ,EAAA,EAAAG,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAX,EAAA;AAAAW,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}