/* Welcome Sidebar Styles */
.welcome-sidebar {
  position: fixed;
  left: 0;
  top: 70px; /* Header height */
  bottom: 0;
  width: 280px;
  background: #1a1b23;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  z-index: 999;
  overflow: hidden;
}

.welcome-sidebar.collapsed {
  width: 80px;
}

/* Header */
.welcome-sidebar__header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.welcome-sidebar__title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #00ff87;
  font-weight: 600;
  font-size: 1.1rem;
}

.welcome-sidebar__title-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.welcome-sidebar.collapsed .welcome-sidebar__title span {
  display: none;
}

/* Content */
.welcome-sidebar__content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
}

/* Loading State */
.welcome-sidebar__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem 1rem;
  color: #888;
}

.welcome-sidebar__loading .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Empty State */
.welcome-sidebar__empty {
  padding: 2rem 1rem;
  text-align: center;
  color: #888;
  font-size: 0.9rem;
}

/* Leagues List */
.welcome-sidebar__leagues {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0 1rem;
}

.welcome-sidebar__league-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  text-decoration: none;
  color: #fff;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.welcome-sidebar__league-item:hover {
  background: rgba(0, 255, 135, 0.1);
  border-color: rgba(0, 255, 135, 0.2);
  transform: translateX(2px);
}

.welcome-sidebar.collapsed .welcome-sidebar__league-item {
  justify-content: center;
  padding: 1rem 0.5rem;
}

/* League Icon */
.welcome-sidebar__league-icon {
  position: relative;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.welcome-sidebar__league-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid rgba(0, 255, 135, 0.3);
}

.welcome-sidebar__league-fallback {
  width: 100%;
  height: 100%;
  background: rgba(0, 255, 135, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00ff87;
  font-size: 1.2rem;
}

/* League Info */
.welcome-sidebar__league-info {
  flex: 1;
  min-width: 0;
}

.welcome-sidebar__league-name {
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.welcome-sidebar__league-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}

.welcome-sidebar__league-range {
  font-size: 0.8rem;
  color: #00ff87;
  font-weight: 500;
}

.welcome-sidebar__league-members {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: #888;
}

/* League Arrow */
.welcome-sidebar__league-arrow {
  color: #888;
  font-size: 0.8rem;
  transition: color 0.2s ease;
}

.welcome-sidebar__league-item:hover .welcome-sidebar__league-arrow {
  color: #00ff87;
}

/* Footer */
.welcome-sidebar__footer {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.welcome-sidebar__view-all {
  display: block;
  width: 100%;
  padding: 0.75rem;
  background: rgba(0, 255, 135, 0.1);
  border: 1px solid rgba(0, 255, 135, 0.2);
  border-radius: 6px;
  text-align: center;
  text-decoration: none;
  color: #00ff87;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.welcome-sidebar__view-all:hover {
  background: rgba(0, 255, 135, 0.2);
  border-color: rgba(0, 255, 135, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .welcome-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .welcome-sidebar.mobile-open {
    transform: translateX(0);
  }
}

@media (max-width: 768px) {
  .welcome-sidebar {
    width: 100%;
    max-width: 320px;
  }
}

/* Custom Scrollbar */
.welcome-sidebar__content::-webkit-scrollbar {
  width: 4px;
}

.welcome-sidebar__content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.welcome-sidebar__content::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 135, 0.3);
  border-radius: 2px;
}

.welcome-sidebar__content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 135, 0.5);
}
