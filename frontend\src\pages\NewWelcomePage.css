/* Fresh Welcome Page - Clean CSS with no conflicts */
/* IMPORTANT: This CSS completely overrides any existing layout styles */

/* Global Reset for this page only */
.fresh-welcome-page,
.fresh-welcome-page *,
.fresh-welcome-page *::before,
.fresh-welcome-page *::after {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Force scrolling to work */
html,
body {
  overflow: auto !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
  height: auto !important;
  min-height: 100vh !important;
}

/* CSS Variables */
:root {
  --fresh-primary: #00ff87;
  --fresh-primary-dark: #00cc6a;
  --fresh-secondary: #409cff;
  --fresh-dark-bg: #13141B;
  --fresh-card-bg: #1a1b23;
  --fresh-border: #2a2b35;
  --fresh-text-primary: #ffffff;
  --fresh-text-secondary: #b0b0b0;
  --fresh-text-muted: #808080;
  --fresh-success: #10b981;
  --fresh-warning: #f59e0b;
  --fresh-error: #ef4444;
  --fresh-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --fresh-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.2);
  --fresh-radius: 8px;
  --fresh-radius-lg: 12px;
  --fresh-transition: all 0.3s ease;
}

/* Reset and Base */
.fresh-welcome-page {
  min-height: 100vh !important;
  height: auto !important;
  background: var(--fresh-dark-bg) !important;
  color: var(--fresh-text-primary) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  line-height: 1.6 !important;
  overflow: visible !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
  position: relative !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

.fresh-welcome-page * {
  box-sizing: border-box !important;
}

/* Override any MainLayout or existing layout styles */
.fresh-welcome-page .main-layout,
.fresh-welcome-page .main-container,
.fresh-welcome-page .admin-layout {
  overflow: visible !important;
  overflow-y: auto !important;
  height: auto !important;
  min-height: auto !important;
  position: static !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure no fixed positioning interferes */
.fresh-welcome-page .main-header,
.fresh-welcome-page .header,
.fresh-welcome-page .sidebar {
  position: static !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Header */
.fresh-header {
  background: var(--fresh-dark-bg);
  border-bottom: 1px solid var(--fresh-border);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.fresh-header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.fresh-logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--fresh-primary);
  text-decoration: none;
  transition: var(--fresh-transition);
}

.fresh-logo:hover {
  color: var(--fresh-primary-dark);
}

.fresh-nav {
  display: flex;
  gap: 2rem;
}

.fresh-nav-link {
  color: var(--fresh-text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: var(--fresh-transition);
  position: relative;
}

.fresh-nav-link:hover,
.fresh-nav-link.active {
  color: var(--fresh-text-primary);
}

.fresh-nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--fresh-primary);
}

.fresh-auth-buttons {
  display: flex;
  gap: 1rem;
}

.fresh-btn {
  padding: 0.5rem 1rem;
  border-radius: var(--fresh-radius);
  text-decoration: none;
  font-weight: 500;
  transition: var(--fresh-transition);
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.fresh-btn-login {
  background: transparent;
  color: var(--fresh-text-primary);
  border: 1px solid var(--fresh-border);
}

.fresh-btn-login:hover {
  background: var(--fresh-border);
}

.fresh-btn-register {
  background: var(--fresh-primary);
  color: var(--fresh-dark-bg);
}

.fresh-btn-register:hover {
  background: var(--fresh-primary-dark);
}

/* Layout */
.fresh-layout {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  gap: 2rem;
  padding: 2rem 1rem;
}

/* Sidebar */
.fresh-sidebar {
  width: 300px;
  background: linear-gradient(135deg, #00ff87 0%, #00cc6a 100%);
  border-radius: var(--fresh-radius-lg);
  padding: 1.5rem;
  height: fit-content;
  position: sticky;
  top: 90px;
  box-shadow: var(--fresh-shadow-lg);
}

.fresh-sidebar-header h3 {
  margin: 0 0 1rem 0;
  color: var(--fresh-dark-bg);
  font-size: 1.2rem;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.fresh-leagues-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.fresh-league-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--fresh-radius);
  transition: var(--fresh-transition);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.fresh-league-item:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.fresh-league-rank {
  font-weight: bold;
  color: var(--fresh-dark-bg);
  min-width: 24px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.fresh-league-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--fresh-radius);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.fresh-league-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.fresh-league-placeholder {
  font-size: 1.2rem;
}

.fresh-league-info {
  flex: 1;
}

.fresh-league-info h4 {
  margin: 0;
  font-size: 0.9rem;
  color: var(--fresh-dark-bg);
  font-weight: 600;
}

.fresh-league-info p {
  margin: 0;
  font-size: 0.8rem;
  color: rgba(19, 20, 27, 0.8);
}

.fresh-league-range {
  font-size: 0.75rem;
  color: rgba(19, 20, 27, 0.7);
  font-weight: 500;
}

.fresh-sidebar-cta {
  display: block;
  width: 100%;
  padding: 0.75rem;
  background: var(--fresh-dark-bg);
  color: var(--fresh-primary);
  text-decoration: none;
  text-align: center;
  border-radius: var(--fresh-radius);
  font-weight: 600;
  transition: var(--fresh-transition);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.fresh-sidebar-cta:hover {
  background: rgba(19, 20, 27, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Main Content */
.fresh-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Hero Section */
.fresh-hero-section {
  width: 100%;
}

.fresh-hero-slider {
  position: relative;
  height: 300px;
  border-radius: var(--fresh-radius-lg);
  overflow: hidden;
  box-shadow: var(--fresh-shadow-lg);
}

.fresh-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.fresh-slide.active {
  opacity: 1;
}

.fresh-slider-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 1rem;
}

.fresh-slider-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--fresh-transition);
}

.fresh-slider-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}

.fresh-slider-indicators {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
}

.fresh-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: var(--fresh-transition);
}

.fresh-indicator.active {
  background: var(--fresh-primary);
}

/* Section Headers */
.fresh-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.fresh-section-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--fresh-text-primary);
  font-weight: bold;
}

.fresh-section-link {
  color: var(--fresh-primary);
  text-decoration: none;
  font-weight: 500;
  transition: var(--fresh-transition);
}

.fresh-section-link:hover {
  color: var(--fresh-primary-dark);
}

/* Loading and Empty States */
.fresh-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--fresh-text-secondary);
  background: var(--fresh-card-bg);
  border-radius: var(--fresh-radius);
}

.fresh-empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--fresh-text-muted);
  background: var(--fresh-card-bg);
  border-radius: var(--fresh-radius);
}

/* Challenges Section */
.fresh-challenges-container {
  width: 100%;
}

.fresh-challenges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.fresh-challenge-card {
  background: var(--fresh-card-bg);
  border-radius: var(--fresh-radius);
  padding: 1.5rem;
  transition: var(--fresh-transition);
  border: 1px solid var(--fresh-border);
}

.fresh-challenge-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--fresh-shadow-lg);
}

.fresh-match-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.fresh-team {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.fresh-team img {
  width: 40px;
  height: 40px;
  border-radius: var(--fresh-radius);
  object-fit: cover;
}

.fresh-team span {
  font-size: 0.9rem;
  color: var(--fresh-text-primary);
  text-align: center;
}

.fresh-vs {
  font-weight: bold;
  color: var(--fresh-text-secondary);
  margin: 0 1rem;
}

.fresh-odds {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.fresh-odd {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  background: var(--fresh-dark-bg);
  border-radius: var(--fresh-radius);
}

.fresh-odd span:first-child {
  font-size: 0.8rem;
  color: var(--fresh-text-secondary);
}

.fresh-odd span:last-child {
  font-weight: bold;
  color: var(--fresh-primary);
}

.fresh-bet-btn {
  width: 100%;
  padding: 0.75rem;
  background: var(--fresh-secondary);
  color: white;
  border: none;
  border-radius: var(--fresh-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--fresh-transition);
}

.fresh-bet-btn:hover {
  background: #3b7dff;
}

/* Recent Bets Section */
.fresh-bets-container {
  width: 100%;
}

.fresh-bets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.fresh-bet-card {
  background: var(--fresh-card-bg);
  border-radius: var(--fresh-radius);
  padding: 1rem;
  border: 1px solid var(--fresh-border);
  transition: var(--fresh-transition);
}

.fresh-bet-card:hover {
  border-color: var(--fresh-primary);
}

.fresh-bet-ref {
  font-size: 0.8rem;
  color: var(--fresh-text-muted);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.fresh-bet-match {
  margin-bottom: 0.5rem;
}

.fresh-bet-teams {
  font-size: 0.85rem;
  color: var(--fresh-primary);
  font-weight: 500;
  text-align: center;
}

.fresh-bet-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.fresh-bet-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.fresh-bet-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  gap: 0.25rem;
}

.fresh-bet-user span:first-child {
  font-weight: 500;
  color: var(--fresh-text-primary);
}

.fresh-bet-user span:nth-child(2) {
  font-size: 0.8rem;
  color: var(--fresh-primary);
  font-weight: 600;
}

.fresh-bet-user small {
  font-size: 0.7rem;
  color: var(--fresh-text-secondary);
  text-transform: capitalize;
}

.fresh-bet-vs {
  margin: 0 1rem;
  color: var(--fresh-text-secondary);
  font-weight: bold;
}

.fresh-bet-status {
  text-align: center;
}

.fresh-status {
  padding: 0.25rem 0.5rem;
  border-radius: var(--fresh-radius);
  font-size: 0.8rem;
  font-weight: 500;
}

.fresh-status.open {
  background: var(--fresh-warning);
  color: white;
}

.fresh-status.completed {
  background: var(--fresh-success);
  color: white;
}

.fresh-status.cancelled {
  background: var(--fresh-error);
  color: white;
}

/* Footer */
.fresh-footer {
  background: var(--fresh-card-bg);
  border-top: 1px solid var(--fresh-border);
  margin-top: 3rem;
}

.fresh-footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem 1rem;
}

.fresh-footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  align-items: start;
}

.fresh-footer-brand h3 {
  margin: 0 0 0.5rem 0;
  color: var(--fresh-primary);
}

.fresh-footer-brand p {
  margin: 0;
  color: var(--fresh-text-secondary);
  font-size: 0.9rem;
}

.fresh-footer-links {
  display: contents;
}

.fresh-footer-column h4 {
  margin: 0 0 1rem 0;
  color: var(--fresh-text-primary);
  font-size: 1rem;
}

.fresh-footer-column a {
  display: block;
  color: var(--fresh-text-secondary);
  text-decoration: none;
  margin-bottom: 0.5rem;
  transition: var(--fresh-transition);
}

.fresh-footer-column a:hover {
  color: var(--fresh-primary);
}

.fresh-footer-social {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.fresh-footer-social a {
  color: var(--fresh-text-secondary);
  text-decoration: none;
  transition: var(--fresh-transition);
}

.fresh-footer-social a:hover {
  color: var(--fresh-primary);
}

.fresh-footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid var(--fresh-border);
}

.fresh-footer-bottom p {
  margin: 0;
  color: var(--fresh-text-muted);
  font-size: 0.9rem;
}

.fresh-footer-badges {
  display: flex;
  gap: 1rem;
}

.fresh-footer-badges span {
  color: var(--fresh-success);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .fresh-layout {
    flex-direction: column;
    gap: 1.5rem;
  }

  .fresh-sidebar {
    width: 100%;
    position: static;
  }

  .fresh-leagues-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .fresh-nav {
    display: none;
  }

  .fresh-header-container {
    padding: 0 1rem;
  }

  .fresh-challenges-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .fresh-footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .fresh-footer-links {
    display: flex;
    gap: 1rem;
  }

  .fresh-footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .fresh-header-container {
    height: 60px;
  }

  .fresh-layout {
    padding: 1rem;
    gap: 1rem;
  }

  .fresh-sidebar {
    padding: 1rem;
  }

  .fresh-leagues-list {
    grid-template-columns: 1fr;
  }

  .fresh-hero-slider {
    height: 200px;
  }

  .fresh-challenges-grid {
    grid-template-columns: 1fr;
  }

  .fresh-bets-grid {
    grid-template-columns: 1fr;
  }

  .fresh-auth-buttons {
    gap: 0.5rem;
  }

  .fresh-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .fresh-section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .fresh-section-header h2 {
    font-size: 1.3rem;
  }

  .fresh-match-info {
    flex-direction: column;
    gap: 1rem;
  }

  .fresh-vs {
    margin: 0;
  }

  .fresh-footer-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .fresh-footer-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .fresh-layout {
    padding: 0.5rem;
  }

  .fresh-header-container {
    padding: 0 0.5rem;
  }

  .fresh-logo {
    font-size: 1.3rem;
  }

  .fresh-sidebar {
    padding: 0.75rem;
  }

  .fresh-challenge-card,
  .fresh-bet-card {
    padding: 1rem;
  }

  .fresh-hero-slider {
    height: 150px;
  }

  .fresh-slider-btn {
    width: 35px;
    height: 35px;
  }

  .fresh-team img {
    width: 30px;
    height: 30px;
  }

  .fresh-team span {
    font-size: 0.8rem;
  }

  .fresh-odds {
    gap: 0.25rem;
  }

  .fresh-odd {
    padding: 0.4rem;
  }

  .fresh-footer-container {
    padding: 1.5rem 0.5rem 1rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .fresh-welcome-page {
    background: var(--fresh-dark-bg);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .fresh-welcome-page {
    --fresh-border: #404040;
    --fresh-text-secondary: #e0e0e0;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .fresh-welcome-page * {
    transition: none !important;
    animation: none !important;
  }

  .fresh-slide {
    transition: none;
  }
}
