{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\WelcomeLayout\\\\WelcomeLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from '../../utils/axiosConfig';\nimport { API_BASE_URL } from '../../config';\nimport WelcomeHeader from './WelcomeHeader';\nimport WelcomeSidebar from './WelcomeSidebar';\nimport WelcomeFooter from './WelcomeFooter';\nimport './WelcomeLayout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeLayout = ({\n  children\n}) => {\n  _s();\n  const [leagues, setLeagues] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  useEffect(() => {\n    fetchTopLeagues();\n  }, []);\n  const fetchTopLeagues = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_leagues.php`);\n      if (response.data.success) {\n        // Get top 7 leagues\n        const topLeagues = response.data.leagues.slice(0, 7);\n        setLeagues(topLeagues);\n      } else {\n        console.error('Failed to fetch leagues:', response.data.message);\n      }\n    } catch (error) {\n      console.error('Error fetching leagues:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"welcome-layout\",\n    children: [/*#__PURE__*/_jsxDEV(WelcomeHeader, {\n      onToggleSidebar: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-layout__body\",\n      children: [/*#__PURE__*/_jsxDEV(WelcomeSidebar, {\n        leagues: leagues,\n        loading: loading,\n        collapsed: sidebarCollapsed,\n        onToggle: toggleSidebar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `welcome-layout__main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-layout__content\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WelcomeFooter, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(WelcomeLayout, \"pqnvGqsSHqgUJ6PmxO1LG7rsxI4=\");\n_c = WelcomeLayout;\nexport default WelcomeLayout;\nvar _c;\n$RefreshReg$(_c, \"WelcomeLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "API_BASE_URL", "Welcome<PERSON><PERSON>er", "WelcomeSidebar", "Welcome<PERSON>ooter", "jsxDEV", "_jsxDEV", "WelcomeLayout", "children", "_s", "leagues", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "sidebarCollapsed", "setSidebarCollapsed", "fetchTopLeagues", "response", "get", "data", "success", "topLeagues", "slice", "console", "error", "message", "toggleSidebar", "className", "onToggleSidebar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "collapsed", "onToggle", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/WelcomeLayout/WelcomeLayout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from '../../utils/axiosConfig';\nimport { API_BASE_URL } from '../../config';\nimport WelcomeHeader from './WelcomeHeader';\nimport WelcomeSidebar from './WelcomeSidebar';\nimport WelcomeFooter from './WelcomeFooter';\nimport './WelcomeLayout.css';\n\nconst WelcomeLayout = ({ children }) => {\n  const [leagues, setLeagues] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n\n  useEffect(() => {\n    fetchTopLeagues();\n  }, []);\n\n  const fetchTopLeagues = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_leagues.php`);\n      if (response.data.success) {\n        // Get top 7 leagues\n        const topLeagues = response.data.leagues.slice(0, 7);\n        setLeagues(topLeagues);\n      } else {\n        console.error('Failed to fetch leagues:', response.data.message);\n      }\n    } catch (error) {\n      console.error('Error fetching leagues:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  return (\n    <div className=\"welcome-layout\">\n      <WelcomeHeader onToggleSidebar={toggleSidebar} />\n      \n      <div className=\"welcome-layout__body\">\n        <WelcomeSidebar \n          leagues={leagues}\n          loading={loading}\n          collapsed={sidebarCollapsed}\n          onToggle={toggleSidebar}\n        />\n        \n        <main className={`welcome-layout__main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>\n          <div className=\"welcome-layout__content\">\n            {children}\n          </div>\n        </main>\n      </div>\n      \n      <WelcomeFooter />\n    </div>\n  );\n};\n\nexport default WelcomeLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,SAASC,YAAY,QAAQ,cAAc;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE/DC,SAAS,CAAC,MAAM;IACdiB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAAC,GAAGjB,YAAY,2BAA2B,CAAC;MAC5E,IAAIgB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB;QACA,MAAMC,UAAU,GAAGJ,QAAQ,CAACE,IAAI,CAACT,OAAO,CAACY,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACpDX,UAAU,CAACU,UAAU,CAAC;MACxB,CAAC,MAAM;QACLE,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEP,QAAQ,CAACE,IAAI,CAACM,OAAO,CAAC;MAClE;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,aAAa,GAAGA,CAAA,KAAM;IAC1BX,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,oBACER,OAAA;IAAKqB,SAAS,EAAC,gBAAgB;IAAAnB,QAAA,gBAC7BF,OAAA,CAACJ,aAAa;MAAC0B,eAAe,EAAEF;IAAc;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEjD1B,OAAA;MAAKqB,SAAS,EAAC,sBAAsB;MAAAnB,QAAA,gBACnCF,OAAA,CAACH,cAAc;QACbO,OAAO,EAAEA,OAAQ;QACjBE,OAAO,EAAEA,OAAQ;QACjBqB,SAAS,EAAEnB,gBAAiB;QAC5BoB,QAAQ,EAAER;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAEF1B,OAAA;QAAMqB,SAAS,EAAE,wBAAwBb,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,EAAG;QAAAN,QAAA,eACrFF,OAAA;UAAKqB,SAAS,EAAC,yBAAyB;UAAAnB,QAAA,EACrCA;QAAQ;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEN1B,OAAA,CAACF,aAAa;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;AAACvB,EAAA,CArDIF,aAAa;AAAA4B,EAAA,GAAb5B,aAAa;AAuDnB,eAAeA,aAAa;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}