/* Simple Soccer Home - Clean and Modern Design */

/* CSS Variables */
:root {
  --primary-green: #00c851;
  --primary-dark: #1a1a1a;
  --secondary-blue: #007bff;
  --text-light: #ffffff;
  --text-dark: #333333;
  --text-muted: #666666;
  --bg-light: #f8f9fa;
  --bg-card: #ffffff;
  --border-color: #e0e0e0;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--bg-light);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
.soccer-header {
  background: var(--primary-dark);
  color: var(--text-light);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow);
}

.soccer-header .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  text-decoration: none;
  color: var(--text-light);
}

.logo-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-green);
}

.nav-menu {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: var(--text-light);
  text-decoration: none;
  padding: 0.5rem 0;
  border-bottom: 2px solid transparent;
  transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-green);
  border-bottom-color: var(--primary-green);
}

.auth-buttons {
  display: flex;
  gap: 1rem;
}

.btn-login {
  color: var(--text-light);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border: 1px solid var(--primary-green);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.btn-login:hover {
  background: var(--primary-green);
  color: var(--text-light);
}

.btn-register {
  background: var(--primary-green);
  color: var(--text-light);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.btn-register:hover {
  background: #00a844;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--primary-dark) 0%, #2c2c2c 100%);
  color: var(--text-light);
  padding: 4rem 0;
  text-align: center;
}

.hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.highlight {
  color: var(--primary-green);
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #cccccc;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-primary {
  background: var(--primary-green);
  color: var(--text-light);
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  transition: var(--transition);
}

.btn-primary:hover {
  background: #00a844;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: var(--text-light);
  text-decoration: none;
  padding: 1rem 2rem;
  border: 2px solid var(--primary-green);
  border-radius: var(--border-radius);
  font-weight: 600;
  transition: var(--transition);
}

.btn-secondary:hover {
  background: var(--primary-green);
  color: var(--text-light);
}

/* Main Content */
.main-content {
  padding: 3rem 0;
}

.section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-header h2 {
  font-size: 2rem;
  color: var(--text-dark);
}

.view-all {
  color: var(--primary-green);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

.view-all:hover {
  color: #00a844;
}

/* Loading */
.loading {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
  font-style: italic;
}

/* Leagues Grid */
.leagues-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.league-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.league-card:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-2px);
}

.league-rank {
  background: var(--primary-green);
  color: var(--text-light);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.league-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-light);
}

.league-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.league-emoji {
  font-size: 1.5rem;
}

.league-info h3 {
  margin-bottom: 0.25rem;
  color: var(--text-dark);
}

.league-info p {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.league-range {
  color: var(--primary-green);
  font-size: 0.8rem;
  font-weight: 600;
}

/* Challenges Grid */
.challenges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.challenge-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.challenge-card:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-2px);
}

.match-teams {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.team {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.team img {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.team span {
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
}

.vs {
  font-weight: bold;
  color: var(--text-muted);
  margin: 0 1rem;
}

.odds-row {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.odd {
  flex: 1;
  background: var(--bg-light);
  padding: 0.75rem;
  border-radius: var(--border-radius);
  text-align: center;
  transition: var(--transition);
}

.odd:hover {
  background: var(--primary-green);
  color: var(--text-light);
}

.odd span:first-child {
  display: block;
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
}

.odd:hover span:first-child {
  color: var(--text-light);
}

.odd-value {
  font-weight: bold;
  font-size: 1.1rem;
}

.bet-button {
  display: block;
  width: 100%;
  background: var(--secondary-blue);
  color: var(--text-light);
  text-decoration: none;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  text-align: center;
  font-weight: 600;
  transition: var(--transition);
}

.bet-button:hover {
  background: #0056b3;
}

/* Bets List */
.bets-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bet-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.bet-card:hover {
  box-shadow: var(--shadow-hover);
}

.bet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.bet-ref {
  font-size: 0.8rem;
  color: var(--text-muted);
  font-weight: 600;
}

.bet-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: bold;
  text-transform: uppercase;
}

.bet-status.completed {
  background: #d4edda;
  color: #155724;
}

.bet-status.open {
  background: #fff3cd;
  color: #856404;
}

.bet-status.joined {
  background: #cce5ff;
  color: #004085;
}

.bet-match {
  margin-bottom: 0.75rem;
  text-align: center;
  color: var(--primary-green);
  font-weight: 600;
}

.bet-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bet-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  flex: 1;
}

.username {
  font-weight: 600;
  color: var(--text-dark);
}

.amount {
  color: var(--primary-green);
  font-weight: bold;
}

.choice {
  font-size: 0.8rem;
  color: var(--text-muted);
  text-transform: capitalize;
}

/* Footer */
.soccer-footer {
  background: var(--primary-dark);
  color: var(--text-light);
  padding: 2rem 0 1rem;
  margin-top: 3rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-brand h3 {
  color: var(--primary-green);
  margin-bottom: 0.5rem;
}

.footer-brand p {
  color: #cccccc;
  margin-bottom: 0.5rem;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.footer-column h4 {
  color: var(--primary-green);
  margin-bottom: 1rem;
}

.footer-column a {
  color: #cccccc;
  text-decoration: none;
  display: block;
  margin-bottom: 0.5rem;
  transition: var(--transition);
}

.footer-column a:hover {
  color: var(--primary-green);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #333;
}

.footer-badges {
  display: flex;
  gap: 1rem;
}

.footer-badges span {
  color: var(--primary-green);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .soccer-header .container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-menu {
    gap: 1rem;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .leagues-grid,
  .challenges-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
  }
  
  .footer-links {
    grid-template-columns: 1fr;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
  }
}
