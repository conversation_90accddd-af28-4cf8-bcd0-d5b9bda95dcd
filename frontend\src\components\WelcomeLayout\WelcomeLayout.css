/* Welcome Layout - Clean, Modern Design */
.welcome-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #13141B;
  color: #fff;
  overflow-x: hidden;
}

/* Layout Body */
.welcome-layout__body {
  display: flex;
  flex: 1;
  min-height: calc(100vh - 140px); /* Account for header and footer */
}

/* Main Content Area */
.welcome-layout__main {
  flex: 1;
  margin-left: 280px; /* Sidebar width */
  transition: margin-left 0.3s ease;
  overflow-y: auto;
  background: #13141B;
}

.welcome-layout__main.sidebar-collapsed {
  margin-left: 80px; /* Collapsed sidebar width */
}

.welcome-layout__content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .welcome-layout__main {
    margin-left: 0;
  }
  
  .welcome-layout__main.sidebar-collapsed {
    margin-left: 0;
  }
  
  .welcome-layout__content {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .welcome-layout__body {
    flex-direction: column;
  }
  
  .welcome-layout__main {
    margin-left: 0;
  }
  
  .welcome-layout__content {
    padding: 0.5rem;
  }
}

/* Smooth scrolling */
.welcome-layout__main {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Ensure proper stacking */
.welcome-layout {
  position: relative;
  z-index: 1;
}

/* Fix any potential overflow issues */
.welcome-layout * {
  box-sizing: border-box;
}
