import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from '../utils/axiosConfig';
import { API_BASE_URL } from '../config';
import './SimpleSoccerHome.css';

const SimpleSoccerHome = () => {
  const [data, setData] = useState({
    leagues: [],
    challenges: [],
    recentBets: []
  });
  const [loading, setLoading] = useState({
    leagues: true,
    challenges: true,
    bets: true
  });

  // Fetch top leagues
  const fetchLeagues = async () => {
    try {
      const response = await axios.get('/handlers/get_leagues.php');
      if (response.data.success && response.data.leagues) {
        setData(prev => ({ ...prev, leagues: response.data.leagues.slice(0, 6) }));
      }
    } catch (err) {
      console.error('Error fetching leagues:', err);
    } finally {
      setLoading(prev => ({ ...prev, leagues: false }));
    }
  };

  // Fetch live challenges
  const fetchChallenges = async () => {
    try {
      const response = await axios.get('/handlers/recent_challenges.php');
      if (response.data.success && response.data.challenges) {
        setData(prev => ({ ...prev, challenges: response.data.challenges.slice(0, 4) }));
      }
    } catch (err) {
      console.error('Error fetching challenges:', err);
    } finally {
      setLoading(prev => ({ ...prev, challenges: false }));
    }
  };

  // Fetch recent bets
  const fetchRecentBets = async () => {
    try {
      const response = await axios.get('/handlers/welcome_recent_bets.php');
      if (response.data.success && response.data.bets) {
        setData(prev => ({ ...prev, recentBets: response.data.bets.slice(0, 4) }));
      }
    } catch (err) {
      console.error('Error fetching recent bets:', err);
    } finally {
      setLoading(prev => ({ ...prev, bets: false }));
    }
  };

  useEffect(() => {
    fetchLeagues();
    fetchChallenges();
    fetchRecentBets();
  }, []);

  const getTeamLogo = (teamName, logoPath = null) => {
    // For now, use a placeholder image service since team logos are causing 404s
    if (teamName) {
      // Use a soccer-themed placeholder with team name
      return `https://via.placeholder.com/40x40/00c851/ffffff?text=${teamName.charAt(0)}`;
    }
    return 'https://via.placeholder.com/40x40/cccccc/ffffff?text=⚽';
  };

  const formatCurrency = (amount) => {
    return parseFloat(amount || 0).toLocaleString('en-US', { minimumFractionDigits: 2 });
  };

  return (
    <div className="soccer-home">
      {/* Header */}
      <header className="soccer-header">
        <div className="container">
          <Link to="/" className="logo">
            <span className="logo-text">FanBet247</span>
          </Link>
          <nav className="nav-menu">
            <Link to="/" className="nav-link active">Home</Link>
            <Link to="/live-challenges" className="nav-link">Live</Link>
            <Link to="/leagues" className="nav-link">Leagues</Link>
            <Link to="/leaderboard" className="nav-link">Leaders</Link>
            <Link to="/about" className="nav-link">About</Link>
          </nav>
          <div className="auth-buttons">
            <Link to="/login" className="btn-login">Login</Link>
            <Link to="/register" className="btn-register">Register</Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <h1 className="hero-title">
            Welcome to <span className="highlight">FanBet247</span>
          </h1>
          <p className="hero-subtitle">
            The ultimate soccer betting platform for passionate fans
          </p>
          <div className="hero-actions">
            <Link to="/register" className="btn-primary">Get Started</Link>
            <Link to="/live-challenges" className="btn-secondary">View Live Matches</Link>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="main-content">
        <div className="container">
          
          {/* Top Leagues Section */}
          <section className="section">
            <div className="section-header">
              <h2>Top Leagues</h2>
              <Link to="/leagues" className="view-all">View All</Link>
            </div>
            <div className="leagues-grid">
              {loading.leagues ? (
                <div className="loading">Loading leagues...</div>
              ) : (
                data.leagues.map((league, index) => (
                  <div key={league.league_id} className="league-card">
                    <div className="league-rank">#{index + 1}</div>
                    <div className="league-icon">
                      <span className="league-emoji">🏆</span>
                    </div>
                    <div className="league-info">
                      <h3>{league.name}</h3>
                      <p>{league.member_count || 0} members</p>
                      <span className="league-range">
                        {formatCurrency(league.min_bet)} - {formatCurrency(league.max_bet)} FC
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </section>

          {/* Live Challenges Section */}
          <section className="section">
            <div className="section-header">
              <h2>Live Challenges</h2>
              <Link to="/live-challenges" className="view-all">View All</Link>
            </div>
            <div className="challenges-grid">
              {loading.challenges ? (
                <div className="loading">Loading challenges...</div>
              ) : (
                data.challenges.map(challenge => (
                  <div key={challenge.challenge_id} className="challenge-card">
                    <div className="match-teams">
                      <div className="team">
                        <img 
                          src={getTeamLogo(challenge.team_a, challenge.team_a_logo)} 
                          alt={challenge.team_a}
                          onError={(e) => e.target.src = '/default-team.png'}
                        />
                        <span>{challenge.team_a}</span>
                      </div>
                      <div className="vs">VS</div>
                      <div className="team">
                        <img 
                          src={getTeamLogo(challenge.team_b, challenge.team_b_logo)} 
                          alt={challenge.team_b}
                          onError={(e) => e.target.src = '/default-team.png'}
                        />
                        <span>{challenge.team_b}</span>
                      </div>
                    </div>
                    <div className="odds-row">
                      <div className="odd">
                        <span>Home</span>
                        <span className="odd-value">{challenge.odds_team_a}</span>
                      </div>
                      <div className="odd">
                        <span>Draw</span>
                        <span className="odd-value">{challenge.odds_draw}</span>
                      </div>
                      <div className="odd">
                        <span>Away</span>
                        <span className="odd-value">{challenge.odds_team_b}</span>
                      </div>
                    </div>
                    <Link to="/login" className="bet-button">Login to Bet</Link>
                  </div>
                ))
              )}
            </div>
          </section>

          {/* Recent Bets Section */}
          <section className="section">
            <div className="section-header">
              <h2>Recent Bets</h2>
              <Link to="/user/recent-bets" className="view-all">View All</Link>
            </div>
            <div className="bets-list">
              {loading.bets ? (
                <div className="loading">Loading recent bets...</div>
              ) : (
                data.recentBets.map(bet => (
                  <div key={bet.bet_id} className="bet-card">
                    <div className="bet-header">
                      <span className="bet-ref">REF: {bet.unique_code || bet.bet_id}</span>
                      <span className={`bet-status ${bet.bet_status}`}>
                        {bet.bet_status.toUpperCase()}
                      </span>
                    </div>
                    {bet.team_a && bet.team_b && (
                      <div className="bet-match">
                        <span>{bet.team_a} vs {bet.team_b}</span>
                      </div>
                    )}
                    <div className="bet-details">
                      <div className="bet-user">
                        <span className="username">{bet.user1_name}</span>
                        <span className="amount">{formatCurrency(bet.amount_user1 || bet.amount)} FC</span>
                        <span className="choice">{bet.bet_choice_user1}</span>
                      </div>
                      <div className="vs">VS</div>
                      <div className="bet-user">
                        <span className="username">{bet.user2_name || 'Open'}</span>
                        <span className="amount">
                          {bet.user2_name ? formatCurrency(bet.amount_user2 || bet.amount) : 'Waiting'} FC
                        </span>
                        <span className="choice">{bet.bet_choice_user2 || 'N/A'}</span>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </section>

        </div>
      </main>

      {/* Footer */}
      <footer className="soccer-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-brand">
              <h3>FanBet247</h3>
              <p>The ultimate soccer betting platform</p>
              <p><strong>18+ Bet Responsibly</strong></p>
            </div>
            <div className="footer-links">
              <div className="footer-column">
                <h4>Quick Links</h4>
                <Link to="/live-challenges">Live Matches</Link>
                <Link to="/leagues">All Leagues</Link>
                <Link to="/leaderboard">Leaderboard</Link>
              </div>
              <div className="footer-column">
                <h4>Support</h4>
                <Link to="/help">Help Center</Link>
                <Link to="/terms">Terms</Link>
                <Link to="/privacy">Privacy</Link>
              </div>
              <div className="footer-column">
                <h4>Connect</h4>
                <a href="https://twitter.com/fanbet247" target="_blank" rel="noopener noreferrer">Twitter</a>
                <a href="https://facebook.com/fanbet247" target="_blank" rel="noopener noreferrer">Facebook</a>
                <a href="mailto:<EMAIL>">Support</a>
              </div>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2024 FanBet247. All rights reserved.</p>
            <div className="footer-badges">
              <span>✓ SSL Secured</span>
              <span>✓ Licensed</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SimpleSoccerHome;
