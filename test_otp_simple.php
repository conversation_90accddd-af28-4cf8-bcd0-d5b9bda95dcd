<?php
header('Content-Type: text/plain');

echo "🧪 SIMPLE OTP FLOW TEST\n";
echo "=======================\n\n";

require_once 'backend/includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Test user credentials
    $testUsername = 'demohomexx';
    $testPassword = 'loving12';
    
    echo "📋 Step 1: Verify user credentials and OTP settings\n";
    echo "---------------------------------------------------\n";
    
    // Check user credentials
    $stmt = $conn->prepare("SELECT user_id, username, email, password, otp_enabled, tfa_enabled, auth_method FROM users WHERE username = ?");
    $stmt->execute([$testUsername]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "❌ User not found\n";
        exit;
    }
    
    // Verify password
    if (!password_verify($testPassword, $user['password'])) {
        echo "❌ Invalid password\n";
        exit;
    }
    
    echo "✅ User found and password verified\n";
    echo "   Username: {$user['username']}\n";
    echo "   Email: {$user['email']}\n";
    echo "   OTP Enabled: " . ($user['otp_enabled'] ? 'Yes' : 'No') . "\n";
    echo "   Auth Method: {$user['auth_method']}\n\n";
    
    if ($user['auth_method'] === 'password_otp' || $user['auth_method'] === 'password_otp_2fa') {
        echo "📋 Step 2: Generate and send OTP\n";
        echo "--------------------------------\n";
        
        // Generate OTP
        $otp = sprintf('%06d', mt_rand(0, 999999));
        $expiresAt = date('Y-m-d H:i:s', time() + 300); // 5 minutes
        
        // Store OTP
        $stmt = $conn->prepare("
            INSERT INTO user_otp (user_id, otp, expiry) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            otp = VALUES(otp), 
            expiry = VALUES(expiry),
            attempts = 0,
            used = 0
        ");
        $stmt->execute([$user['user_id'], $otp, $expiresAt]);
        
        echo "✅ OTP generated and stored\n";
        echo "   OTP Code: {$otp}\n";
        echo "   Expires: {$expiresAt}\n\n";
        
        echo "📋 Step 3: Test OTP verification\n";
        echo "--------------------------------\n";
        
        // Verify OTP
        $stmt = $conn->prepare("
            SELECT id, otp, expiry, attempts, used 
            FROM user_otp 
            WHERE user_id = ? AND used = 0 AND expiry > NOW()
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$user['user_id']]);
        $otpRecord = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$otpRecord) {
            echo "❌ No valid OTP found\n";
            exit;
        }
        
        if ($otpRecord['otp'] === $otp) {
            // Mark OTP as used
            $stmt = $conn->prepare("UPDATE user_otp SET used = 1 WHERE id = ?");
            $stmt->execute([$otpRecord['id']]);
            
            echo "✅ OTP verification successful\n";
            echo "   OTP marked as used\n\n";
            
            echo "🎉 COMPLETE OTP FLOW TEST PASSED!\n\n";
            
            echo "📝 WHAT THIS MEANS:\n";
            echo "-------------------\n";
            echo "1. ✅ User has OTP enabled\n";
            echo "2. ✅ OTP generation works\n";
            echo "3. ✅ OTP storage works\n";
            echo "4. ✅ OTP verification works\n";
            echo "5. ✅ Database schema is correct\n\n";
            
            echo "📱 FRONTEND SHOULD NOW WORK:\n";
            echo "----------------------------\n";
            echo "1. Login with username: {$testUsername}\n";
            echo "2. Password: {$testPassword}\n";
            echo "3. System should require OTP\n";
            echo "4. Check email for OTP code\n\n";
            
        } else {
            echo "❌ OTP verification failed\n";
            echo "   Expected: {$otp}\n";
            echo "   Found: {$otpRecord['otp']}\n";
        }
        
    } else {
        echo "❌ User does not have OTP authentication enabled\n";
        echo "   Current auth method: {$user['auth_method']}\n";
        echo "   Expected: password_otp or password_otp_2fa\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
