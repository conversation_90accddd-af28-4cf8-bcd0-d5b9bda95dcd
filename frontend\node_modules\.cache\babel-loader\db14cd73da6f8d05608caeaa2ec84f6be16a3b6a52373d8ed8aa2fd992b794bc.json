{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\WelcomeLayout\\\\WelcomeLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from '../../utils/axiosConfig';\nimport { API_BASE_URL } from '../../config';\nimport WelcomeHeader from './WelcomeHeader';\nimport WelcomeSidebar from './WelcomeSidebar';\nimport WelcomeFooter from './WelcomeFooter';\nimport './WelcomeLayout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeLayout = ({\n  children\n}) => {\n  _s();\n  const [leagues, setLeagues] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  useEffect(() => {\n    fetchTopLeagues();\n  }, []);\n  const fetchTopLeagues = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('get_leagues.php');\n      if (response.data.success) {\n        // Get top 7 leagues\n        const topLeagues = response.data.leagues.slice(0, 7);\n        setLeagues(topLeagues);\n      } else {\n        console.error('Failed to fetch leagues:', response.data.message);\n        setMockLeagues();\n      }\n    } catch (error) {\n      console.error('Error fetching leagues:', error);\n      // Fallback: use mock data for demonstration\n      setMockLeagues();\n    } finally {\n      setLoading(false);\n    }\n  };\n  const setMockLeagues = () => {\n    const mockLeagues = [{\n      league_id: 1,\n      name: 'Premier League',\n      min_bet_amount: 1000,\n      max_bet_amount: 10000,\n      member_count: 150,\n      icon_path: null\n    }, {\n      league_id: 2,\n      name: 'Champions League',\n      min_bet_amount: 5000,\n      max_bet_amount: 50000,\n      member_count: 89,\n      icon_path: null\n    }, {\n      league_id: 3,\n      name: 'Europa League',\n      min_bet_amount: 2000,\n      max_bet_amount: 20000,\n      member_count: 67,\n      icon_path: null\n    }];\n    setLeagues(mockLeagues);\n  };\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"welcome-layout\",\n    children: [/*#__PURE__*/_jsxDEV(WelcomeHeader, {\n      onToggleSidebar: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-layout__body\",\n      children: [/*#__PURE__*/_jsxDEV(WelcomeSidebar, {\n        leagues: leagues,\n        loading: loading,\n        collapsed: sidebarCollapsed,\n        onToggle: toggleSidebar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `welcome-layout__main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-layout__content\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WelcomeFooter, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(WelcomeLayout, \"pqnvGqsSHqgUJ6PmxO1LG7rsxI4=\");\n_c = WelcomeLayout;\nexport default WelcomeLayout;\nvar _c;\n$RefreshReg$(_c, \"WelcomeLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "API_BASE_URL", "Welcome<PERSON><PERSON>er", "WelcomeSidebar", "Welcome<PERSON>ooter", "jsxDEV", "_jsxDEV", "WelcomeLayout", "children", "_s", "leagues", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "sidebarCollapsed", "setSidebarCollapsed", "fetchTopLeagues", "response", "get", "data", "success", "topLeagues", "slice", "console", "error", "message", "setMockLeagues", "mockLeagues", "league_id", "name", "min_bet_amount", "max_bet_amount", "member_count", "icon_path", "toggleSidebar", "className", "onToggleSidebar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "collapsed", "onToggle", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/WelcomeLayout/WelcomeLayout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from '../../utils/axiosConfig';\nimport { API_BASE_URL } from '../../config';\nimport WelcomeHeader from './WelcomeHeader';\nimport WelcomeSidebar from './WelcomeSidebar';\nimport WelcomeFooter from './WelcomeFooter';\nimport './WelcomeLayout.css';\n\nconst WelcomeLayout = ({ children }) => {\n  const [leagues, setLeagues] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n\n  useEffect(() => {\n    fetchTopLeagues();\n  }, []);\n\n  const fetchTopLeagues = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('get_leagues.php');\n      if (response.data.success) {\n        // Get top 7 leagues\n        const topLeagues = response.data.leagues.slice(0, 7);\n        setLeagues(topLeagues);\n      } else {\n        console.error('Failed to fetch leagues:', response.data.message);\n        setMockLeagues();\n      }\n    } catch (error) {\n      console.error('Error fetching leagues:', error);\n      // Fallback: use mock data for demonstration\n      setMockLeagues();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const setMockLeagues = () => {\n    const mockLeagues = [\n      {\n        league_id: 1,\n        name: 'Premier League',\n        min_bet_amount: 1000,\n        max_bet_amount: 10000,\n        member_count: 150,\n        icon_path: null\n      },\n      {\n        league_id: 2,\n        name: 'Champions League',\n        min_bet_amount: 5000,\n        max_bet_amount: 50000,\n        member_count: 89,\n        icon_path: null\n      },\n      {\n        league_id: 3,\n        name: 'Europa League',\n        min_bet_amount: 2000,\n        max_bet_amount: 20000,\n        member_count: 67,\n        icon_path: null\n      }\n    ];\n    setLeagues(mockLeagues);\n  };\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  return (\n    <div className=\"welcome-layout\">\n      <WelcomeHeader onToggleSidebar={toggleSidebar} />\n      \n      <div className=\"welcome-layout__body\">\n        <WelcomeSidebar \n          leagues={leagues}\n          loading={loading}\n          collapsed={sidebarCollapsed}\n          onToggle={toggleSidebar}\n        />\n        \n        <main className={`welcome-layout__main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>\n          <div className=\"welcome-layout__content\">\n            {children}\n          </div>\n        </main>\n      </div>\n      \n      <WelcomeFooter />\n    </div>\n  );\n};\n\nexport default WelcomeLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,SAASC,YAAY,QAAQ,cAAc;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE/DC,SAAS,CAAC,MAAM;IACdiB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAAC,iBAAiB,CAAC;MACnD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB;QACA,MAAMC,UAAU,GAAGJ,QAAQ,CAACE,IAAI,CAACT,OAAO,CAACY,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACpDX,UAAU,CAACU,UAAU,CAAC;MACxB,CAAC,MAAM;QACLE,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEP,QAAQ,CAACE,IAAI,CAACM,OAAO,CAAC;QAChEC,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;MACAE,cAAc,CAAC,CAAC;IAClB,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,WAAW,GAAG,CAClB;MACEC,SAAS,EAAE,CAAC;MACZC,IAAI,EAAE,gBAAgB;MACtBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,KAAK;MACrBC,YAAY,EAAE,GAAG;MACjBC,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,CAAC;MACZC,IAAI,EAAE,kBAAkB;MACxBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,KAAK;MACrBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,CAAC;MACZC,IAAI,EAAE,eAAe;MACrBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,KAAK;MACrBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CACF;IACDtB,UAAU,CAACgB,WAAW,CAAC;EACzB,CAAC;EAED,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1BnB,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,oBACER,OAAA;IAAK6B,SAAS,EAAC,gBAAgB;IAAA3B,QAAA,gBAC7BF,OAAA,CAACJ,aAAa;MAACkC,eAAe,EAAEF;IAAc;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEjDlC,OAAA;MAAK6B,SAAS,EAAC,sBAAsB;MAAA3B,QAAA,gBACnCF,OAAA,CAACH,cAAc;QACbO,OAAO,EAAEA,OAAQ;QACjBE,OAAO,EAAEA,OAAQ;QACjB6B,SAAS,EAAE3B,gBAAiB;QAC5B4B,QAAQ,EAAER;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAEFlC,OAAA;QAAM6B,SAAS,EAAE,wBAAwBrB,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,EAAG;QAAAN,QAAA,eACrFF,OAAA;UAAK6B,SAAS,EAAC,yBAAyB;UAAA3B,QAAA,EACrCA;QAAQ;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENlC,OAAA,CAACF,aAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAtFIF,aAAa;AAAAoC,EAAA,GAAbpC,aAAa;AAwFnB,eAAeA,aAAa;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}