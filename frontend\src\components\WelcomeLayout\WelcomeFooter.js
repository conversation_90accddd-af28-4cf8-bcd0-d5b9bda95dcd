import React from 'react';
import { Link } from 'react-router-dom';
import { FaTwitter, FaFacebook, FaTelegram, FaShieldAlt, FaCertificate } from 'react-icons/fa';
import './WelcomeFooter.css';

const WelcomeFooter = () => {
  return (
    <footer className="welcome-footer">
      <div className="welcome-footer__container">
        {/* Main Footer Content */}
        <div className="welcome-footer__main">
          {/* Brand Section */}
          <div className="welcome-footer__brand">
            <div className="welcome-footer__logo">
              <h3>FanBet247</h3>
              <div className="welcome-footer__tagline">
                <span className="welcome-footer__age">18+</span>
                <p>Bet Responsibly</p>
              </div>
            </div>
            <div className="welcome-footer__features">
              <span>Live Betting</span>
              <span>•</span>
              <span>Best Odds</span>
              <span>•</span>
              <span>Fast Payouts</span>
            </div>
          </div>

          {/* Links Section */}
          <div className="welcome-footer__links">
            <div className="welcome-footer__link-group">
              <Link to="/live-challenges" className="welcome-footer__link">
                Live Matches
              </Link>
              <Link to="/upcoming-matches" className="welcome-footer__link">
                Upcoming
              </Link>
              <Link to="/leaderboard" className="welcome-footer__link">
                Leaderboard
              </Link>
            </div>
            <div className="welcome-footer__link-group">
              <Link to="/help" className="welcome-footer__link">
                Help Center
              </Link>
              <Link to="/responsible-gambling" className="welcome-footer__link">
                Responsible Gaming
              </Link>
              <Link to="/terms" className="welcome-footer__link">
                Terms
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="welcome-footer__bottom">
          {/* Social Links */}
          <div className="welcome-footer__social">
            <a 
              href="https://twitter.com/fanbet247" 
              target="_blank" 
              rel="noopener noreferrer"
              className="welcome-footer__social-link"
              aria-label="Follow us on Twitter"
            >
              <FaTwitter />
            </a>
            <a 
              href="https://facebook.com/fanbet247" 
              target="_blank" 
              rel="noopener noreferrer"
              className="welcome-footer__social-link"
              aria-label="Follow us on Facebook"
            >
              <FaFacebook />
            </a>
            <a 
              href="https://t.me/fanbet247" 
              target="_blank" 
              rel="noopener noreferrer"
              className="welcome-footer__social-link"
              aria-label="Join our Telegram"
            >
              <FaTelegram />
            </a>
          </div>

          {/* Security Badges */}
          <div className="welcome-footer__security">
            <div className="welcome-footer__badge">
              <FaShieldAlt />
              <span>SSL Secured</span>
            </div>
            <div className="welcome-footer__badge">
              <FaCertificate />
              <span>Licensed</span>
            </div>
          </div>

          {/* Copyright */}
          <div className="welcome-footer__copyright">
            <p>2024 FanBet247. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default WelcomeFooter;
