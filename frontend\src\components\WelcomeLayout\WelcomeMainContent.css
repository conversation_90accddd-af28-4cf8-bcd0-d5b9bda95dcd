/* Welcome Main Content Styles */
.welcome-main-content {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* Hero Section */
.welcome-main-content__hero {
  width: 100%;
  margin-bottom: 1rem;
}

/* Section Styles */
.welcome-main-content__section {
  width: 100%;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.welcome-main-content__section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.welcome-main-content__section-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #fff;
  margin: 0;
  letter-spacing: 0.5px;
}

.welcome-main-content__section-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.welcome-main-content__user-welcome {
  color: #00ff87;
  font-weight: 500;
  font-size: 0.95rem;
}

.welcome-main-content__section-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(0, 255, 135, 0.1);
  border: 1px solid rgba(0, 255, 135, 0.2);
  border-radius: 6px;
  color: #00ff87;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.welcome-main-content__section-link:hover {
  background: rgba(0, 255, 135, 0.2);
  border-color: rgba(0, 255, 135, 0.3);
  transform: translateY(-1px);
}

.welcome-main-content__section-link.login-link {
  background: #00ff87;
  color: #000;
  border-color: #00ff87;
}

.welcome-main-content__section-link.login-link:hover {
  background: #00e676;
  border-color: #00e676;
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-main-content {
    gap: 2rem;
  }
  
  .welcome-main-content__section {
    padding: 1.5rem;
    border-radius: 8px;
  }
  
  .welcome-main-content__section-header {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 1.5rem;
  }
  
  .welcome-main-content__section-title {
    font-size: 1.3rem;
  }
  
  .welcome-main-content__section-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .welcome-main-content__user-welcome {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .welcome-main-content {
    gap: 1.5rem;
  }
  
  .welcome-main-content__section {
    padding: 1rem;
    border-radius: 6px;
  }
  
  .welcome-main-content__section-title {
    font-size: 1.2rem;
  }
  
  .welcome-main-content__section-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .welcome-main-content__section-link {
    text-align: center;
    justify-content: center;
  }
}

/* Ensure proper spacing and layout */
.welcome-main-content > * {
  flex-shrink: 0;
}

/* Override any conflicting styles */
.welcome-main-content .hero,
.welcome-main-content .challenges-list,
.welcome-main-content .recent-bets-grid {
  width: 100%;
  max-width: 100%;
}

/* Error alert styling within main content */
.welcome-main-content .error-alert {
  margin-bottom: 2rem;
}
