{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserLogin.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { useNavigate, Link } from 'react-router-dom';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction UserLogin() {\n  _s();\n  const [credentials, setCredentials] = useState({\n    usernameOrEmail: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [showOtpVerification, setShowOtpVerification] = useState(false);\n  const [showGoogleAuthVerification, setShowGoogleAuthVerification] = useState(false);\n  const [otpCode, setOtpCode] = useState('');\n  const [googleAuthCode, setGoogleAuthCode] = useState('');\n  const [userEmail, setUserEmail] = useState('');\n  const [otpExpiry, setOtpExpiry] = useState(null);\n  const [otpCountdown, setOtpCountdown] = useState(0);\n  const [otpResending, setOtpResending] = useState(false);\n  const navigate = useNavigate();\n\n  // Redirect if already logged in\n  useEffect(() => {\n    const userId = localStorage.getItem('userId');\n    if (userId) {\n      navigate('/user/dashboard');\n    }\n  }, [navigate]);\n\n  // OTP countdown timer\n  useEffect(() => {\n    let timer;\n    if (otpCountdown > 0) {\n      timer = setTimeout(() => setOtpCountdown(otpCountdown - 1), 1000);\n    }\n    return () => clearTimeout(timer);\n  }, [otpCountdown]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setCredentials(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('user_login_enhanced.php', credentials);\n      if (response.data.success) {\n        // Check if additional authentication is required\n        if (response.data.requiresAdditionalAuth) {\n          // Store user data for additional auth steps\n          setUserEmail(response.data.email);\n          if (response.data.authType === 'otp' || response.data.authType === 'email_otp') {\n            // Send OTP first, then show verification form\n            await sendOtpCode(response.data.userId, response.data.email);\n            setShowOtpVerification(true);\n            setOtpExpiry(300); // 5 minutes\n            setOtpCountdown(300);\n          } else if (response.data.authType === '2fa' || response.data.authType === 'google_auth') {\n            // Show Google Authenticator verification form\n            setShowGoogleAuthVerification(true);\n          } else if (response.data.authType === 'otp_2fa') {\n            // Start with OTP, then 2FA\n            await sendOtpCode(response.data.userId, response.data.email);\n            setShowOtpVerification(true);\n            setOtpExpiry(300); // 5 minutes\n            setOtpCountdown(300);\n          }\n        } else {\n          // No additional auth required, proceed with login\n          localStorage.setItem('userId', response.data.userId);\n          localStorage.setItem('username', response.data.username);\n\n          // Get redirect path or default to dashboard\n          const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n          sessionStorage.removeItem('redirectAfterLogin');\n          navigate(redirectPath);\n        }\n      } else {\n        setError(response.data.message || 'Login failed. Please try again.');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response3;\n      console.error('Login error:', err);\n      if ((_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.message) {\n        setError(err.response.data.message);\n      } else if (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401) {\n        setError('Invalid username/email or password');\n      } else if (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 400) {\n        setError('Please enter both username/email and password');\n      } else {\n        setError('An error occurred. Please try again later.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleOtpVerification = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('verify_otp.php', {\n        email: userEmail,\n        otp: otpCode\n      });\n      if (response.data.success) {\n        localStorage.setItem('userId', response.data.userId);\n        localStorage.setItem('username', response.data.username);\n\n        // Get redirect path or default to dashboard\n        const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n        sessionStorage.removeItem('redirectAfterLogin');\n        navigate(redirectPath);\n      } else {\n        setError(response.data.message || 'OTP verification failed. Please try again.');\n      }\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      console.error('OTP verification error:', err);\n      if ((_err$response4 = err.response) !== null && _err$response4 !== void 0 && (_err$response4$data = _err$response4.data) !== null && _err$response4$data !== void 0 && _err$response4$data.message) {\n        setError(err.response.data.message);\n      } else {\n        setError('An error occurred during OTP verification. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleGoogleAuthVerification = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('verify_google_auth.php', {\n        email: userEmail,\n        code: googleAuthCode\n      });\n      if (response.data.success) {\n        localStorage.setItem('userId', response.data.userId);\n        localStorage.setItem('username', response.data.username);\n\n        // Get redirect path or default to dashboard\n        const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n        sessionStorage.removeItem('redirectAfterLogin');\n        navigate(redirectPath);\n      } else {\n        setError(response.data.message || 'Verification failed. Please try again.');\n      }\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      console.error('Google Auth verification error:', err);\n      if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.message) {\n        setError(err.response.data.message);\n      } else {\n        setError('An error occurred during verification. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleResendOtp = async () => {\n    setError('');\n    setOtpResending(true);\n    try {\n      const response = await axios.post('send_otp.php', {\n        email: userEmail\n      });\n      if (response.data.success) {\n        setOtpExpiry(response.data.expiresIn);\n        setOtpCountdown(response.data.expiresIn);\n        setError(''); // Clear any previous errors\n      } else {\n        setError(response.data.message || 'Failed to resend OTP. Please try again.');\n      }\n    } catch (err) {\n      var _err$response6, _err$response6$data;\n      console.error('Resend OTP error:', err);\n      if ((_err$response6 = err.response) !== null && _err$response6 !== void 0 && (_err$response6$data = _err$response6.data) !== null && _err$response6$data !== void 0 && _err$response6$data.message) {\n        setError(err.response.data.message);\n      } else {\n        setError('An error occurred while resending OTP. Please try again.');\n      }\n    } finally {\n      setOtpResending(false);\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;\n  };\n\n  // Render OTP verification form\n  if (showOtpVerification) {\n    return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n      title: \"OTP Verification\",\n      subtitle: \"Enter the one-time password sent to your email\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 27\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleOtpVerification,\n        className: \"user-auth-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"otpCode\",\n            children: \"OTP Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-auth-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"otpCode\",\n              name: \"otpCode\",\n              value: otpCode,\n              onChange: e => setOtpCode(e.target.value),\n              placeholder: \"Enter 6-digit OTP code\",\n              required: true,\n              autoComplete: \"one-time-code\",\n              className: \"user-auth-otp-input\",\n              maxLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-key\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 21\n        }, this), otpCountdown > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-countdown-timer\",\n          children: [\"OTP expires in: \", formatTime(otpCountdown)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"user-auth-button\",\n          disabled: isLoading,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-auth-loading-spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 33\n            }, this), \"Verifying...\"]\n          }, void 0, true) : 'Verify OTP'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-options center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"user-auth-resend-button\",\n            onClick: handleResendOtp,\n            disabled: otpResending || otpCountdown > 0,\n            children: otpResending ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"user-auth-loading-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 37\n              }, this), \"Resending...\"]\n            }, void 0, true) : 'Resend OTP'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"user-auth-button-secondary\",\n            onClick: () => {\n              setShowOtpVerification(false);\n              setOtpCode('');\n              setError('');\n            },\n            children: \"Back to Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Render Google Authenticator verification form\n  if (showGoogleAuthVerification) {\n    return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n      title: \"Two-Factor Authentication\",\n      subtitle: \"Enter the code from your Google Authenticator app\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 27\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleGoogleAuthVerification,\n        className: \"user-auth-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"googleAuthCode\",\n            children: \"Authentication Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-auth-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"googleAuthCode\",\n              name: \"googleAuthCode\",\n              value: googleAuthCode,\n              onChange: e => setGoogleAuthCode(e.target.value),\n              placeholder: \"Enter 6-digit code\",\n              required: true,\n              autoComplete: \"one-time-code\",\n              className: \"user-auth-otp-input\",\n              maxLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-shield-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-auth-countdown-timer\",\n            children: \"Open your Google Authenticator app and enter the 6-digit code for FanBet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"user-auth-button\",\n          disabled: isLoading,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-auth-loading-spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 33\n            }, this), \"Verifying...\"]\n          }, void 0, true) : 'Verify Code'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"user-auth-button-secondary\",\n            onClick: () => {\n              setShowGoogleAuthVerification(false);\n              setGoogleAuthCode('');\n              setError('');\n            },\n            children: \"Back to Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Render main login form\n  return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n    title: \"Welcome Back\",\n    subtitle: \"Sign in to your account\",\n    variant: \"login\",\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-auth-error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 23\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"user-auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"usernameOrEmail\",\n          children: \"Email or Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"usernameOrEmail\",\n            name: \"usernameOrEmail\",\n            value: credentials.usernameOrEmail,\n            onChange: handleInputChange,\n            placeholder: \"Enter your email or username\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: credentials.password,\n            onChange: handleInputChange,\n            placeholder: \"Enter your password\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-lock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-options end\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/forgot-password\",\n          className: \"user-auth-forgot-password\",\n          children: \"Forgot Password?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"user-auth-button\",\n        disabled: isLoading,\n        children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-auth-loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 29\n          }, this), \"Signing in...\"]\n        }, void 0, true) : 'Sign In'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"user-auth-register-link\",\n            children: \"Create one here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 47\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 357,\n    columnNumber: 9\n  }, this);\n}\n_s(UserLogin, \"0FSFqBU7L5g0HrMXSXaVjx0W4hg=\", false, function () {\n  return [useNavigate];\n});\n_c = UserLogin;\nexport default UserLogin;\nvar _c;\n$RefreshReg$(_c, \"UserLogin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useNavigate", "Link", "UserAuthLayout", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserLogin", "_s", "credentials", "setCredentials", "usernameOrEmail", "password", "error", "setError", "isLoading", "setIsLoading", "showOtpVerification", "setShowOtpVerification", "showGoogleAuthVerification", "setShowGoogleAuthVerification", "otpCode", "setOtpCode", "googleAuthCode", "setGoogleAuthCode", "userEmail", "setUserEmail", "otpExpiry", "setOtpExpiry", "otpCountdown", "setOtpCountdown", "otpResending", "setOtpResending", "navigate", "userId", "localStorage", "getItem", "timer", "setTimeout", "clearTimeout", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "response", "post", "data", "success", "requiresAdditionalAuth", "email", "authType", "sendOtpCode", "setItem", "username", "redirectPath", "sessionStorage", "removeItem", "message", "err", "_err$response", "_err$response$data", "_err$response2", "_err$response3", "console", "status", "handleOtpVerification", "otp", "_err$response4", "_err$response4$data", "handleGoogleAuthVerification", "code", "_err$response5", "_err$response5$data", "handleResendOtp", "expiresIn", "_err$response6", "_err$response6$data", "formatTime", "seconds", "mins", "Math", "floor", "secs", "title", "subtitle", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "required", "autoComplete", "max<PERSON><PERSON><PERSON>", "disabled", "onClick", "variant", "to", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserLogin.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { useNavigate, Link } from 'react-router-dom';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\n\nfunction UserLogin() {\n    const [credentials, setCredentials] = useState({ usernameOrEmail: '', password: '' });\n    const [error, setError] = useState('');\n    const [isLoading, setIsLoading] = useState(false);\n    const [showOtpVerification, setShowOtpVerification] = useState(false);\n    const [showGoogleAuthVerification, setShowGoogleAuthVerification] = useState(false);\n    const [otpCode, setOtpCode] = useState('');\n    const [googleAuthCode, setGoogleAuthCode] = useState('');\n    const [userEmail, setUserEmail] = useState('');\n    const [otpExpiry, setOtpExpiry] = useState(null);\n    const [otpCountdown, setOtpCountdown] = useState(0);\n    const [otpResending, setOtpResending] = useState(false);\n    const navigate = useNavigate();\n\n    // Redirect if already logged in\n    useEffect(() => {\n        const userId = localStorage.getItem('userId');\n        if (userId) {\n            navigate('/user/dashboard');\n        }\n    }, [navigate]);\n\n    // OTP countdown timer\n    useEffect(() => {\n        let timer;\n        if (otpCountdown > 0) {\n            timer = setTimeout(() => setOtpCountdown(otpCountdown - 1), 1000);\n        }\n        return () => clearTimeout(timer);\n    }, [otpCountdown]);\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setCredentials(prev => ({ ...prev, [name]: value }));\n        if (error) setError('');\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n\n        try {\n            const response = await axios.post(\n                'user_login_enhanced.php',\n                credentials\n            );\n\n            if (response.data.success) {\n                // Check if additional authentication is required\n                if (response.data.requiresAdditionalAuth) {\n                    // Store user data for additional auth steps\n                    setUserEmail(response.data.email);\n\n                    if (response.data.authType === 'otp' || response.data.authType === 'email_otp') {\n                        // Send OTP first, then show verification form\n                        await sendOtpCode(response.data.userId, response.data.email);\n                        setShowOtpVerification(true);\n                        setOtpExpiry(300); // 5 minutes\n                        setOtpCountdown(300);\n                    } else if (response.data.authType === '2fa' || response.data.authType === 'google_auth') {\n                        // Show Google Authenticator verification form\n                        setShowGoogleAuthVerification(true);\n                    } else if (response.data.authType === 'otp_2fa') {\n                        // Start with OTP, then 2FA\n                        await sendOtpCode(response.data.userId, response.data.email);\n                        setShowOtpVerification(true);\n                        setOtpExpiry(300); // 5 minutes\n                        setOtpCountdown(300);\n                    }\n                } else {\n                    // No additional auth required, proceed with login\n                    localStorage.setItem('userId', response.data.userId);\n                    localStorage.setItem('username', response.data.username);\n\n                    // Get redirect path or default to dashboard\n                    const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n                    sessionStorage.removeItem('redirectAfterLogin');\n                    navigate(redirectPath);\n                }\n            } else {\n                setError(response.data.message || 'Login failed. Please try again.');\n            }\n        } catch (err) {\n            console.error('Login error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else if (err.response?.status === 401) {\n                setError('Invalid username/email or password');\n            } else if (err.response?.status === 400) {\n                setError('Please enter both username/email and password');\n            } else {\n                setError('An error occurred. Please try again later.');\n            }\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const handleOtpVerification = async (e) => {\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n\n        try {\n            const response = await axios.post(\n                'verify_otp.php',\n                {\n                    email: userEmail,\n                    otp: otpCode\n                }\n            );\n\n            if (response.data.success) {\n                localStorage.setItem('userId', response.data.userId);\n                localStorage.setItem('username', response.data.username);\n\n                // Get redirect path or default to dashboard\n                const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n                sessionStorage.removeItem('redirectAfterLogin');\n                navigate(redirectPath);\n            } else {\n                setError(response.data.message || 'OTP verification failed. Please try again.');\n            }\n        } catch (err) {\n            console.error('OTP verification error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else {\n                setError('An error occurred during OTP verification. Please try again.');\n            }\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const handleGoogleAuthVerification = async (e) => {\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n\n        try {\n            const response = await axios.post(\n                'verify_google_auth.php',\n                {\n                    email: userEmail,\n                    code: googleAuthCode\n                }\n            );\n\n            if (response.data.success) {\n                localStorage.setItem('userId', response.data.userId);\n                localStorage.setItem('username', response.data.username);\n\n                // Get redirect path or default to dashboard\n                const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n                sessionStorage.removeItem('redirectAfterLogin');\n                navigate(redirectPath);\n            } else {\n                setError(response.data.message || 'Verification failed. Please try again.');\n            }\n        } catch (err) {\n            console.error('Google Auth verification error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else {\n                setError('An error occurred during verification. Please try again.');\n            }\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const handleResendOtp = async () => {\n        setError('');\n        setOtpResending(true);\n\n        try {\n            const response = await axios.post(\n                'send_otp.php',\n                {\n                    email: userEmail\n                }\n            );\n\n            if (response.data.success) {\n                setOtpExpiry(response.data.expiresIn);\n                setOtpCountdown(response.data.expiresIn);\n                setError(''); // Clear any previous errors\n            } else {\n                setError(response.data.message || 'Failed to resend OTP. Please try again.');\n            }\n        } catch (err) {\n            console.error('Resend OTP error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else {\n                setError('An error occurred while resending OTP. Please try again.');\n            }\n        } finally {\n            setOtpResending(false);\n        }\n    };\n\n    const formatTime = (seconds) => {\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs < 10 ? '0' : ''}${secs}`;\n    };\n\n    // Render OTP verification form\n    if (showOtpVerification) {\n        return (\n            <UserAuthLayout\n                title=\"OTP Verification\"\n                subtitle=\"Enter the one-time password sent to your email\"\n            >\n                {error && <div className=\"user-auth-error-message\">{error}</div>}\n                <form onSubmit={handleOtpVerification} className=\"user-auth-form\">\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"otpCode\">OTP Code</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                type=\"text\"\n                                id=\"otpCode\"\n                                name=\"otpCode\"\n                                value={otpCode}\n                                onChange={(e) => setOtpCode(e.target.value)}\n                                placeholder=\"Enter 6-digit OTP code\"\n                                required\n                                autoComplete=\"one-time-code\"\n                                className=\"user-auth-otp-input\"\n                                maxLength=\"6\"\n                            />\n                            <i className=\"fas fa-key\"></i>\n                        </div>\n                    </div>\n                    {otpCountdown > 0 && (\n                        <div className=\"user-auth-countdown-timer\">\n                            OTP expires in: {formatTime(otpCountdown)}\n                        </div>\n                    )}\n                    <button type=\"submit\" className=\"user-auth-button\" disabled={isLoading}>\n                        {isLoading ? (\n                            <>\n                                <span className=\"user-auth-loading-spinner\"></span>\n                                Verifying...\n                            </>\n                        ) : (\n                            'Verify OTP'\n                        )}\n                    </button>\n                    <div className=\"user-auth-form-options center\">\n                        <button\n                            type=\"button\"\n                            className=\"user-auth-resend-button\"\n                            onClick={handleResendOtp}\n                            disabled={otpResending || otpCountdown > 0}\n                        >\n                            {otpResending ? (\n                                <>\n                                    <span className=\"user-auth-loading-spinner\"></span>\n                                    Resending...\n                                </>\n                            ) : (\n                                'Resend OTP'\n                            )}\n                        </button>\n                    </div>\n                    <div className=\"user-auth-footer\">\n                        <button\n                            type=\"button\"\n                            className=\"user-auth-button-secondary\"\n                            onClick={() => {\n                                setShowOtpVerification(false);\n                                setOtpCode('');\n                                setError('');\n                            }}\n                        >\n                            Back to Login\n                        </button>\n                    </div>\n                </form>\n            </UserAuthLayout>\n        );\n    }\n\n    // Render Google Authenticator verification form\n    if (showGoogleAuthVerification) {\n        return (\n            <UserAuthLayout\n                title=\"Two-Factor Authentication\"\n                subtitle=\"Enter the code from your Google Authenticator app\"\n            >\n                {error && <div className=\"user-auth-error-message\">{error}</div>}\n                <form onSubmit={handleGoogleAuthVerification} className=\"user-auth-form\">\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"googleAuthCode\">Authentication Code</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                type=\"text\"\n                                id=\"googleAuthCode\"\n                                name=\"googleAuthCode\"\n                                value={googleAuthCode}\n                                onChange={(e) => setGoogleAuthCode(e.target.value)}\n                                placeholder=\"Enter 6-digit code\"\n                                required\n                                autoComplete=\"one-time-code\"\n                                className=\"user-auth-otp-input\"\n                                maxLength=\"6\"\n                            />\n                            <i className=\"fas fa-shield-alt\"></i>\n                        </div>\n                    </div>\n                    <div className=\"user-auth-form-group\">\n                        <div className=\"user-auth-countdown-timer\">\n                            Open your Google Authenticator app and enter the 6-digit code for FanBet247\n                        </div>\n                    </div>\n                    <button type=\"submit\" className=\"user-auth-button\" disabled={isLoading}>\n                        {isLoading ? (\n                            <>\n                                <span className=\"user-auth-loading-spinner\"></span>\n                                Verifying...\n                            </>\n                        ) : (\n                            'Verify Code'\n                        )}\n                    </button>\n                    <div className=\"user-auth-footer\">\n                        <button\n                            type=\"button\"\n                            className=\"user-auth-button-secondary\"\n                            onClick={() => {\n                                setShowGoogleAuthVerification(false);\n                                setGoogleAuthCode('');\n                                setError('');\n                            }}\n                        >\n                            Back to Login\n                        </button>\n                    </div>\n                </form>\n            </UserAuthLayout>\n        );\n    }\n\n    // Render main login form\n    return (\n        <UserAuthLayout\n            title=\"Welcome Back\"\n            subtitle=\"Sign in to your account\"\n            variant=\"login\"\n        >\n            {error && <div className=\"user-auth-error-message\">{error}</div>}\n            <form onSubmit={handleSubmit} className=\"user-auth-form\">\n                <div className=\"user-auth-form-group\">\n                    <label htmlFor=\"usernameOrEmail\">Email or Username</label>\n                    <div className=\"user-auth-input-wrapper\">\n                        <input\n                            type=\"text\"\n                            id=\"usernameOrEmail\"\n                            name=\"usernameOrEmail\"\n                            value={credentials.usernameOrEmail}\n                            onChange={handleInputChange}\n                            placeholder=\"Enter your email or username\"\n                            required\n                        />\n                        <i className=\"fas fa-user\"></i>\n                    </div>\n                </div>\n                <div className=\"user-auth-form-group\">\n                    <label htmlFor=\"password\">Password</label>\n                    <div className=\"user-auth-input-wrapper\">\n                        <input\n                            type=\"password\"\n                            id=\"password\"\n                            name=\"password\"\n                            value={credentials.password}\n                            onChange={handleInputChange}\n                            placeholder=\"Enter your password\"\n                            required\n                        />\n                        <i className=\"fas fa-lock\"></i>\n                    </div>\n                </div>\n                <div className=\"user-auth-form-options end\">\n                    <Link to=\"/forgot-password\" className=\"user-auth-forgot-password\">\n                        Forgot Password?\n                    </Link>\n                </div>\n                <button type=\"submit\" className=\"user-auth-button\" disabled={isLoading}>\n                    {isLoading ? (\n                        <>\n                            <span className=\"user-auth-loading-spinner\"></span>\n                            Signing in...\n                        </>\n                    ) : (\n                        'Sign In'\n                    )}\n                </button>\n                <div className=\"user-auth-footer\">\n                    <p>Don't have an account? <Link to=\"/register\" className=\"user-auth-register-link\">Create one here</Link></p>\n                </div>\n            </form>\n        </UserAuthLayout>\n    );\n}\n\nexport default UserLogin;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAO,wBAAwB;AAC/B,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC;IAAEc,eAAe,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EACrF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMoC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACZ,MAAMoC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,IAAIF,MAAM,EAAE;MACRD,QAAQ,CAAC,iBAAiB,CAAC;IAC/B;EACJ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACAnC,SAAS,CAAC,MAAM;IACZ,IAAIuC,KAAK;IACT,IAAIR,YAAY,GAAG,CAAC,EAAE;MAClBQ,KAAK,GAAGC,UAAU,CAAC,MAAMR,eAAe,CAACD,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;IACrE;IACA,OAAO,MAAMU,YAAY,CAACF,KAAK,CAAC;EACpC,CAAC,EAAE,CAACR,YAAY,CAAC,CAAC;EAElB,MAAMW,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClC,cAAc,CAACmC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;IACpD,IAAI9B,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EAC3B,CAAC;EAED,MAAMgC,YAAY,GAAG,MAAOL,CAAC,IAAK;IAC9BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBjC,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAMgC,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,IAAI,CAC7B,yBAAyB,EACzBxC,WACJ,CAAC;MAED,IAAIuC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB;QACA,IAAIH,QAAQ,CAACE,IAAI,CAACE,sBAAsB,EAAE;UACtC;UACA1B,YAAY,CAACsB,QAAQ,CAACE,IAAI,CAACG,KAAK,CAAC;UAEjC,IAAIL,QAAQ,CAACE,IAAI,CAACI,QAAQ,KAAK,KAAK,IAAIN,QAAQ,CAACE,IAAI,CAACI,QAAQ,KAAK,WAAW,EAAE;YAC5E;YACA,MAAMC,WAAW,CAACP,QAAQ,CAACE,IAAI,CAAChB,MAAM,EAAEc,QAAQ,CAACE,IAAI,CAACG,KAAK,CAAC;YAC5DnC,sBAAsB,CAAC,IAAI,CAAC;YAC5BU,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YACnBE,eAAe,CAAC,GAAG,CAAC;UACxB,CAAC,MAAM,IAAIkB,QAAQ,CAACE,IAAI,CAACI,QAAQ,KAAK,KAAK,IAAIN,QAAQ,CAACE,IAAI,CAACI,QAAQ,KAAK,aAAa,EAAE;YACrF;YACAlC,6BAA6B,CAAC,IAAI,CAAC;UACvC,CAAC,MAAM,IAAI4B,QAAQ,CAACE,IAAI,CAACI,QAAQ,KAAK,SAAS,EAAE;YAC7C;YACA,MAAMC,WAAW,CAACP,QAAQ,CAACE,IAAI,CAAChB,MAAM,EAAEc,QAAQ,CAACE,IAAI,CAACG,KAAK,CAAC;YAC5DnC,sBAAsB,CAAC,IAAI,CAAC;YAC5BU,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YACnBE,eAAe,CAAC,GAAG,CAAC;UACxB;QACJ,CAAC,MAAM;UACH;UACAK,YAAY,CAACqB,OAAO,CAAC,QAAQ,EAAER,QAAQ,CAACE,IAAI,CAAChB,MAAM,CAAC;UACpDC,YAAY,CAACqB,OAAO,CAAC,UAAU,EAAER,QAAQ,CAACE,IAAI,CAACO,QAAQ,CAAC;;UAExD;UACA,MAAMC,YAAY,GAAGC,cAAc,CAACvB,OAAO,CAAC,oBAAoB,CAAC,IAAI,iBAAiB;UACtFuB,cAAc,CAACC,UAAU,CAAC,oBAAoB,CAAC;UAC/C3B,QAAQ,CAACyB,YAAY,CAAC;QAC1B;MACJ,CAAC,MAAM;QACH5C,QAAQ,CAACkC,QAAQ,CAACE,IAAI,CAACW,OAAO,IAAI,iCAAiC,CAAC;MACxE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,cAAA;MACVC,OAAO,CAACtD,KAAK,CAAC,cAAc,EAAEiD,GAAG,CAAC;MAClC,KAAAC,aAAA,GAAID,GAAG,CAACd,QAAQ,cAAAe,aAAA,gBAAAC,kBAAA,GAAZD,aAAA,CAAcb,IAAI,cAAAc,kBAAA,eAAlBA,kBAAA,CAAoBH,OAAO,EAAE;QAC7B/C,QAAQ,CAACgD,GAAG,CAACd,QAAQ,CAACE,IAAI,CAACW,OAAO,CAAC;MACvC,CAAC,MAAM,IAAI,EAAAI,cAAA,GAAAH,GAAG,CAACd,QAAQ,cAAAiB,cAAA,uBAAZA,cAAA,CAAcG,MAAM,MAAK,GAAG,EAAE;QACrCtD,QAAQ,CAAC,oCAAoC,CAAC;MAClD,CAAC,MAAM,IAAI,EAAAoD,cAAA,GAAAJ,GAAG,CAACd,QAAQ,cAAAkB,cAAA,uBAAZA,cAAA,CAAcE,MAAM,MAAK,GAAG,EAAE;QACrCtD,QAAQ,CAAC,+CAA+C,CAAC;MAC7D,CAAC,MAAM;QACHA,QAAQ,CAAC,4CAA4C,CAAC;MAC1D;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMqD,qBAAqB,GAAG,MAAO5B,CAAC,IAAK;IACvCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBjC,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAMgC,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,IAAI,CAC7B,gBAAgB,EAChB;QACII,KAAK,EAAE5B,SAAS;QAChB6C,GAAG,EAAEjD;MACT,CACJ,CAAC;MAED,IAAI2B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBhB,YAAY,CAACqB,OAAO,CAAC,QAAQ,EAAER,QAAQ,CAACE,IAAI,CAAChB,MAAM,CAAC;QACpDC,YAAY,CAACqB,OAAO,CAAC,UAAU,EAAER,QAAQ,CAACE,IAAI,CAACO,QAAQ,CAAC;;QAExD;QACA,MAAMC,YAAY,GAAGC,cAAc,CAACvB,OAAO,CAAC,oBAAoB,CAAC,IAAI,iBAAiB;QACtFuB,cAAc,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC/C3B,QAAQ,CAACyB,YAAY,CAAC;MAC1B,CAAC,MAAM;QACH5C,QAAQ,CAACkC,QAAQ,CAACE,IAAI,CAACW,OAAO,IAAI,4CAA4C,CAAC;MACnF;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAS,cAAA,EAAAC,mBAAA;MACVL,OAAO,CAACtD,KAAK,CAAC,yBAAyB,EAAEiD,GAAG,CAAC;MAC7C,KAAAS,cAAA,GAAIT,GAAG,CAACd,QAAQ,cAAAuB,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcrB,IAAI,cAAAsB,mBAAA,eAAlBA,mBAAA,CAAoBX,OAAO,EAAE;QAC7B/C,QAAQ,CAACgD,GAAG,CAACd,QAAQ,CAACE,IAAI,CAACW,OAAO,CAAC;MACvC,CAAC,MAAM;QACH/C,QAAQ,CAAC,8DAA8D,CAAC;MAC5E;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMyD,4BAA4B,GAAG,MAAOhC,CAAC,IAAK;IAC9CA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBjC,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAMgC,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,IAAI,CAC7B,wBAAwB,EACxB;QACII,KAAK,EAAE5B,SAAS;QAChBiD,IAAI,EAAEnD;MACV,CACJ,CAAC;MAED,IAAIyB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBhB,YAAY,CAACqB,OAAO,CAAC,QAAQ,EAAER,QAAQ,CAACE,IAAI,CAAChB,MAAM,CAAC;QACpDC,YAAY,CAACqB,OAAO,CAAC,UAAU,EAAER,QAAQ,CAACE,IAAI,CAACO,QAAQ,CAAC;;QAExD;QACA,MAAMC,YAAY,GAAGC,cAAc,CAACvB,OAAO,CAAC,oBAAoB,CAAC,IAAI,iBAAiB;QACtFuB,cAAc,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC/C3B,QAAQ,CAACyB,YAAY,CAAC;MAC1B,CAAC,MAAM;QACH5C,QAAQ,CAACkC,QAAQ,CAACE,IAAI,CAACW,OAAO,IAAI,wCAAwC,CAAC;MAC/E;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAa,cAAA,EAAAC,mBAAA;MACVT,OAAO,CAACtD,KAAK,CAAC,iCAAiC,EAAEiD,GAAG,CAAC;MACrD,KAAAa,cAAA,GAAIb,GAAG,CAACd,QAAQ,cAAA2B,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAczB,IAAI,cAAA0B,mBAAA,eAAlBA,mBAAA,CAAoBf,OAAO,EAAE;QAC7B/C,QAAQ,CAACgD,GAAG,CAACd,QAAQ,CAACE,IAAI,CAACW,OAAO,CAAC;MACvC,CAAC,MAAM;QACH/C,QAAQ,CAAC,0DAA0D,CAAC;MACxE;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAM6D,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC/D,QAAQ,CAAC,EAAE,CAAC;IACZkB,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACA,MAAMgB,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,IAAI,CAC7B,cAAc,EACd;QACII,KAAK,EAAE5B;MACX,CACJ,CAAC;MAED,IAAIuB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBvB,YAAY,CAACoB,QAAQ,CAACE,IAAI,CAAC4B,SAAS,CAAC;QACrChD,eAAe,CAACkB,QAAQ,CAACE,IAAI,CAAC4B,SAAS,CAAC;QACxChE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,MAAM;QACHA,QAAQ,CAACkC,QAAQ,CAACE,IAAI,CAACW,OAAO,IAAI,yCAAyC,CAAC;MAChF;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAiB,cAAA,EAAAC,mBAAA;MACVb,OAAO,CAACtD,KAAK,CAAC,mBAAmB,EAAEiD,GAAG,CAAC;MACvC,KAAAiB,cAAA,GAAIjB,GAAG,CAACd,QAAQ,cAAA+B,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAc7B,IAAI,cAAA8B,mBAAA,eAAlBA,mBAAA,CAAoBnB,OAAO,EAAE;QAC7B/C,QAAQ,CAACgD,GAAG,CAACd,QAAQ,CAACE,IAAI,CAACW,OAAO,CAAC;MACvC,CAAC,MAAM;QACH/C,QAAQ,CAAC,0DAA0D,CAAC;MACxE;IACJ,CAAC,SAAS;MACNkB,eAAe,CAAC,KAAK,CAAC;IAC1B;EACJ,CAAC;EAED,MAAMiD,UAAU,GAAIC,OAAO,IAAK;IAC5B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAGA,IAAI,EAAE;EACnD,CAAC;;EAED;EACA,IAAIrE,mBAAmB,EAAE;IACrB,oBACIb,OAAA,CAACF,cAAc;MACXqF,KAAK,EAAC,kBAAkB;MACxBC,QAAQ,EAAC,gDAAgD;MAAAC,QAAA,GAExD5E,KAAK,iBAAIT,OAAA;QAAKsF,SAAS,EAAC,yBAAyB;QAAAD,QAAA,EAAE5E;MAAK;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChE1F,OAAA;QAAM2F,QAAQ,EAAE1B,qBAAsB;QAACqB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7DrF,OAAA;UAAKsF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCrF,OAAA;YAAO4F,OAAO,EAAC,SAAS;YAAAP,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzC1F,OAAA;YAAKsF,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBACpCrF,OAAA;cACI6F,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,SAAS;cACZxD,IAAI,EAAC,SAAS;cACdC,KAAK,EAAEtB,OAAQ;cACf8E,QAAQ,EAAG1D,CAAC,IAAKnB,UAAU,CAACmB,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAC5CyD,WAAW,EAAC,wBAAwB;cACpCC,QAAQ;cACRC,YAAY,EAAC,eAAe;cAC5BZ,SAAS,EAAC,qBAAqB;cAC/Ba,SAAS,EAAC;YAAG;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACF1F,OAAA;cAAGsF,SAAS,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACLjE,YAAY,GAAG,CAAC,iBACbzB,OAAA;UAAKsF,SAAS,EAAC,2BAA2B;UAAAD,QAAA,GAAC,kBACvB,EAACR,UAAU,CAACpD,YAAY,CAAC;QAAA;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACR,eACD1F,OAAA;UAAQ6F,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,kBAAkB;UAACc,QAAQ,EAAEzF,SAAU;UAAA0E,QAAA,EAClE1E,SAAS,gBACNX,OAAA,CAAAE,SAAA;YAAAmF,QAAA,gBACIrF,OAAA;cAAMsF,SAAS,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,gBAEvD;UAAA,eAAE,CAAC,GAEH;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACT1F,OAAA;UAAKsF,SAAS,EAAC,+BAA+B;UAAAD,QAAA,eAC1CrF,OAAA;YACI6F,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,yBAAyB;YACnCe,OAAO,EAAE5B,eAAgB;YACzB2B,QAAQ,EAAEzE,YAAY,IAAIF,YAAY,GAAG,CAAE;YAAA4D,QAAA,EAE1C1D,YAAY,gBACT3B,OAAA,CAAAE,SAAA;cAAAmF,QAAA,gBACIrF,OAAA;gBAAMsF,SAAS,EAAC;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,gBAEvD;YAAA,eAAE,CAAC,GAEH;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACN1F,OAAA;UAAKsF,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC7BrF,OAAA;YACI6F,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,4BAA4B;YACtCe,OAAO,EAAEA,CAAA,KAAM;cACXvF,sBAAsB,CAAC,KAAK,CAAC;cAC7BI,UAAU,CAAC,EAAE,CAAC;cACdR,QAAQ,CAAC,EAAE,CAAC;YAChB,CAAE;YAAA2E,QAAA,EACL;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAEzB;;EAEA;EACA,IAAI3E,0BAA0B,EAAE;IAC5B,oBACIf,OAAA,CAACF,cAAc;MACXqF,KAAK,EAAC,2BAA2B;MACjCC,QAAQ,EAAC,mDAAmD;MAAAC,QAAA,GAE3D5E,KAAK,iBAAIT,OAAA;QAAKsF,SAAS,EAAC,yBAAyB;QAAAD,QAAA,EAAE5E;MAAK;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChE1F,OAAA;QAAM2F,QAAQ,EAAEtB,4BAA6B;QAACiB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBACpErF,OAAA;UAAKsF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCrF,OAAA;YAAO4F,OAAO,EAAC,gBAAgB;YAAAP,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3D1F,OAAA;YAAKsF,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBACpCrF,OAAA;cACI6F,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,gBAAgB;cACnBxD,IAAI,EAAC,gBAAgB;cACrBC,KAAK,EAAEpB,cAAe;cACtB4E,QAAQ,EAAG1D,CAAC,IAAKjB,iBAAiB,CAACiB,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cACnDyD,WAAW,EAAC,oBAAoB;cAChCC,QAAQ;cACRC,YAAY,EAAC,eAAe;cAC5BZ,SAAS,EAAC,qBAAqB;cAC/Ba,SAAS,EAAC;YAAG;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACF1F,OAAA;cAAGsF,SAAS,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN1F,OAAA;UAAKsF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,eACjCrF,OAAA;YAAKsF,SAAS,EAAC,2BAA2B;YAAAD,QAAA,EAAC;UAE3C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN1F,OAAA;UAAQ6F,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,kBAAkB;UAACc,QAAQ,EAAEzF,SAAU;UAAA0E,QAAA,EAClE1E,SAAS,gBACNX,OAAA,CAAAE,SAAA;YAAAmF,QAAA,gBACIrF,OAAA;cAAMsF,SAAS,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,gBAEvD;UAAA,eAAE,CAAC,GAEH;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACT1F,OAAA;UAAKsF,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC7BrF,OAAA;YACI6F,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,4BAA4B;YACtCe,OAAO,EAAEA,CAAA,KAAM;cACXrF,6BAA6B,CAAC,KAAK,CAAC;cACpCI,iBAAiB,CAAC,EAAE,CAAC;cACrBV,QAAQ,CAAC,EAAE,CAAC;YAChB,CAAE;YAAA2E,QAAA,EACL;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAEzB;;EAEA;EACA,oBACI1F,OAAA,CAACF,cAAc;IACXqF,KAAK,EAAC,cAAc;IACpBC,QAAQ,EAAC,yBAAyB;IAClCkB,OAAO,EAAC,OAAO;IAAAjB,QAAA,GAEd5E,KAAK,iBAAIT,OAAA;MAAKsF,SAAS,EAAC,yBAAyB;MAAAD,QAAA,EAAE5E;IAAK;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAChE1F,OAAA;MAAM2F,QAAQ,EAAEjD,YAAa;MAAC4C,SAAS,EAAC,gBAAgB;MAAAD,QAAA,gBACpDrF,OAAA;QAAKsF,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBACjCrF,OAAA;UAAO4F,OAAO,EAAC,iBAAiB;UAAAP,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1D1F,OAAA;UAAKsF,SAAS,EAAC,yBAAyB;UAAAD,QAAA,gBACpCrF,OAAA;YACI6F,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,iBAAiB;YACpBxD,IAAI,EAAC,iBAAiB;YACtBC,KAAK,EAAElC,WAAW,CAACE,eAAgB;YACnCwF,QAAQ,EAAE3D,iBAAkB;YAC5B4D,WAAW,EAAC,8BAA8B;YAC1CC,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACF1F,OAAA;YAAGsF,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN1F,OAAA;QAAKsF,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBACjCrF,OAAA;UAAO4F,OAAO,EAAC,UAAU;UAAAP,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C1F,OAAA;UAAKsF,SAAS,EAAC,yBAAyB;UAAAD,QAAA,gBACpCrF,OAAA;YACI6F,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbxD,IAAI,EAAC,UAAU;YACfC,KAAK,EAAElC,WAAW,CAACG,QAAS;YAC5BuF,QAAQ,EAAE3D,iBAAkB;YAC5B4D,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACF1F,OAAA;YAAGsF,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN1F,OAAA;QAAKsF,SAAS,EAAC,4BAA4B;QAAAD,QAAA,eACvCrF,OAAA,CAACH,IAAI;UAAC0G,EAAE,EAAC,kBAAkB;UAACjB,SAAS,EAAC,2BAA2B;UAAAD,QAAA,EAAC;QAElE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN1F,OAAA;QAAQ6F,IAAI,EAAC,QAAQ;QAACP,SAAS,EAAC,kBAAkB;QAACc,QAAQ,EAAEzF,SAAU;QAAA0E,QAAA,EAClE1E,SAAS,gBACNX,OAAA,CAAAE,SAAA;UAAAmF,QAAA,gBACIrF,OAAA;YAAMsF,SAAS,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,iBAEvD;QAAA,eAAE,CAAC,GAEH;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACT1F,OAAA;QAAKsF,SAAS,EAAC,kBAAkB;QAAAD,QAAA,eAC7BrF,OAAA;UAAAqF,QAAA,GAAG,yBAAuB,eAAArF,OAAA,CAACH,IAAI;YAAC0G,EAAE,EAAC,WAAW;YAACjB,SAAS,EAAC,yBAAyB;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEzB;AAACtF,EAAA,CAvZQD,SAAS;EAAA,QAYGP,WAAW;AAAA;AAAA4G,EAAA,GAZvBrG,SAAS;AAyZlB,eAAeA,SAAS;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}