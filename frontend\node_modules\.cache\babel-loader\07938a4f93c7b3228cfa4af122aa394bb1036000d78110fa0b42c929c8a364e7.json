{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\WelcomeLayout\\\\WelcomeFooter.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTwitter, FaFacebook, FaTelegram, FaShieldAlt, FaCertificate } from 'react-icons/fa';\nimport './WelcomeFooter.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeFooter = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"welcome-footer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-footer__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-footer__main\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-footer__brand\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-footer__logo\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"FanBet247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"welcome-footer__tagline\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"welcome-footer__age\",\n                children: \"18+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 17,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Bet Responsibly\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 18,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-footer__features\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Live Betting\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Best Odds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Fast Payouts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-footer__links\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-footer__link-group\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/live-challenges\",\n              className: \"welcome-footer__link\",\n              children: \"Live Matches\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/upcoming-matches\",\n              className: \"welcome-footer__link\",\n              children: \"Upcoming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/leaderboard\",\n              className: \"welcome-footer__link\",\n              children: \"Leaderboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-footer__link-group\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/help\",\n              className: \"welcome-footer__link\",\n              children: \"Help Center\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/responsible-gambling\",\n              className: \"welcome-footer__link\",\n              children: \"Responsible Gaming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/terms\",\n              className: \"welcome-footer__link\",\n              children: \"Terms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-footer__bottom\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-footer__social\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://twitter.com/fanbet247\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"welcome-footer__social-link\",\n            \"aria-label\": \"Follow us on Twitter\",\n            children: /*#__PURE__*/_jsxDEV(FaTwitter, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://facebook.com/fanbet247\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"welcome-footer__social-link\",\n            \"aria-label\": \"Follow us on Facebook\",\n            children: /*#__PURE__*/_jsxDEV(FaFacebook, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://t.me/fanbet247\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"welcome-footer__social-link\",\n            \"aria-label\": \"Join our Telegram\",\n            children: /*#__PURE__*/_jsxDEV(FaTelegram, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-footer__security\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-footer__badge\",\n            children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"SSL Secured\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"welcome-footer__badge\",\n            children: [/*#__PURE__*/_jsxDEV(FaCertificate, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Licensed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-footer__copyright\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"2024 FanBet247. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = WelcomeFooter;\nexport default WelcomeFooter;\nvar _c;\n$RefreshReg$(_c, \"WelcomeFooter\");", "map": {"version": 3, "names": ["React", "Link", "FaTwitter", "FaFacebook", "FaTelegram", "FaShieldAlt", "FaCertificate", "jsxDEV", "_jsxDEV", "Welcome<PERSON>ooter", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/WelcomeLayout/WelcomeFooter.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaTwitter, FaFacebook, FaTelegram, FaShieldAlt, FaCertificate } from 'react-icons/fa';\nimport './WelcomeFooter.css';\n\nconst WelcomeFooter = () => {\n  return (\n    <footer className=\"welcome-footer\">\n      <div className=\"welcome-footer__container\">\n        {/* Main Footer Content */}\n        <div className=\"welcome-footer__main\">\n          {/* Brand Section */}\n          <div className=\"welcome-footer__brand\">\n            <div className=\"welcome-footer__logo\">\n              <h3>FanBet247</h3>\n              <div className=\"welcome-footer__tagline\">\n                <span className=\"welcome-footer__age\">18+</span>\n                <p>Bet Responsibly</p>\n              </div>\n            </div>\n            <div className=\"welcome-footer__features\">\n              <span>Live Betting</span>\n              <span>•</span>\n              <span>Best Odds</span>\n              <span>•</span>\n              <span>Fast Payouts</span>\n            </div>\n          </div>\n\n          {/* Links Section */}\n          <div className=\"welcome-footer__links\">\n            <div className=\"welcome-footer__link-group\">\n              <Link to=\"/live-challenges\" className=\"welcome-footer__link\">\n                Live Matches\n              </Link>\n              <Link to=\"/upcoming-matches\" className=\"welcome-footer__link\">\n                Upcoming\n              </Link>\n              <Link to=\"/leaderboard\" className=\"welcome-footer__link\">\n                Leaderboard\n              </Link>\n            </div>\n            <div className=\"welcome-footer__link-group\">\n              <Link to=\"/help\" className=\"welcome-footer__link\">\n                Help Center\n              </Link>\n              <Link to=\"/responsible-gambling\" className=\"welcome-footer__link\">\n                Responsible Gaming\n              </Link>\n              <Link to=\"/terms\" className=\"welcome-footer__link\">\n                Terms\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"welcome-footer__bottom\">\n          {/* Social Links */}\n          <div className=\"welcome-footer__social\">\n            <a \n              href=\"https://twitter.com/fanbet247\" \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              className=\"welcome-footer__social-link\"\n              aria-label=\"Follow us on Twitter\"\n            >\n              <FaTwitter />\n            </a>\n            <a \n              href=\"https://facebook.com/fanbet247\" \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              className=\"welcome-footer__social-link\"\n              aria-label=\"Follow us on Facebook\"\n            >\n              <FaFacebook />\n            </a>\n            <a \n              href=\"https://t.me/fanbet247\" \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              className=\"welcome-footer__social-link\"\n              aria-label=\"Join our Telegram\"\n            >\n              <FaTelegram />\n            </a>\n          </div>\n\n          {/* Security Badges */}\n          <div className=\"welcome-footer__security\">\n            <div className=\"welcome-footer__badge\">\n              <FaShieldAlt />\n              <span>SSL Secured</span>\n            </div>\n            <div className=\"welcome-footer__badge\">\n              <FaCertificate />\n              <span>Licensed</span>\n            </div>\n          </div>\n\n          {/* Copyright */}\n          <div className=\"welcome-footer__copyright\">\n            <p>2024 FanBet247. All rights reserved.</p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default WelcomeFooter;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,aAAa,QAAQ,gBAAgB;AAC9F,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA;IAAQE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAChCH,OAAA;MAAKE,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBAExCH,OAAA;QAAKE,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEnCH,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCH,OAAA;YAAKE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCH,OAAA;cAAAG,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBP,OAAA;cAAKE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCH,OAAA;gBAAME,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDP,OAAA;gBAAAG,QAAA,EAAG;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCH,OAAA;cAAAG,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBP,OAAA;cAAAG,QAAA,EAAM;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACdP,OAAA;cAAAG,QAAA,EAAM;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtBP,OAAA;cAAAG,QAAA,EAAM;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACdP,OAAA;cAAAG,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNP,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCH,OAAA;YAAKE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCH,OAAA,CAACP,IAAI;cAACe,EAAE,EAAC,kBAAkB;cAACN,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAE7D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPP,OAAA,CAACP,IAAI;cAACe,EAAE,EAAC,mBAAmB;cAACN,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAE9D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPP,OAAA,CAACP,IAAI;cAACe,EAAE,EAAC,cAAc;cAACN,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCH,OAAA,CAACP,IAAI;cAACe,EAAE,EAAC,OAAO;cAACN,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPP,OAAA,CAACP,IAAI;cAACe,EAAE,EAAC,uBAAuB;cAACN,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPP,OAAA,CAACP,IAAI;cAACe,EAAE,EAAC,QAAQ;cAACN,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNP,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAErCH,OAAA;UAAKE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCH,OAAA;YACES,IAAI,EAAC,+BAA+B;YACpCC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBT,SAAS,EAAC,6BAA6B;YACvC,cAAW,sBAAsB;YAAAC,QAAA,eAEjCH,OAAA,CAACN,SAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACJP,OAAA;YACES,IAAI,EAAC,gCAAgC;YACrCC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBT,SAAS,EAAC,6BAA6B;YACvC,cAAW,uBAAuB;YAAAC,QAAA,eAElCH,OAAA,CAACL,UAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACJP,OAAA;YACES,IAAI,EAAC,wBAAwB;YAC7BC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBT,SAAS,EAAC,6BAA6B;YACvC,cAAW,mBAAmB;YAAAC,QAAA,eAE9BH,OAAA,CAACJ,UAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNP,OAAA;UAAKE,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCH,OAAA;YAAKE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCH,OAAA,CAACH,WAAW;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACfP,OAAA;cAAAG,QAAA,EAAM;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCH,OAAA,CAACF,aAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjBP,OAAA;cAAAG,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNP,OAAA;UAAKE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxCH,OAAA;YAAAG,QAAA,EAAG;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACK,EAAA,GAxGIX,aAAa;AA0GnB,eAAeA,aAAa;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}