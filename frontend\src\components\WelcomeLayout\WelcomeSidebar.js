import React from 'react';
import { Link } from 'react-router-dom';
import { FaTrophy, FaUsers, FaChevronRight, FaSpinner } from 'react-icons/fa';
import { API_BASE_URL } from '../../config';
import './WelcomeSidebar.css';

const WelcomeSidebar = ({ leagues, loading, collapsed, onToggle }) => {
  const getLeagueIcon = (league) => {
    if (league.icon_url) {
      return `${API_BASE_URL}${league.icon_url}`;
    } else if (league.league_icon) {
      return `${API_BASE_URL}/uploads/leagues/icons/${league.league_icon}`;
    } else if (league.icon_path) {
      return `${API_BASE_URL}/uploads/leagues/icons/${league.icon_path}`;
    }
    return null;
  };

  const formatBetRange = (min, max) => {
    const formatAmount = (amount) => {
      if (amount >= 1000000) {
        return `${(amount / 1000000).toFixed(1)}M`;
      } else if (amount >= 1000) {
        return `${(amount / 1000).toFixed(0)}K`;
      }
      return amount.toString();
    };
    
    return `${formatAmount(min)} - ${formatAmount(max)} FC`;
  };

  return (
    <aside className={`welcome-sidebar ${collapsed ? 'collapsed' : ''}`}>
      <div className="welcome-sidebar__header">
        <div className="welcome-sidebar__title">
          <FaTrophy className="welcome-sidebar__title-icon" />
          {!collapsed && <span>Top Leagues</span>}
        </div>
      </div>

      <div className="welcome-sidebar__content">
        {loading ? (
          <div className="welcome-sidebar__loading">
            <FaSpinner className="spinning" />
            {!collapsed && <span>Loading leagues...</span>}
          </div>
        ) : leagues.length === 0 ? (
          <div className="welcome-sidebar__empty">
            {!collapsed && <span>No leagues available</span>}
          </div>
        ) : (
          <div className="welcome-sidebar__leagues">
            {leagues.map((league) => (
              <Link
                key={league.league_id}
                to="/login"
                className="welcome-sidebar__league-item"
                title={collapsed ? league.name : ''}
              >
                <div className="welcome-sidebar__league-icon">
                  {getLeagueIcon(league) ? (
                    <img
                      src={getLeagueIcon(league)}
                      alt={`${league.name} icon`}
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div className="welcome-sidebar__league-fallback">
                    <FaTrophy />
                  </div>
                </div>

                {!collapsed && (
                  <div className="welcome-sidebar__league-info">
                    <h4 className="welcome-sidebar__league-name">{league.name}</h4>
                    <div className="welcome-sidebar__league-meta">
                      <span className="welcome-sidebar__league-range">
                        {formatBetRange(league.min_bet_amount, league.max_bet_amount)}
                      </span>
                      <div className="welcome-sidebar__league-members">
                        <FaUsers />
                        <span>{league.member_count || 0}</span>
                      </div>
                    </div>
                  </div>
                )}

                {!collapsed && (
                  <FaChevronRight className="welcome-sidebar__league-arrow" />
                )}
              </Link>
            ))}
          </div>
        )}
      </div>

      {!collapsed && (
        <div className="welcome-sidebar__footer">
          <Link to="/login" className="welcome-sidebar__view-all">
            View All Leagues
          </Link>
        </div>
      )}
    </aside>
  );
};

export default WelcomeSidebar;
