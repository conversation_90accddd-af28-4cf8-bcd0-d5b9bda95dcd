{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\NewWelcomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport './NewWelcomePage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewWelcomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [data, setData] = useState({\n    leagues: [],\n    challenges: [],\n    recentBets: []\n  });\n  const [loading, setLoading] = useState({\n    leagues: true,\n    challenges: true,\n    bets: true\n  });\n  const [error, setError] = useState(null);\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  // Hero slider images\n  const sliderImages = [process.env.PUBLIC_URL + '/slider/Slider1.png', process.env.PUBLIC_URL + '/slider/Slider2.png'];\n\n  // Fetch top 7 leagues\n  const fetchLeagues = async () => {\n    try {\n      const response = await axios.get('/handlers/league_management.php');\n      if (response.data.status === 200) {\n        // Get top 7 leagues by member count\n        const topLeagues = response.data.data.sort((a, b) => (b.member_count || 0) - (a.member_count || 0)).slice(0, 7);\n        setData(prev => ({\n          ...prev,\n          leagues: topLeagues\n        }));\n      }\n    } catch (err) {\n      console.error('Error fetching leagues:', err);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        leagues: false\n      }));\n    }\n  };\n\n  // Fetch live challenges\n  const fetchChallenges = async () => {\n    try {\n      const response = await axios.get('/handlers/recent_challenges.php');\n      if (response.data.success) {\n        setData(prev => ({\n          ...prev,\n          challenges: response.data.challenges.slice(0, 6)\n        }));\n      }\n    } catch (err) {\n      console.error('Error fetching challenges:', err);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        challenges: false\n      }));\n    }\n  };\n\n  // Fetch recent bets\n  const fetchRecentBets = async () => {\n    try {\n      const response = await axios.get('/api/get_recent_data.php');\n      if (response.data.status === 'success') {\n        setData(prev => ({\n          ...prev,\n          recentBets: response.data.data.recentBets.slice(0, 6)\n        }));\n      }\n    } catch (err) {\n      console.error('Error fetching recent bets:', err);\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        bets: false\n      }));\n    }\n  };\n  useEffect(() => {\n    fetchLeagues();\n    fetchChallenges();\n    fetchRecentBets();\n  }, []);\n\n  // Auto-advance slider\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % sliderImages.length);\n    }, 5000);\n    return () => clearInterval(interval);\n  }, [sliderImages.length]);\n  const handleChallengeClick = challengeId => {\n    sessionStorage.setItem('redirectAfterLogin', `/user/join-challenge/${challengeId}`);\n    navigate('/login');\n  };\n  const getTeamLogo = teamName => {\n    return `${API_BASE_URL}/uploads/teams/${teamName === null || teamName === void 0 ? void 0 : teamName.toLowerCase().replace(/\\s+/g, '_')}.png`;\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fresh-welcome-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"fresh-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fresh-header-container\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"fresh-logo\",\n          children: \"FanBet247\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"fresh-nav\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"fresh-nav-link active\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/live-challenges\",\n            className: \"fresh-nav-link\",\n            children: \"Live\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"fresh-nav-link\",\n            children: \"Leagues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/leaderboard\",\n            className: \"fresh-nav-link\",\n            children: \"Leaders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"fresh-nav-link\",\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fresh-auth-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"fresh-btn fresh-btn-login\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"fresh-btn fresh-btn-register\",\n            children: \"Register\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fresh-layout\",\n      children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n        className: \"fresh-sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fresh-sidebar-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Top Leagues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fresh-leagues-list\",\n          children: loading.leagues ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-loading\",\n            children: \"Loading leagues...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this) : data.leagues.map((league, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-league-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-league-rank\",\n              children: [\"#\", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-league-icon\",\n              children: league.icon_path ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `${API_BASE_URL}/uploads/leagues/icons/${league.icon_path}`,\n                alt: league.name,\n                onError: e => e.target.src = '/default-league.png'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fresh-league-placeholder\",\n                children: \"\\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-league-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: league.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [league.member_count || 0, \" members\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"fresh-league-range\",\n                children: [formatCurrency(league.min_bet_amount), \" - \", formatCurrency(league.max_bet_amount), \" FC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this)]\n          }, league.league_id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"fresh-sidebar-cta\",\n          children: \"Login to View All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"fresh-main-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"fresh-hero-section\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-hero-slider\",\n            children: [sliderImages.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `fresh-slide ${index === currentSlide ? 'active' : ''}`,\n              style: {\n                backgroundImage: `url(${image})`\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-slider-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"fresh-slider-btn prev\",\n                onClick: () => setCurrentSlide(prev => prev === 0 ? sliderImages.length - 1 : prev - 1),\n                children: \"\\u276E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"fresh-slider-btn next\",\n                onClick: () => setCurrentSlide(prev => (prev + 1) % sliderImages.length),\n                children: \"\\u276F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-slider-indicators\",\n              children: sliderImages.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `fresh-indicator ${index === currentSlide ? 'active' : ''}`,\n                onClick: () => setCurrentSlide(index)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"fresh-challenges-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"LIVE CHALLENGES\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"fresh-section-link\",\n              children: \"Login to Bet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-challenges-container\",\n            children: loading.challenges ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-loading\",\n              children: \"Loading challenges...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this) : data.challenges.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-empty-state\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No active challenges at the moment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-challenges-grid\",\n              children: data.challenges.map(challenge => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fresh-challenge-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fresh-match-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-team\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: getTeamLogo(challenge.team_a),\n                      alt: challenge.team_a,\n                      onError: e => e.target.src = '/default-team.png'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: challenge.team_a\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-vs\",\n                    children: \"VS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-team\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: getTeamLogo(challenge.team_b),\n                      alt: challenge.team_b,\n                      onError: e => e.target.src = '/default-team.png'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: challenge.team_b\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fresh-odds\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-odd\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Home\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: challenge.odds_team_a\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-odd\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Draw\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: challenge.odds_draw\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-odd\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Away\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: challenge.odds_team_b\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"fresh-bet-btn\",\n                  onClick: () => handleChallengeClick(challenge.challenge_id),\n                  children: \"LOGIN TO BET\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this)]\n              }, challenge.challenge_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"fresh-recent-bets-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"RECENT BETS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/recent-bets\",\n              className: \"fresh-section-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-bets-container\",\n            children: loading.bets ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-loading\",\n              children: \"Loading recent bets...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this) : data.recentBets.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-empty-state\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No recent bets to display\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-bets-grid\",\n              children: data.recentBets.map(bet => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fresh-bet-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fresh-bet-ref\",\n                  children: [\"REF: \", bet.bet_id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fresh-bet-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-bet-user\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: bet.user1_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [formatCurrency(bet.bet_amount), \" FC\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-bet-vs\",\n                    children: \"VS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-bet-user\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: bet.user2_name || 'Open'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [bet.user2_name ? formatCurrency(bet.bet_amount) : 'Waiting', \" FC\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fresh-bet-status\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `fresh-status ${bet.bet_status}`,\n                    children: bet.bet_status.toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 23\n                }, this)]\n              }, bet.bet_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"fresh-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fresh-footer-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fresh-footer-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-footer-brand\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"FanBet247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"18+ Bet Responsibly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-footer-column\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Quick Links\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/live-challenges\",\n                children: \"Live Matches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/upcoming-matches\",\n                children: \"Upcoming\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/leaderboard\",\n                children: \"Leaderboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-footer-column\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/help\",\n                children: \"Help Center\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/responsible-gambling\",\n                children: \"Responsible Gaming\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/terms\",\n                children: \"Terms\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-footer-social\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://twitter.com/fanbet247\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: \"Twitter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://facebook.com/fanbet247\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: \"Facebook\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://t.me/fanbet247\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: \"Telegram\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fresh-footer-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\xA9 2024 FanBet247. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-footer-badges\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2713 SSL Secured\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2713 Licensed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_s(NewWelcomePage, \"DlF0zW4RuF0Yr/U5Z3qs5cXFiO8=\", false, function () {\n  return [useNavigate];\n});\n_c = NewWelcomePage;\nexport default NewWelcomePage;\nvar _c;\n$RefreshReg$(_c, \"NewWelcomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "axios", "API_BASE_URL", "jsxDEV", "_jsxDEV", "NewWelcomePage", "_s", "navigate", "data", "setData", "leagues", "challenges", "recentBets", "loading", "setLoading", "bets", "error", "setError", "currentSlide", "setCurrentSlide", "sliderImages", "process", "env", "PUBLIC_URL", "fetchLeagues", "response", "get", "status", "topLeagues", "sort", "a", "b", "member_count", "slice", "prev", "err", "console", "fetchChallenges", "success", "fetchRecentBets", "interval", "setInterval", "length", "clearInterval", "handleChallengeClick", "challengeId", "sessionStorage", "setItem", "getTeamLogo", "teamName", "toLowerCase", "replace", "formatCurrency", "amount", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "league", "index", "icon_path", "src", "alt", "name", "onError", "e", "target", "min_bet_amount", "max_bet_amount", "league_id", "image", "style", "backgroundImage", "onClick", "_", "challenge", "team_a", "team_b", "odds_team_a", "odds_draw", "odds_team_b", "challenge_id", "bet", "bet_id", "user1_name", "bet_amount", "user2_name", "bet_status", "toUpperCase", "href", "rel", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/NewWelcomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport './NewWelcomePage.css';\n\nconst NewWelcomePage = () => {\n  const navigate = useNavigate();\n  const [data, setData] = useState({\n    leagues: [],\n    challenges: [],\n    recentBets: []\n  });\n  const [loading, setLoading] = useState({\n    leagues: true,\n    challenges: true,\n    bets: true\n  });\n  const [error, setError] = useState(null);\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  // Hero slider images\n  const sliderImages = [\n    process.env.PUBLIC_URL + '/slider/Slider1.png',\n    process.env.PUBLIC_URL + '/slider/Slider2.png'\n  ];\n\n  // Fetch top 7 leagues\n  const fetchLeagues = async () => {\n    try {\n      const response = await axios.get('/handlers/league_management.php');\n      if (response.data.status === 200) {\n        // Get top 7 leagues by member count\n        const topLeagues = response.data.data\n          .sort((a, b) => (b.member_count || 0) - (a.member_count || 0))\n          .slice(0, 7);\n        setData(prev => ({ ...prev, leagues: topLeagues }));\n      }\n    } catch (err) {\n      console.error('Error fetching leagues:', err);\n    } finally {\n      setLoading(prev => ({ ...prev, leagues: false }));\n    }\n  };\n\n  // Fetch live challenges\n  const fetchChallenges = async () => {\n    try {\n      const response = await axios.get('/handlers/recent_challenges.php');\n      if (response.data.success) {\n        setData(prev => ({ ...prev, challenges: response.data.challenges.slice(0, 6) }));\n      }\n    } catch (err) {\n      console.error('Error fetching challenges:', err);\n    } finally {\n      setLoading(prev => ({ ...prev, challenges: false }));\n    }\n  };\n\n  // Fetch recent bets\n  const fetchRecentBets = async () => {\n    try {\n      const response = await axios.get('/api/get_recent_data.php');\n      if (response.data.status === 'success') {\n        setData(prev => ({ ...prev, recentBets: response.data.data.recentBets.slice(0, 6) }));\n      }\n    } catch (err) {\n      console.error('Error fetching recent bets:', err);\n    } finally {\n      setLoading(prev => ({ ...prev, bets: false }));\n    }\n  };\n\n  useEffect(() => {\n    fetchLeagues();\n    fetchChallenges();\n    fetchRecentBets();\n  }, []);\n\n  // Auto-advance slider\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % sliderImages.length);\n    }, 5000);\n    return () => clearInterval(interval);\n  }, [sliderImages.length]);\n\n  const handleChallengeClick = (challengeId) => {\n    sessionStorage.setItem('redirectAfterLogin', `/user/join-challenge/${challengeId}`);\n    navigate('/login');\n  };\n\n  const getTeamLogo = (teamName) => {\n    return `${API_BASE_URL}/uploads/teams/${teamName?.toLowerCase().replace(/\\s+/g, '_')}.png`;\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"fresh-welcome-page\">\n      {/* Header */}\n      <header className=\"fresh-header\">\n        <div className=\"fresh-header-container\">\n          <Link to=\"/\" className=\"fresh-logo\">\n            FanBet247\n          </Link>\n          <nav className=\"fresh-nav\">\n            <Link to=\"/\" className=\"fresh-nav-link active\">Home</Link>\n            <Link to=\"/live-challenges\" className=\"fresh-nav-link\">Live</Link>\n            <Link to=\"/login\" className=\"fresh-nav-link\">Leagues</Link>\n            <Link to=\"/leaderboard\" className=\"fresh-nav-link\">Leaders</Link>\n            <Link to=\"/about\" className=\"fresh-nav-link\">About</Link>\n          </nav>\n          <div className=\"fresh-auth-buttons\">\n            <Link to=\"/login\" className=\"fresh-btn fresh-btn-login\">Login</Link>\n            <Link to=\"/register\" className=\"fresh-btn fresh-btn-register\">Register</Link>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"fresh-layout\">\n        {/* Sidebar with Top 7 Leagues */}\n        <aside className=\"fresh-sidebar\">\n          <div className=\"fresh-sidebar-header\">\n            <h3>Top Leagues</h3>\n          </div>\n          <div className=\"fresh-leagues-list\">\n            {loading.leagues ? (\n              <div className=\"fresh-loading\">Loading leagues...</div>\n            ) : (\n              data.leagues.map((league, index) => (\n                <div key={league.league_id} className=\"fresh-league-item\">\n                  <div className=\"fresh-league-rank\">#{index + 1}</div>\n                  <div className=\"fresh-league-icon\">\n                    {league.icon_path ? (\n                      <img \n                        src={`${API_BASE_URL}/uploads/leagues/icons/${league.icon_path}`}\n                        alt={league.name}\n                        onError={(e) => e.target.src = '/default-league.png'}\n                      />\n                    ) : (\n                      <div className=\"fresh-league-placeholder\">🏆</div>\n                    )}\n                  </div>\n                  <div className=\"fresh-league-info\">\n                    <h4>{league.name}</h4>\n                    <p>{league.member_count || 0} members</p>\n                    <span className=\"fresh-league-range\">\n                      {formatCurrency(league.min_bet_amount)} - {formatCurrency(league.max_bet_amount)} FC\n                    </span>\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n          <Link to=\"/login\" className=\"fresh-sidebar-cta\">\n            Login to View All\n          </Link>\n        </aside>\n\n        {/* Main Content */}\n        <main className=\"fresh-main-content\">\n          {/* Hero Slider */}\n          <section className=\"fresh-hero-section\">\n            <div className=\"fresh-hero-slider\">\n              {sliderImages.map((image, index) => (\n                <div \n                  key={index}\n                  className={`fresh-slide ${index === currentSlide ? 'active' : ''}`}\n                  style={{ backgroundImage: `url(${image})` }}\n                />\n              ))}\n              <div className=\"fresh-slider-controls\">\n                <button \n                  className=\"fresh-slider-btn prev\"\n                  onClick={() => setCurrentSlide(prev => prev === 0 ? sliderImages.length - 1 : prev - 1)}\n                >\n                  ❮\n                </button>\n                <button \n                  className=\"fresh-slider-btn next\"\n                  onClick={() => setCurrentSlide(prev => (prev + 1) % sliderImages.length)}\n                >\n                  ❯\n                </button>\n              </div>\n              <div className=\"fresh-slider-indicators\">\n                {sliderImages.map((_, index) => (\n                  <button\n                    key={index}\n                    className={`fresh-indicator ${index === currentSlide ? 'active' : ''}`}\n                    onClick={() => setCurrentSlide(index)}\n                  />\n                ))}\n              </div>\n            </div>\n          </section>\n\n          {/* Live Challenges Section */}\n          <section className=\"fresh-challenges-section\">\n            <div className=\"fresh-section-header\">\n              <h2>LIVE CHALLENGES</h2>\n              <Link to=\"/login\" className=\"fresh-section-link\">Login to Bet</Link>\n            </div>\n            <div className=\"fresh-challenges-container\">\n              {loading.challenges ? (\n                <div className=\"fresh-loading\">Loading challenges...</div>\n              ) : data.challenges.length === 0 ? (\n                <div className=\"fresh-empty-state\">\n                  <p>No active challenges at the moment</p>\n                </div>\n              ) : (\n                <div className=\"fresh-challenges-grid\">\n                  {data.challenges.map(challenge => (\n                    <div key={challenge.challenge_id} className=\"fresh-challenge-card\">\n                      <div className=\"fresh-match-info\">\n                        <div className=\"fresh-team\">\n                          <img \n                            src={getTeamLogo(challenge.team_a)} \n                            alt={challenge.team_a}\n                            onError={(e) => e.target.src = '/default-team.png'}\n                          />\n                          <span>{challenge.team_a}</span>\n                        </div>\n                        <div className=\"fresh-vs\">VS</div>\n                        <div className=\"fresh-team\">\n                          <img \n                            src={getTeamLogo(challenge.team_b)} \n                            alt={challenge.team_b}\n                            onError={(e) => e.target.src = '/default-team.png'}\n                          />\n                          <span>{challenge.team_b}</span>\n                        </div>\n                      </div>\n                      <div className=\"fresh-odds\">\n                        <div className=\"fresh-odd\">\n                          <span>Home</span>\n                          <span>{challenge.odds_team_a}</span>\n                        </div>\n                        <div className=\"fresh-odd\">\n                          <span>Draw</span>\n                          <span>{challenge.odds_draw}</span>\n                        </div>\n                        <div className=\"fresh-odd\">\n                          <span>Away</span>\n                          <span>{challenge.odds_team_b}</span>\n                        </div>\n                      </div>\n                      <button \n                        className=\"fresh-bet-btn\"\n                        onClick={() => handleChallengeClick(challenge.challenge_id)}\n                      >\n                        LOGIN TO BET\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </section>\n\n          {/* Recent Bets Section */}\n          <section className=\"fresh-recent-bets-section\">\n            <div className=\"fresh-section-header\">\n              <h2>RECENT BETS</h2>\n              <Link to=\"/user/recent-bets\" className=\"fresh-section-link\">View All</Link>\n            </div>\n            <div className=\"fresh-bets-container\">\n              {loading.bets ? (\n                <div className=\"fresh-loading\">Loading recent bets...</div>\n              ) : data.recentBets.length === 0 ? (\n                <div className=\"fresh-empty-state\">\n                  <p>No recent bets to display</p>\n                </div>\n              ) : (\n                <div className=\"fresh-bets-grid\">\n                  {data.recentBets.map(bet => (\n                    <div key={bet.bet_id} className=\"fresh-bet-card\">\n                      <div className=\"fresh-bet-ref\">REF: {bet.bet_id}</div>\n                      <div className=\"fresh-bet-details\">\n                        <div className=\"fresh-bet-user\">\n                          <span>{bet.user1_name}</span>\n                          <span>{formatCurrency(bet.bet_amount)} FC</span>\n                        </div>\n                        <div className=\"fresh-bet-vs\">VS</div>\n                        <div className=\"fresh-bet-user\">\n                          <span>{bet.user2_name || 'Open'}</span>\n                          <span>{bet.user2_name ? formatCurrency(bet.bet_amount) : 'Waiting'} FC</span>\n                        </div>\n                      </div>\n                      <div className=\"fresh-bet-status\">\n                        <span className={`fresh-status ${bet.bet_status}`}>\n                          {bet.bet_status.toUpperCase()}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </section>\n        </main>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"fresh-footer\">\n        <div className=\"fresh-footer-container\">\n          <div className=\"fresh-footer-content\">\n            <div className=\"fresh-footer-brand\">\n              <h3>FanBet247</h3>\n              <p>18+ Bet Responsibly</p>\n            </div>\n            <div className=\"fresh-footer-links\">\n              <div className=\"fresh-footer-column\">\n                <h4>Quick Links</h4>\n                <Link to=\"/live-challenges\">Live Matches</Link>\n                <Link to=\"/upcoming-matches\">Upcoming</Link>\n                <Link to=\"/leaderboard\">Leaderboard</Link>\n              </div>\n              <div className=\"fresh-footer-column\">\n                <h4>Support</h4>\n                <Link to=\"/help\">Help Center</Link>\n                <Link to=\"/responsible-gambling\">Responsible Gaming</Link>\n                <Link to=\"/terms\">Terms</Link>\n              </div>\n            </div>\n            <div className=\"fresh-footer-social\">\n              <a href=\"https://twitter.com/fanbet247\" target=\"_blank\" rel=\"noopener noreferrer\">Twitter</a>\n              <a href=\"https://facebook.com/fanbet247\" target=\"_blank\" rel=\"noopener noreferrer\">Facebook</a>\n              <a href=\"https://t.me/fanbet247\" target=\"_blank\" rel=\"noopener noreferrer\">Telegram</a>\n            </div>\n          </div>\n          <div className=\"fresh-footer-bottom\">\n            <p>© 2024 FanBet247. All rights reserved.</p>\n            <div className=\"fresh-footer-badges\">\n              <span>✓ SSL Secured</span>\n              <span>✓ Licensed</span>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default NewWelcomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC;IAC/Ba,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC;IACrCa,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBI,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAMuB,YAAY,GAAG,CACnBC,OAAO,CAACC,GAAG,CAACC,UAAU,GAAG,qBAAqB,EAC9CF,OAAO,CAACC,GAAG,CAACC,UAAU,GAAG,qBAAqB,CAC/C;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,iCAAiC,CAAC;MACnE,IAAID,QAAQ,CAACjB,IAAI,CAACmB,MAAM,KAAK,GAAG,EAAE;QAChC;QACA,MAAMC,UAAU,GAAGH,QAAQ,CAACjB,IAAI,CAACA,IAAI,CAClCqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,YAAY,IAAI,CAAC,KAAKF,CAAC,CAACE,YAAY,IAAI,CAAC,CAAC,CAAC,CAC7DC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACdxB,OAAO,CAACyB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAExB,OAAO,EAAEkB;QAAW,CAAC,CAAC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZC,OAAO,CAACpB,KAAK,CAAC,yBAAyB,EAAEmB,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRrB,UAAU,CAACoB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExB,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAM2B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,iCAAiC,CAAC;MACnE,IAAID,QAAQ,CAACjB,IAAI,CAAC8B,OAAO,EAAE;QACzB7B,OAAO,CAACyB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEvB,UAAU,EAAEc,QAAQ,CAACjB,IAAI,CAACG,UAAU,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC;MAClF;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACpB,KAAK,CAAC,4BAA4B,EAAEmB,GAAG,CAAC;IAClD,CAAC,SAAS;MACRrB,UAAU,CAACoB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEvB,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM4B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,0BAA0B,CAAC;MAC5D,IAAID,QAAQ,CAACjB,IAAI,CAACmB,MAAM,KAAK,SAAS,EAAE;QACtClB,OAAO,CAACyB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEtB,UAAU,EAAEa,QAAQ,CAACjB,IAAI,CAACA,IAAI,CAACI,UAAU,CAACqB,KAAK,CAAC,CAAC,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC;MACvF;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACpB,KAAK,CAAC,6BAA6B,EAAEmB,GAAG,CAAC;IACnD,CAAC,SAAS;MACRrB,UAAU,CAACoB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEnB,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACd0B,YAAY,CAAC,CAAC;IACda,eAAe,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzC,SAAS,CAAC,MAAM;IACd,MAAM0C,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCtB,eAAe,CAACe,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAId,YAAY,CAACsB,MAAM,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACpB,YAAY,CAACsB,MAAM,CAAC,CAAC;EAEzB,MAAME,oBAAoB,GAAIC,WAAW,IAAK;IAC5CC,cAAc,CAACC,OAAO,CAAC,oBAAoB,EAAE,wBAAwBF,WAAW,EAAE,CAAC;IACnFtC,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMyC,WAAW,GAAIC,QAAQ,IAAK;IAChC,OAAO,GAAG/C,YAAY,kBAAkB+C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM;EAC5F,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,oBACEjD,OAAA;IAAKuD,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjCxD,OAAA;MAAQuD,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC9BxD,OAAA;QAAKuD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCxD,OAAA,CAACL,IAAI;UAAC8D,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAEpC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP7D,OAAA;UAAKuD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBxD,OAAA,CAACL,IAAI;YAAC8D,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1D7D,OAAA,CAACL,IAAI;YAAC8D,EAAE,EAAC,kBAAkB;YAACF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClE7D,OAAA,CAACL,IAAI;YAAC8D,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3D7D,OAAA,CAACL,IAAI;YAAC8D,EAAE,EAAC,cAAc;YAACF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjE7D,OAAA,CAACL,IAAI;YAAC8D,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACN7D,OAAA;UAAKuD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCxD,OAAA,CAACL,IAAI;YAAC8D,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpE7D,OAAA,CAACL,IAAI;YAAC8D,EAAE,EAAC,WAAW;YAACF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET7D,OAAA;MAAKuD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3BxD,OAAA;QAAOuD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC9BxD,OAAA;UAAKuD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnCxD,OAAA;YAAAwD,QAAA,EAAI;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACN7D,OAAA;UAAKuD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAChC/C,OAAO,CAACH,OAAO,gBACdN,OAAA;YAAKuD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAEvDzD,IAAI,CAACE,OAAO,CAACwD,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC7BhE,OAAA;YAA4BuD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACvDxD,OAAA;cAAKuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAAC,GAAC,EAACQ,KAAK,GAAG,CAAC;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrD7D,OAAA;cAAKuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC/BO,MAAM,CAACE,SAAS,gBACfjE,OAAA;gBACEkE,GAAG,EAAE,GAAGpE,YAAY,0BAA0BiE,MAAM,CAACE,SAAS,EAAG;gBACjEE,GAAG,EAAEJ,MAAM,CAACK,IAAK;gBACjBC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG;cAAsB;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,gBAEF7D,OAAA;gBAAKuD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAClD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN7D,OAAA;cAAKuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCxD,OAAA;gBAAAwD,QAAA,EAAKO,MAAM,CAACK;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtB7D,OAAA;gBAAAwD,QAAA,GAAIO,MAAM,CAACnC,YAAY,IAAI,CAAC,EAAC,UAAQ;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzC7D,OAAA;gBAAMuD,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GACjCR,cAAc,CAACe,MAAM,CAACS,cAAc,CAAC,EAAC,KAAG,EAACxB,cAAc,CAACe,MAAM,CAACU,cAAc,CAAC,EAAC,KACnF;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAnBEE,MAAM,CAACW,SAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBrB,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN7D,OAAA,CAACL,IAAI;UAAC8D,EAAE,EAAC,QAAQ;UAACF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAEhD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR7D,OAAA;QAAMuD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAElCxD,OAAA;UAASuD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACrCxD,OAAA;YAAKuD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAC/BxC,YAAY,CAAC8C,GAAG,CAAC,CAACa,KAAK,EAAEX,KAAK,kBAC7BhE,OAAA;cAEEuD,SAAS,EAAE,eAAeS,KAAK,KAAKlD,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;cACnE8D,KAAK,EAAE;gBAAEC,eAAe,EAAE,OAAOF,KAAK;cAAI;YAAE,GAFvCX,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGX,CACF,CAAC,eACF7D,OAAA;cAAKuD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCxD,OAAA;gBACEuD,SAAS,EAAC,uBAAuB;gBACjCuB,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAACe,IAAI,IAAIA,IAAI,KAAK,CAAC,GAAGd,YAAY,CAACsB,MAAM,GAAG,CAAC,GAAGR,IAAI,GAAG,CAAC,CAAE;gBAAA0B,QAAA,EACzF;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7D,OAAA;gBACEuD,SAAS,EAAC,uBAAuB;gBACjCuB,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAACe,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAId,YAAY,CAACsB,MAAM,CAAE;gBAAAkB,QAAA,EAC1E;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7D,OAAA;cAAKuD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EACrCxC,YAAY,CAAC8C,GAAG,CAAC,CAACiB,CAAC,EAAEf,KAAK,kBACzBhE,OAAA;gBAEEuD,SAAS,EAAE,mBAAmBS,KAAK,KAAKlD,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACvEgE,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAACiD,KAAK;cAAE,GAFjCA,KAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGV7D,OAAA;UAASuD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBAC3CxD,OAAA;YAAKuD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCxD,OAAA;cAAAwD,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB7D,OAAA,CAACL,IAAI;cAAC8D,EAAE,EAAC,QAAQ;cAACF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACN7D,OAAA;YAAKuD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxC/C,OAAO,CAACF,UAAU,gBACjBP,OAAA;cAAKuD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GACxDzD,IAAI,CAACG,UAAU,CAAC+B,MAAM,KAAK,CAAC,gBAC9BtC,OAAA;cAAKuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCxD,OAAA;gBAAAwD,QAAA,EAAG;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,gBAEN7D,OAAA;cAAKuD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnCpD,IAAI,CAACG,UAAU,CAACuD,GAAG,CAACkB,SAAS,iBAC5BhF,OAAA;gBAAkCuD,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBAChExD,OAAA;kBAAKuD,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BxD,OAAA;oBAAKuD,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBxD,OAAA;sBACEkE,GAAG,EAAEtB,WAAW,CAACoC,SAAS,CAACC,MAAM,CAAE;sBACnCd,GAAG,EAAEa,SAAS,CAACC,MAAO;sBACtBZ,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG;oBAAoB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACF7D,OAAA;sBAAAwD,QAAA,EAAOwB,SAAS,CAACC;oBAAM;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACN7D,OAAA;oBAAKuD,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClC7D,OAAA;oBAAKuD,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBxD,OAAA;sBACEkE,GAAG,EAAEtB,WAAW,CAACoC,SAAS,CAACE,MAAM,CAAE;sBACnCf,GAAG,EAAEa,SAAS,CAACE,MAAO;sBACtBb,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG;oBAAoB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACF7D,OAAA;sBAAAwD,QAAA,EAAOwB,SAAS,CAACE;oBAAM;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7D,OAAA;kBAAKuD,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBxD,OAAA;oBAAKuD,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBxD,OAAA;sBAAAwD,QAAA,EAAM;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjB7D,OAAA;sBAAAwD,QAAA,EAAOwB,SAAS,CAACG;oBAAW;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACN7D,OAAA;oBAAKuD,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBxD,OAAA;sBAAAwD,QAAA,EAAM;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjB7D,OAAA;sBAAAwD,QAAA,EAAOwB,SAAS,CAACI;oBAAS;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACN7D,OAAA;oBAAKuD,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBxD,OAAA;sBAAAwD,QAAA,EAAM;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjB7D,OAAA;sBAAAwD,QAAA,EAAOwB,SAAS,CAACK;oBAAW;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7D,OAAA;kBACEuD,SAAS,EAAC,eAAe;kBACzBuB,OAAO,EAAEA,CAAA,KAAMtC,oBAAoB,CAACwC,SAAS,CAACM,YAAY,CAAE;kBAAA9B,QAAA,EAC7D;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAvCDmB,SAAS,CAACM,YAAY;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwC3B,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGV7D,OAAA;UAASuD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAC5CxD,OAAA;YAAKuD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCxD,OAAA;cAAAwD,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB7D,OAAA,CAACL,IAAI;cAAC8D,EAAE,EAAC,mBAAmB;cAACF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACN7D,OAAA;YAAKuD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAClC/C,OAAO,CAACE,IAAI,gBACXX,OAAA;cAAKuD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GACzDzD,IAAI,CAACI,UAAU,CAAC8B,MAAM,KAAK,CAAC,gBAC9BtC,OAAA;cAAKuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCxD,OAAA;gBAAAwD,QAAA,EAAG;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,gBAEN7D,OAAA;cAAKuD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC7BpD,IAAI,CAACI,UAAU,CAACsD,GAAG,CAACyB,GAAG,iBACtBvF,OAAA;gBAAsBuD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC9CxD,OAAA;kBAAKuD,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,OAAK,EAAC+B,GAAG,CAACC,MAAM;gBAAA;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtD7D,OAAA;kBAAKuD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCxD,OAAA;oBAAKuD,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BxD,OAAA;sBAAAwD,QAAA,EAAO+B,GAAG,CAACE;oBAAU;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC7B7D,OAAA;sBAAAwD,QAAA,GAAOR,cAAc,CAACuC,GAAG,CAACG,UAAU,CAAC,EAAC,KAAG;oBAAA;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACN7D,OAAA;oBAAKuD,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtC7D,OAAA;oBAAKuD,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BxD,OAAA;sBAAAwD,QAAA,EAAO+B,GAAG,CAACI,UAAU,IAAI;oBAAM;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACvC7D,OAAA;sBAAAwD,QAAA,GAAO+B,GAAG,CAACI,UAAU,GAAG3C,cAAc,CAACuC,GAAG,CAACG,UAAU,CAAC,GAAG,SAAS,EAAC,KAAG;oBAAA;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7D,OAAA;kBAAKuD,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BxD,OAAA;oBAAMuD,SAAS,EAAE,gBAAgBgC,GAAG,CAACK,UAAU,EAAG;oBAAApC,QAAA,EAC/C+B,GAAG,CAACK,UAAU,CAACC,WAAW,CAAC;kBAAC;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAjBE0B,GAAG,CAACC,MAAM;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkBf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN7D,OAAA;MAAQuD,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC9BxD,OAAA;QAAKuD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCxD,OAAA;UAAKuD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCxD,OAAA;YAAKuD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCxD,OAAA;cAAAwD,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB7D,OAAA;cAAAwD,QAAA,EAAG;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACN7D,OAAA;YAAKuD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCxD,OAAA;cAAKuD,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCxD,OAAA;gBAAAwD,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB7D,OAAA,CAACL,IAAI;gBAAC8D,EAAE,EAAC,kBAAkB;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/C7D,OAAA,CAACL,IAAI;gBAAC8D,EAAE,EAAC,mBAAmB;gBAAAD,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5C7D,OAAA,CAACL,IAAI;gBAAC8D,EAAE,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN7D,OAAA;cAAKuD,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCxD,OAAA;gBAAAwD,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB7D,OAAA,CAACL,IAAI;gBAAC8D,EAAE,EAAC,OAAO;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnC7D,OAAA,CAACL,IAAI;gBAAC8D,EAAE,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1D7D,OAAA,CAACL,IAAI;gBAAC8D,EAAE,EAAC,QAAQ;gBAAAD,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA;YAAKuD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCxD,OAAA;cAAG8F,IAAI,EAAC,+BAA+B;cAACvB,MAAM,EAAC,QAAQ;cAACwB,GAAG,EAAC,qBAAqB;cAAAvC,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7F7D,OAAA;cAAG8F,IAAI,EAAC,gCAAgC;cAACvB,MAAM,EAAC,QAAQ;cAACwB,GAAG,EAAC,qBAAqB;cAAAvC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/F7D,OAAA;cAAG8F,IAAI,EAAC,wBAAwB;cAACvB,MAAM,EAAC,QAAQ;cAACwB,GAAG,EAAC,qBAAqB;cAAAvC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7D,OAAA;UAAKuD,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCxD,OAAA;YAAAwD,QAAA,EAAG;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7C7D,OAAA;YAAKuD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCxD,OAAA;cAAAwD,QAAA,EAAM;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1B7D,OAAA;cAAAwD,QAAA,EAAM;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC3D,EAAA,CAtVID,cAAc;EAAA,QACDL,WAAW;AAAA;AAAAoG,EAAA,GADxB/F,cAAc;AAwVpB,eAAeA,cAAc;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}