{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserLogin.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { useNavigate, Link } from 'react-router-dom';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction UserLogin() {\n  _s();\n  const [credentials, setCredentials] = useState({\n    usernameOrEmail: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [showOtpVerification, setShowOtpVerification] = useState(false);\n  const [showGoogleAuthVerification, setShowGoogleAuthVerification] = useState(false);\n  const [otpCode, setOtpCode] = useState('');\n  const [googleAuthCode, setGoogleAuthCode] = useState('');\n  const [userEmail, setUserEmail] = useState('');\n  const [userId, setUserId] = useState(null);\n  const [otpExpiry, setOtpExpiry] = useState(null);\n  const [otpCountdown, setOtpCountdown] = useState(0);\n  const [otpResending, setOtpResending] = useState(false);\n  const navigate = useNavigate();\n\n  // Redirect if already logged in\n  useEffect(() => {\n    const userId = localStorage.getItem('userId');\n    if (userId) {\n      navigate('/user/dashboard');\n    }\n  }, [navigate]);\n\n  // OTP countdown timer\n  useEffect(() => {\n    let timer;\n    if (otpCountdown > 0) {\n      timer = setTimeout(() => setOtpCountdown(otpCountdown - 1), 1000);\n    }\n    return () => clearTimeout(timer);\n  }, [otpCountdown]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setCredentials(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError('');\n  };\n  const sendOtpCode = async (userId, email) => {\n    try {\n      const response = await axios.post('user_send_otp.php', {\n        userId: userId,\n        email: email\n      });\n      if (!response.data.success) {\n        throw new Error(response.data.message || 'Failed to send OTP');\n      }\n    } catch (err) {\n      console.error('OTP send error:', err);\n      setError('Failed to send OTP. Please try again.');\n      throw err;\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('user_login_enhanced.php', credentials);\n      if (response.data.success) {\n        // Check if additional authentication is required\n        if (response.data.requiresAdditionalAuth) {\n          // Store user data for additional auth steps\n          setUserEmail(response.data.email);\n          if (response.data.authType === 'otp' || response.data.authType === 'email_otp') {\n            // Send OTP first, then show verification form\n            await sendOtpCode(response.data.userId, response.data.email);\n            setShowOtpVerification(true);\n            setOtpExpiry(300); // 5 minutes\n            setOtpCountdown(300);\n          } else if (response.data.authType === '2fa' || response.data.authType === 'google_auth') {\n            // Show Google Authenticator verification form\n            setShowGoogleAuthVerification(true);\n          } else if (response.data.authType === 'otp_2fa') {\n            // Start with OTP, then 2FA\n            await sendOtpCode(response.data.userId, response.data.email);\n            setShowOtpVerification(true);\n            setOtpExpiry(300); // 5 minutes\n            setOtpCountdown(300);\n          }\n        } else {\n          // No additional auth required, proceed with login\n          localStorage.setItem('userId', response.data.userId);\n          localStorage.setItem('username', response.data.username);\n\n          // Get redirect path or default to dashboard\n          const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n          sessionStorage.removeItem('redirectAfterLogin');\n          navigate(redirectPath);\n        }\n      } else {\n        setError(response.data.message || 'Login failed. Please try again.');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response3;\n      console.error('Login error:', err);\n      if ((_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.message) {\n        setError(err.response.data.message);\n      } else if (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401) {\n        setError('Invalid username/email or password');\n      } else if (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 400) {\n        setError('Please enter both username/email and password');\n      } else {\n        setError('An error occurred. Please try again later.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleOtpVerification = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('verify_otp.php', {\n        email: userEmail,\n        otp: otpCode\n      });\n      if (response.data.success) {\n        localStorage.setItem('userId', response.data.userId);\n        localStorage.setItem('username', response.data.username);\n\n        // Get redirect path or default to dashboard\n        const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n        sessionStorage.removeItem('redirectAfterLogin');\n        navigate(redirectPath);\n      } else {\n        setError(response.data.message || 'OTP verification failed. Please try again.');\n      }\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      console.error('OTP verification error:', err);\n      if ((_err$response4 = err.response) !== null && _err$response4 !== void 0 && (_err$response4$data = _err$response4.data) !== null && _err$response4$data !== void 0 && _err$response4$data.message) {\n        setError(err.response.data.message);\n      } else {\n        setError('An error occurred during OTP verification. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleGoogleAuthVerification = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('verify_google_auth.php', {\n        email: userEmail,\n        code: googleAuthCode\n      });\n      if (response.data.success) {\n        localStorage.setItem('userId', response.data.userId);\n        localStorage.setItem('username', response.data.username);\n\n        // Get redirect path or default to dashboard\n        const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n        sessionStorage.removeItem('redirectAfterLogin');\n        navigate(redirectPath);\n      } else {\n        setError(response.data.message || 'Verification failed. Please try again.');\n      }\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      console.error('Google Auth verification error:', err);\n      if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.message) {\n        setError(err.response.data.message);\n      } else {\n        setError('An error occurred during verification. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleResendOtp = async () => {\n    setError('');\n    setOtpResending(true);\n    try {\n      const response = await axios.post('send_otp.php', {\n        email: userEmail\n      });\n      if (response.data.success) {\n        setOtpExpiry(response.data.expiresIn);\n        setOtpCountdown(response.data.expiresIn);\n        setError(''); // Clear any previous errors\n      } else {\n        setError(response.data.message || 'Failed to resend OTP. Please try again.');\n      }\n    } catch (err) {\n      var _err$response6, _err$response6$data;\n      console.error('Resend OTP error:', err);\n      if ((_err$response6 = err.response) !== null && _err$response6 !== void 0 && (_err$response6$data = _err$response6.data) !== null && _err$response6$data !== void 0 && _err$response6$data.message) {\n        setError(err.response.data.message);\n      } else {\n        setError('An error occurred while resending OTP. Please try again.');\n      }\n    } finally {\n      setOtpResending(false);\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;\n  };\n\n  // Render OTP verification form\n  if (showOtpVerification) {\n    return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n      title: \"OTP Verification\",\n      subtitle: \"Enter the one-time password sent to your email\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 27\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleOtpVerification,\n        className: \"user-auth-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"otpCode\",\n            children: \"OTP Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-auth-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"otpCode\",\n              name: \"otpCode\",\n              value: otpCode,\n              onChange: e => setOtpCode(e.target.value),\n              placeholder: \"Enter 6-digit OTP code\",\n              required: true,\n              autoComplete: \"one-time-code\",\n              className: \"user-auth-otp-input\",\n              maxLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-key\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 21\n        }, this), otpCountdown > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-countdown-timer\",\n          children: [\"OTP expires in: \", formatTime(otpCountdown)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"user-auth-button\",\n          disabled: isLoading,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-auth-loading-spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 33\n            }, this), \"Verifying...\"]\n          }, void 0, true) : 'Verify OTP'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-options center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"user-auth-resend-button\",\n            onClick: handleResendOtp,\n            disabled: otpResending || otpCountdown > 0,\n            children: otpResending ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"user-auth-loading-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 37\n              }, this), \"Resending...\"]\n            }, void 0, true) : 'Resend OTP'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"user-auth-button-secondary\",\n            onClick: () => {\n              setShowOtpVerification(false);\n              setOtpCode('');\n              setError('');\n            },\n            children: \"Back to Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Render Google Authenticator verification form\n  if (showGoogleAuthVerification) {\n    return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n      title: \"Two-Factor Authentication\",\n      subtitle: \"Enter the code from your Google Authenticator app\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 27\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleGoogleAuthVerification,\n        className: \"user-auth-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"googleAuthCode\",\n            children: \"Authentication Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-auth-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"googleAuthCode\",\n              name: \"googleAuthCode\",\n              value: googleAuthCode,\n              onChange: e => setGoogleAuthCode(e.target.value),\n              placeholder: \"Enter 6-digit code\",\n              required: true,\n              autoComplete: \"one-time-code\",\n              className: \"user-auth-otp-input\",\n              maxLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-shield-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-auth-countdown-timer\",\n            children: \"Open your Google Authenticator app and enter the 6-digit code for FanBet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"user-auth-button\",\n          disabled: isLoading,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-auth-loading-spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 33\n            }, this), \"Verifying...\"]\n          }, void 0, true) : 'Verify Code'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"user-auth-button-secondary\",\n            onClick: () => {\n              setShowGoogleAuthVerification(false);\n              setGoogleAuthCode('');\n              setError('');\n            },\n            children: \"Back to Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Render main login form\n  return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n    title: \"Welcome Back\",\n    subtitle: \"Sign in to your account\",\n    variant: \"login\",\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-auth-error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 23\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"user-auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"usernameOrEmail\",\n          children: \"Email or Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"usernameOrEmail\",\n            name: \"usernameOrEmail\",\n            value: credentials.usernameOrEmail,\n            onChange: handleInputChange,\n            placeholder: \"Enter your email or username\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: credentials.password,\n            onChange: handleInputChange,\n            placeholder: \"Enter your password\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-lock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-options end\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/forgot-password\",\n          className: \"user-auth-forgot-password\",\n          children: \"Forgot Password?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"user-auth-button\",\n        disabled: isLoading,\n        children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-auth-loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 29\n          }, this), \"Signing in...\"]\n        }, void 0, true) : 'Sign In'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"user-auth-register-link\",\n            children: \"Create one here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 47\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 378,\n    columnNumber: 9\n  }, this);\n}\n_s(UserLogin, \"jTrK+c1psqkcg7H88ymZO0yGAO8=\", false, function () {\n  return [useNavigate];\n});\n_c = UserLogin;\nexport default UserLogin;\nvar _c;\n$RefreshReg$(_c, \"UserLogin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useNavigate", "Link", "UserAuthLayout", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserLogin", "_s", "credentials", "setCredentials", "usernameOrEmail", "password", "error", "setError", "isLoading", "setIsLoading", "showOtpVerification", "setShowOtpVerification", "showGoogleAuthVerification", "setShowGoogleAuthVerification", "otpCode", "setOtpCode", "googleAuthCode", "setGoogleAuthCode", "userEmail", "setUserEmail", "userId", "setUserId", "otpExpiry", "setOtpExpiry", "otpCountdown", "setOtpCountdown", "otpResending", "setOtpResending", "navigate", "localStorage", "getItem", "timer", "setTimeout", "clearTimeout", "handleInputChange", "e", "name", "value", "target", "prev", "sendOtpCode", "email", "response", "post", "data", "success", "Error", "message", "err", "console", "handleSubmit", "preventDefault", "requiresAdditionalAuth", "authType", "setItem", "username", "redirectPath", "sessionStorage", "removeItem", "_err$response", "_err$response$data", "_err$response2", "_err$response3", "status", "handleOtpVerification", "otp", "_err$response4", "_err$response4$data", "handleGoogleAuthVerification", "code", "_err$response5", "_err$response5$data", "handleResendOtp", "expiresIn", "_err$response6", "_err$response6$data", "formatTime", "seconds", "mins", "Math", "floor", "secs", "title", "subtitle", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "required", "autoComplete", "max<PERSON><PERSON><PERSON>", "disabled", "onClick", "variant", "to", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserLogin.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { useNavigate, Link } from 'react-router-dom';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\n\nfunction UserLogin() {\n    const [credentials, setCredentials] = useState({ usernameOrEmail: '', password: '' });\n    const [error, setError] = useState('');\n    const [isLoading, setIsLoading] = useState(false);\n    const [showOtpVerification, setShowOtpVerification] = useState(false);\n    const [showGoogleAuthVerification, setShowGoogleAuthVerification] = useState(false);\n    const [otpCode, setOtpCode] = useState('');\n    const [googleAuthCode, setGoogleAuthCode] = useState('');\n    const [userEmail, setUserEmail] = useState('');\n    const [userId, setUserId] = useState(null);\n    const [otpExpiry, setOtpExpiry] = useState(null);\n    const [otpCountdown, setOtpCountdown] = useState(0);\n    const [otpResending, setOtpResending] = useState(false);\n    const navigate = useNavigate();\n\n    // Redirect if already logged in\n    useEffect(() => {\n        const userId = localStorage.getItem('userId');\n        if (userId) {\n            navigate('/user/dashboard');\n        }\n    }, [navigate]);\n\n    // OTP countdown timer\n    useEffect(() => {\n        let timer;\n        if (otpCountdown > 0) {\n            timer = setTimeout(() => setOtpCountdown(otpCountdown - 1), 1000);\n        }\n        return () => clearTimeout(timer);\n    }, [otpCountdown]);\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setCredentials(prev => ({ ...prev, [name]: value }));\n        if (error) setError('');\n    };\n\n    const sendOtpCode = async (userId, email) => {\n        try {\n            const response = await axios.post(\n                'user_send_otp.php',\n                {\n                    userId: userId,\n                    email: email\n                }\n            );\n\n            if (!response.data.success) {\n                throw new Error(response.data.message || 'Failed to send OTP');\n            }\n        } catch (err) {\n            console.error('OTP send error:', err);\n            setError('Failed to send OTP. Please try again.');\n            throw err;\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n\n        try {\n            const response = await axios.post(\n                'user_login_enhanced.php',\n                credentials\n            );\n\n            if (response.data.success) {\n                // Check if additional authentication is required\n                if (response.data.requiresAdditionalAuth) {\n                    // Store user data for additional auth steps\n                    setUserEmail(response.data.email);\n\n                    if (response.data.authType === 'otp' || response.data.authType === 'email_otp') {\n                        // Send OTP first, then show verification form\n                        await sendOtpCode(response.data.userId, response.data.email);\n                        setShowOtpVerification(true);\n                        setOtpExpiry(300); // 5 minutes\n                        setOtpCountdown(300);\n                    } else if (response.data.authType === '2fa' || response.data.authType === 'google_auth') {\n                        // Show Google Authenticator verification form\n                        setShowGoogleAuthVerification(true);\n                    } else if (response.data.authType === 'otp_2fa') {\n                        // Start with OTP, then 2FA\n                        await sendOtpCode(response.data.userId, response.data.email);\n                        setShowOtpVerification(true);\n                        setOtpExpiry(300); // 5 minutes\n                        setOtpCountdown(300);\n                    }\n                } else {\n                    // No additional auth required, proceed with login\n                    localStorage.setItem('userId', response.data.userId);\n                    localStorage.setItem('username', response.data.username);\n\n                    // Get redirect path or default to dashboard\n                    const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n                    sessionStorage.removeItem('redirectAfterLogin');\n                    navigate(redirectPath);\n                }\n            } else {\n                setError(response.data.message || 'Login failed. Please try again.');\n            }\n        } catch (err) {\n            console.error('Login error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else if (err.response?.status === 401) {\n                setError('Invalid username/email or password');\n            } else if (err.response?.status === 400) {\n                setError('Please enter both username/email and password');\n            } else {\n                setError('An error occurred. Please try again later.');\n            }\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const handleOtpVerification = async (e) => {\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n\n        try {\n            const response = await axios.post(\n                'verify_otp.php',\n                {\n                    email: userEmail,\n                    otp: otpCode\n                }\n            );\n\n            if (response.data.success) {\n                localStorage.setItem('userId', response.data.userId);\n                localStorage.setItem('username', response.data.username);\n\n                // Get redirect path or default to dashboard\n                const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n                sessionStorage.removeItem('redirectAfterLogin');\n                navigate(redirectPath);\n            } else {\n                setError(response.data.message || 'OTP verification failed. Please try again.');\n            }\n        } catch (err) {\n            console.error('OTP verification error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else {\n                setError('An error occurred during OTP verification. Please try again.');\n            }\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const handleGoogleAuthVerification = async (e) => {\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n\n        try {\n            const response = await axios.post(\n                'verify_google_auth.php',\n                {\n                    email: userEmail,\n                    code: googleAuthCode\n                }\n            );\n\n            if (response.data.success) {\n                localStorage.setItem('userId', response.data.userId);\n                localStorage.setItem('username', response.data.username);\n\n                // Get redirect path or default to dashboard\n                const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n                sessionStorage.removeItem('redirectAfterLogin');\n                navigate(redirectPath);\n            } else {\n                setError(response.data.message || 'Verification failed. Please try again.');\n            }\n        } catch (err) {\n            console.error('Google Auth verification error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else {\n                setError('An error occurred during verification. Please try again.');\n            }\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const handleResendOtp = async () => {\n        setError('');\n        setOtpResending(true);\n\n        try {\n            const response = await axios.post(\n                'send_otp.php',\n                {\n                    email: userEmail\n                }\n            );\n\n            if (response.data.success) {\n                setOtpExpiry(response.data.expiresIn);\n                setOtpCountdown(response.data.expiresIn);\n                setError(''); // Clear any previous errors\n            } else {\n                setError(response.data.message || 'Failed to resend OTP. Please try again.');\n            }\n        } catch (err) {\n            console.error('Resend OTP error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else {\n                setError('An error occurred while resending OTP. Please try again.');\n            }\n        } finally {\n            setOtpResending(false);\n        }\n    };\n\n    const formatTime = (seconds) => {\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs < 10 ? '0' : ''}${secs}`;\n    };\n\n    // Render OTP verification form\n    if (showOtpVerification) {\n        return (\n            <UserAuthLayout\n                title=\"OTP Verification\"\n                subtitle=\"Enter the one-time password sent to your email\"\n            >\n                {error && <div className=\"user-auth-error-message\">{error}</div>}\n                <form onSubmit={handleOtpVerification} className=\"user-auth-form\">\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"otpCode\">OTP Code</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                type=\"text\"\n                                id=\"otpCode\"\n                                name=\"otpCode\"\n                                value={otpCode}\n                                onChange={(e) => setOtpCode(e.target.value)}\n                                placeholder=\"Enter 6-digit OTP code\"\n                                required\n                                autoComplete=\"one-time-code\"\n                                className=\"user-auth-otp-input\"\n                                maxLength=\"6\"\n                            />\n                            <i className=\"fas fa-key\"></i>\n                        </div>\n                    </div>\n                    {otpCountdown > 0 && (\n                        <div className=\"user-auth-countdown-timer\">\n                            OTP expires in: {formatTime(otpCountdown)}\n                        </div>\n                    )}\n                    <button type=\"submit\" className=\"user-auth-button\" disabled={isLoading}>\n                        {isLoading ? (\n                            <>\n                                <span className=\"user-auth-loading-spinner\"></span>\n                                Verifying...\n                            </>\n                        ) : (\n                            'Verify OTP'\n                        )}\n                    </button>\n                    <div className=\"user-auth-form-options center\">\n                        <button\n                            type=\"button\"\n                            className=\"user-auth-resend-button\"\n                            onClick={handleResendOtp}\n                            disabled={otpResending || otpCountdown > 0}\n                        >\n                            {otpResending ? (\n                                <>\n                                    <span className=\"user-auth-loading-spinner\"></span>\n                                    Resending...\n                                </>\n                            ) : (\n                                'Resend OTP'\n                            )}\n                        </button>\n                    </div>\n                    <div className=\"user-auth-footer\">\n                        <button\n                            type=\"button\"\n                            className=\"user-auth-button-secondary\"\n                            onClick={() => {\n                                setShowOtpVerification(false);\n                                setOtpCode('');\n                                setError('');\n                            }}\n                        >\n                            Back to Login\n                        </button>\n                    </div>\n                </form>\n            </UserAuthLayout>\n        );\n    }\n\n    // Render Google Authenticator verification form\n    if (showGoogleAuthVerification) {\n        return (\n            <UserAuthLayout\n                title=\"Two-Factor Authentication\"\n                subtitle=\"Enter the code from your Google Authenticator app\"\n            >\n                {error && <div className=\"user-auth-error-message\">{error}</div>}\n                <form onSubmit={handleGoogleAuthVerification} className=\"user-auth-form\">\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"googleAuthCode\">Authentication Code</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                type=\"text\"\n                                id=\"googleAuthCode\"\n                                name=\"googleAuthCode\"\n                                value={googleAuthCode}\n                                onChange={(e) => setGoogleAuthCode(e.target.value)}\n                                placeholder=\"Enter 6-digit code\"\n                                required\n                                autoComplete=\"one-time-code\"\n                                className=\"user-auth-otp-input\"\n                                maxLength=\"6\"\n                            />\n                            <i className=\"fas fa-shield-alt\"></i>\n                        </div>\n                    </div>\n                    <div className=\"user-auth-form-group\">\n                        <div className=\"user-auth-countdown-timer\">\n                            Open your Google Authenticator app and enter the 6-digit code for FanBet247\n                        </div>\n                    </div>\n                    <button type=\"submit\" className=\"user-auth-button\" disabled={isLoading}>\n                        {isLoading ? (\n                            <>\n                                <span className=\"user-auth-loading-spinner\"></span>\n                                Verifying...\n                            </>\n                        ) : (\n                            'Verify Code'\n                        )}\n                    </button>\n                    <div className=\"user-auth-footer\">\n                        <button\n                            type=\"button\"\n                            className=\"user-auth-button-secondary\"\n                            onClick={() => {\n                                setShowGoogleAuthVerification(false);\n                                setGoogleAuthCode('');\n                                setError('');\n                            }}\n                        >\n                            Back to Login\n                        </button>\n                    </div>\n                </form>\n            </UserAuthLayout>\n        );\n    }\n\n    // Render main login form\n    return (\n        <UserAuthLayout\n            title=\"Welcome Back\"\n            subtitle=\"Sign in to your account\"\n            variant=\"login\"\n        >\n            {error && <div className=\"user-auth-error-message\">{error}</div>}\n            <form onSubmit={handleSubmit} className=\"user-auth-form\">\n                <div className=\"user-auth-form-group\">\n                    <label htmlFor=\"usernameOrEmail\">Email or Username</label>\n                    <div className=\"user-auth-input-wrapper\">\n                        <input\n                            type=\"text\"\n                            id=\"usernameOrEmail\"\n                            name=\"usernameOrEmail\"\n                            value={credentials.usernameOrEmail}\n                            onChange={handleInputChange}\n                            placeholder=\"Enter your email or username\"\n                            required\n                        />\n                        <i className=\"fas fa-user\"></i>\n                    </div>\n                </div>\n                <div className=\"user-auth-form-group\">\n                    <label htmlFor=\"password\">Password</label>\n                    <div className=\"user-auth-input-wrapper\">\n                        <input\n                            type=\"password\"\n                            id=\"password\"\n                            name=\"password\"\n                            value={credentials.password}\n                            onChange={handleInputChange}\n                            placeholder=\"Enter your password\"\n                            required\n                        />\n                        <i className=\"fas fa-lock\"></i>\n                    </div>\n                </div>\n                <div className=\"user-auth-form-options end\">\n                    <Link to=\"/forgot-password\" className=\"user-auth-forgot-password\">\n                        Forgot Password?\n                    </Link>\n                </div>\n                <button type=\"submit\" className=\"user-auth-button\" disabled={isLoading}>\n                    {isLoading ? (\n                        <>\n                            <span className=\"user-auth-loading-spinner\"></span>\n                            Signing in...\n                        </>\n                    ) : (\n                        'Sign In'\n                    )}\n                </button>\n                <div className=\"user-auth-footer\">\n                    <p>Don't have an account? <Link to=\"/register\" className=\"user-auth-register-link\">Create one here</Link></p>\n                </div>\n            </form>\n        </UserAuthLayout>\n    );\n}\n\nexport default UserLogin;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAO,wBAAwB;AAC/B,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC;IAAEc,eAAe,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EACrF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMsC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACZ,MAAM6B,MAAM,GAAGS,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,IAAIV,MAAM,EAAE;MACRQ,QAAQ,CAAC,iBAAiB,CAAC;IAC/B;EACJ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACArC,SAAS,CAAC,MAAM;IACZ,IAAIwC,KAAK;IACT,IAAIP,YAAY,GAAG,CAAC,EAAE;MAClBO,KAAK,GAAGC,UAAU,CAAC,MAAMP,eAAe,CAACD,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;IACrE;IACA,OAAO,MAAMS,YAAY,CAACF,KAAK,CAAC;EACpC,CAAC,EAAE,CAACP,YAAY,CAAC,CAAC;EAElB,MAAMU,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCnC,cAAc,CAACoC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;IACpD,IAAI/B,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EAC3B,CAAC;EAED,MAAMiC,WAAW,GAAG,MAAAA,CAAOpB,MAAM,EAAEqB,KAAK,KAAK;IACzC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMlD,KAAK,CAACmD,IAAI,CAC7B,mBAAmB,EACnB;QACIvB,MAAM,EAAEA,MAAM;QACdqB,KAAK,EAAEA;MACX,CACJ,CAAC;MAED,IAAI,CAACC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACxB,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACG,OAAO,IAAI,oBAAoB,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAAC3C,KAAK,CAAC,iBAAiB,EAAE0C,GAAG,CAAC;MACrCzC,QAAQ,CAAC,uCAAuC,CAAC;MACjD,MAAMyC,GAAG;IACb;EACJ,CAAC;EAED,MAAME,YAAY,GAAG,MAAOf,CAAC,IAAK;IAC9BA,CAAC,CAACgB,cAAc,CAAC,CAAC;IAClB5C,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAMiC,QAAQ,GAAG,MAAMlD,KAAK,CAACmD,IAAI,CAC7B,yBAAyB,EACzBzC,WACJ,CAAC;MAED,IAAIwC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB;QACA,IAAIH,QAAQ,CAACE,IAAI,CAACQ,sBAAsB,EAAE;UACtC;UACAjC,YAAY,CAACuB,QAAQ,CAACE,IAAI,CAACH,KAAK,CAAC;UAEjC,IAAIC,QAAQ,CAACE,IAAI,CAACS,QAAQ,KAAK,KAAK,IAAIX,QAAQ,CAACE,IAAI,CAACS,QAAQ,KAAK,WAAW,EAAE;YAC5E;YACA,MAAMb,WAAW,CAACE,QAAQ,CAACE,IAAI,CAACxB,MAAM,EAAEsB,QAAQ,CAACE,IAAI,CAACH,KAAK,CAAC;YAC5D9B,sBAAsB,CAAC,IAAI,CAAC;YAC5BY,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YACnBE,eAAe,CAAC,GAAG,CAAC;UACxB,CAAC,MAAM,IAAIiB,QAAQ,CAACE,IAAI,CAACS,QAAQ,KAAK,KAAK,IAAIX,QAAQ,CAACE,IAAI,CAACS,QAAQ,KAAK,aAAa,EAAE;YACrF;YACAxC,6BAA6B,CAAC,IAAI,CAAC;UACvC,CAAC,MAAM,IAAI6B,QAAQ,CAACE,IAAI,CAACS,QAAQ,KAAK,SAAS,EAAE;YAC7C;YACA,MAAMb,WAAW,CAACE,QAAQ,CAACE,IAAI,CAACxB,MAAM,EAAEsB,QAAQ,CAACE,IAAI,CAACH,KAAK,CAAC;YAC5D9B,sBAAsB,CAAC,IAAI,CAAC;YAC5BY,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YACnBE,eAAe,CAAC,GAAG,CAAC;UACxB;QACJ,CAAC,MAAM;UACH;UACAI,YAAY,CAACyB,OAAO,CAAC,QAAQ,EAAEZ,QAAQ,CAACE,IAAI,CAACxB,MAAM,CAAC;UACpDS,YAAY,CAACyB,OAAO,CAAC,UAAU,EAAEZ,QAAQ,CAACE,IAAI,CAACW,QAAQ,CAAC;;UAExD;UACA,MAAMC,YAAY,GAAGC,cAAc,CAAC3B,OAAO,CAAC,oBAAoB,CAAC,IAAI,iBAAiB;UACtF2B,cAAc,CAACC,UAAU,CAAC,oBAAoB,CAAC;UAC/C9B,QAAQ,CAAC4B,YAAY,CAAC;QAC1B;MACJ,CAAC,MAAM;QACHjD,QAAQ,CAACmC,QAAQ,CAACE,IAAI,CAACG,OAAO,IAAI,iCAAiC,CAAC;MACxE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAW,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,cAAA;MACVb,OAAO,CAAC3C,KAAK,CAAC,cAAc,EAAE0C,GAAG,CAAC;MAClC,KAAAW,aAAA,GAAIX,GAAG,CAACN,QAAQ,cAAAiB,aAAA,gBAAAC,kBAAA,GAAZD,aAAA,CAAcf,IAAI,cAAAgB,kBAAA,eAAlBA,kBAAA,CAAoBb,OAAO,EAAE;QAC7BxC,QAAQ,CAACyC,GAAG,CAACN,QAAQ,CAACE,IAAI,CAACG,OAAO,CAAC;MACvC,CAAC,MAAM,IAAI,EAAAc,cAAA,GAAAb,GAAG,CAACN,QAAQ,cAAAmB,cAAA,uBAAZA,cAAA,CAAcE,MAAM,MAAK,GAAG,EAAE;QACrCxD,QAAQ,CAAC,oCAAoC,CAAC;MAClD,CAAC,MAAM,IAAI,EAAAuD,cAAA,GAAAd,GAAG,CAACN,QAAQ,cAAAoB,cAAA,uBAAZA,cAAA,CAAcC,MAAM,MAAK,GAAG,EAAE;QACrCxD,QAAQ,CAAC,+CAA+C,CAAC;MAC7D,CAAC,MAAM;QACHA,QAAQ,CAAC,4CAA4C,CAAC;MAC1D;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMuD,qBAAqB,GAAG,MAAO7B,CAAC,IAAK;IACvCA,CAAC,CAACgB,cAAc,CAAC,CAAC;IAClB5C,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAMiC,QAAQ,GAAG,MAAMlD,KAAK,CAACmD,IAAI,CAC7B,gBAAgB,EAChB;QACIF,KAAK,EAAEvB,SAAS;QAChB+C,GAAG,EAAEnD;MACT,CACJ,CAAC;MAED,IAAI4B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBhB,YAAY,CAACyB,OAAO,CAAC,QAAQ,EAAEZ,QAAQ,CAACE,IAAI,CAACxB,MAAM,CAAC;QACpDS,YAAY,CAACyB,OAAO,CAAC,UAAU,EAAEZ,QAAQ,CAACE,IAAI,CAACW,QAAQ,CAAC;;QAExD;QACA,MAAMC,YAAY,GAAGC,cAAc,CAAC3B,OAAO,CAAC,oBAAoB,CAAC,IAAI,iBAAiB;QACtF2B,cAAc,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC/C9B,QAAQ,CAAC4B,YAAY,CAAC;MAC1B,CAAC,MAAM;QACHjD,QAAQ,CAACmC,QAAQ,CAACE,IAAI,CAACG,OAAO,IAAI,4CAA4C,CAAC;MACnF;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAkB,cAAA,EAAAC,mBAAA;MACVlB,OAAO,CAAC3C,KAAK,CAAC,yBAAyB,EAAE0C,GAAG,CAAC;MAC7C,KAAAkB,cAAA,GAAIlB,GAAG,CAACN,QAAQ,cAAAwB,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAActB,IAAI,cAAAuB,mBAAA,eAAlBA,mBAAA,CAAoBpB,OAAO,EAAE;QAC7BxC,QAAQ,CAACyC,GAAG,CAACN,QAAQ,CAACE,IAAI,CAACG,OAAO,CAAC;MACvC,CAAC,MAAM;QACHxC,QAAQ,CAAC,8DAA8D,CAAC;MAC5E;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAM2D,4BAA4B,GAAG,MAAOjC,CAAC,IAAK;IAC9CA,CAAC,CAACgB,cAAc,CAAC,CAAC;IAClB5C,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAMiC,QAAQ,GAAG,MAAMlD,KAAK,CAACmD,IAAI,CAC7B,wBAAwB,EACxB;QACIF,KAAK,EAAEvB,SAAS;QAChBmD,IAAI,EAAErD;MACV,CACJ,CAAC;MAED,IAAI0B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBhB,YAAY,CAACyB,OAAO,CAAC,QAAQ,EAAEZ,QAAQ,CAACE,IAAI,CAACxB,MAAM,CAAC;QACpDS,YAAY,CAACyB,OAAO,CAAC,UAAU,EAAEZ,QAAQ,CAACE,IAAI,CAACW,QAAQ,CAAC;;QAExD;QACA,MAAMC,YAAY,GAAGC,cAAc,CAAC3B,OAAO,CAAC,oBAAoB,CAAC,IAAI,iBAAiB;QACtF2B,cAAc,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC/C9B,QAAQ,CAAC4B,YAAY,CAAC;MAC1B,CAAC,MAAM;QACHjD,QAAQ,CAACmC,QAAQ,CAACE,IAAI,CAACG,OAAO,IAAI,wCAAwC,CAAC;MAC/E;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAsB,cAAA,EAAAC,mBAAA;MACVtB,OAAO,CAAC3C,KAAK,CAAC,iCAAiC,EAAE0C,GAAG,CAAC;MACrD,KAAAsB,cAAA,GAAItB,GAAG,CAACN,QAAQ,cAAA4B,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAc1B,IAAI,cAAA2B,mBAAA,eAAlBA,mBAAA,CAAoBxB,OAAO,EAAE;QAC7BxC,QAAQ,CAACyC,GAAG,CAACN,QAAQ,CAACE,IAAI,CAACG,OAAO,CAAC;MACvC,CAAC,MAAM;QACHxC,QAAQ,CAAC,0DAA0D,CAAC;MACxE;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAM+D,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChCjE,QAAQ,CAAC,EAAE,CAAC;IACZoB,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACA,MAAMe,QAAQ,GAAG,MAAMlD,KAAK,CAACmD,IAAI,CAC7B,cAAc,EACd;QACIF,KAAK,EAAEvB;MACX,CACJ,CAAC;MAED,IAAIwB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBtB,YAAY,CAACmB,QAAQ,CAACE,IAAI,CAAC6B,SAAS,CAAC;QACrChD,eAAe,CAACiB,QAAQ,CAACE,IAAI,CAAC6B,SAAS,CAAC;QACxClE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,MAAM;QACHA,QAAQ,CAACmC,QAAQ,CAACE,IAAI,CAACG,OAAO,IAAI,yCAAyC,CAAC;MAChF;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAA0B,cAAA,EAAAC,mBAAA;MACV1B,OAAO,CAAC3C,KAAK,CAAC,mBAAmB,EAAE0C,GAAG,CAAC;MACvC,KAAA0B,cAAA,GAAI1B,GAAG,CAACN,QAAQ,cAAAgC,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAc9B,IAAI,cAAA+B,mBAAA,eAAlBA,mBAAA,CAAoB5B,OAAO,EAAE;QAC7BxC,QAAQ,CAACyC,GAAG,CAACN,QAAQ,CAACE,IAAI,CAACG,OAAO,CAAC;MACvC,CAAC,MAAM;QACHxC,QAAQ,CAAC,0DAA0D,CAAC;MACxE;IACJ,CAAC,SAAS;MACNoB,eAAe,CAAC,KAAK,CAAC;IAC1B;EACJ,CAAC;EAED,MAAMiD,UAAU,GAAIC,OAAO,IAAK;IAC5B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAGA,IAAI,EAAE;EACnD,CAAC;;EAED;EACA,IAAIvE,mBAAmB,EAAE;IACrB,oBACIb,OAAA,CAACF,cAAc;MACXuF,KAAK,EAAC,kBAAkB;MACxBC,QAAQ,EAAC,gDAAgD;MAAAC,QAAA,GAExD9E,KAAK,iBAAIT,OAAA;QAAKwF,SAAS,EAAC,yBAAyB;QAAAD,QAAA,EAAE9E;MAAK;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChE5F,OAAA;QAAM6F,QAAQ,EAAE1B,qBAAsB;QAACqB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7DvF,OAAA;UAAKwF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCvF,OAAA;YAAO8F,OAAO,EAAC,SAAS;YAAAP,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzC5F,OAAA;YAAKwF,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBACpCvF,OAAA;cACI+F,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,SAAS;cACZzD,IAAI,EAAC,SAAS;cACdC,KAAK,EAAEvB,OAAQ;cACfgF,QAAQ,EAAG3D,CAAC,IAAKpB,UAAU,CAACoB,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAC5C0D,WAAW,EAAC,wBAAwB;cACpCC,QAAQ;cACRC,YAAY,EAAC,eAAe;cAC5BZ,SAAS,EAAC,qBAAqB;cAC/Ba,SAAS,EAAC;YAAG;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACF5F,OAAA;cAAGwF,SAAS,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACLjE,YAAY,GAAG,CAAC,iBACb3B,OAAA;UAAKwF,SAAS,EAAC,2BAA2B;UAAAD,QAAA,GAAC,kBACvB,EAACR,UAAU,CAACpD,YAAY,CAAC;QAAA;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACR,eACD5F,OAAA;UAAQ+F,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,kBAAkB;UAACc,QAAQ,EAAE3F,SAAU;UAAA4E,QAAA,EAClE5E,SAAS,gBACNX,OAAA,CAAAE,SAAA;YAAAqF,QAAA,gBACIvF,OAAA;cAAMwF,SAAS,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,gBAEvD;UAAA,eAAE,CAAC,GAEH;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACT5F,OAAA;UAAKwF,SAAS,EAAC,+BAA+B;UAAAD,QAAA,eAC1CvF,OAAA;YACI+F,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,yBAAyB;YACnCe,OAAO,EAAE5B,eAAgB;YACzB2B,QAAQ,EAAEzE,YAAY,IAAIF,YAAY,GAAG,CAAE;YAAA4D,QAAA,EAE1C1D,YAAY,gBACT7B,OAAA,CAAAE,SAAA;cAAAqF,QAAA,gBACIvF,OAAA;gBAAMwF,SAAS,EAAC;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,gBAEvD;YAAA,eAAE,CAAC,GAEH;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACN5F,OAAA;UAAKwF,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC7BvF,OAAA;YACI+F,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,4BAA4B;YACtCe,OAAO,EAAEA,CAAA,KAAM;cACXzF,sBAAsB,CAAC,KAAK,CAAC;cAC7BI,UAAU,CAAC,EAAE,CAAC;cACdR,QAAQ,CAAC,EAAE,CAAC;YAChB,CAAE;YAAA6E,QAAA,EACL;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAEzB;;EAEA;EACA,IAAI7E,0BAA0B,EAAE;IAC5B,oBACIf,OAAA,CAACF,cAAc;MACXuF,KAAK,EAAC,2BAA2B;MACjCC,QAAQ,EAAC,mDAAmD;MAAAC,QAAA,GAE3D9E,KAAK,iBAAIT,OAAA;QAAKwF,SAAS,EAAC,yBAAyB;QAAAD,QAAA,EAAE9E;MAAK;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChE5F,OAAA;QAAM6F,QAAQ,EAAEtB,4BAA6B;QAACiB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBACpEvF,OAAA;UAAKwF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCvF,OAAA;YAAO8F,OAAO,EAAC,gBAAgB;YAAAP,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3D5F,OAAA;YAAKwF,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBACpCvF,OAAA;cACI+F,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,gBAAgB;cACnBzD,IAAI,EAAC,gBAAgB;cACrBC,KAAK,EAAErB,cAAe;cACtB8E,QAAQ,EAAG3D,CAAC,IAAKlB,iBAAiB,CAACkB,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cACnD0D,WAAW,EAAC,oBAAoB;cAChCC,QAAQ;cACRC,YAAY,EAAC,eAAe;cAC5BZ,SAAS,EAAC,qBAAqB;cAC/Ba,SAAS,EAAC;YAAG;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACF5F,OAAA;cAAGwF,SAAS,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN5F,OAAA;UAAKwF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,eACjCvF,OAAA;YAAKwF,SAAS,EAAC,2BAA2B;YAAAD,QAAA,EAAC;UAE3C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN5F,OAAA;UAAQ+F,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,kBAAkB;UAACc,QAAQ,EAAE3F,SAAU;UAAA4E,QAAA,EAClE5E,SAAS,gBACNX,OAAA,CAAAE,SAAA;YAAAqF,QAAA,gBACIvF,OAAA;cAAMwF,SAAS,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,gBAEvD;UAAA,eAAE,CAAC,GAEH;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACT5F,OAAA;UAAKwF,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC7BvF,OAAA;YACI+F,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,4BAA4B;YACtCe,OAAO,EAAEA,CAAA,KAAM;cACXvF,6BAA6B,CAAC,KAAK,CAAC;cACpCI,iBAAiB,CAAC,EAAE,CAAC;cACrBV,QAAQ,CAAC,EAAE,CAAC;YAChB,CAAE;YAAA6E,QAAA,EACL;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAEzB;;EAEA;EACA,oBACI5F,OAAA,CAACF,cAAc;IACXuF,KAAK,EAAC,cAAc;IACpBC,QAAQ,EAAC,yBAAyB;IAClCkB,OAAO,EAAC,OAAO;IAAAjB,QAAA,GAEd9E,KAAK,iBAAIT,OAAA;MAAKwF,SAAS,EAAC,yBAAyB;MAAAD,QAAA,EAAE9E;IAAK;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAChE5F,OAAA;MAAM6F,QAAQ,EAAExC,YAAa;MAACmC,SAAS,EAAC,gBAAgB;MAAAD,QAAA,gBACpDvF,OAAA;QAAKwF,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBACjCvF,OAAA;UAAO8F,OAAO,EAAC,iBAAiB;UAAAP,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1D5F,OAAA;UAAKwF,SAAS,EAAC,yBAAyB;UAAAD,QAAA,gBACpCvF,OAAA;YACI+F,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,iBAAiB;YACpBzD,IAAI,EAAC,iBAAiB;YACtBC,KAAK,EAAEnC,WAAW,CAACE,eAAgB;YACnC0F,QAAQ,EAAE5D,iBAAkB;YAC5B6D,WAAW,EAAC,8BAA8B;YAC1CC,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACF5F,OAAA;YAAGwF,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN5F,OAAA;QAAKwF,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBACjCvF,OAAA;UAAO8F,OAAO,EAAC,UAAU;UAAAP,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C5F,OAAA;UAAKwF,SAAS,EAAC,yBAAyB;UAAAD,QAAA,gBACpCvF,OAAA;YACI+F,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbzD,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEnC,WAAW,CAACG,QAAS;YAC5ByF,QAAQ,EAAE5D,iBAAkB;YAC5B6D,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACF5F,OAAA;YAAGwF,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN5F,OAAA;QAAKwF,SAAS,EAAC,4BAA4B;QAAAD,QAAA,eACvCvF,OAAA,CAACH,IAAI;UAAC4G,EAAE,EAAC,kBAAkB;UAACjB,SAAS,EAAC,2BAA2B;UAAAD,QAAA,EAAC;QAElE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN5F,OAAA;QAAQ+F,IAAI,EAAC,QAAQ;QAACP,SAAS,EAAC,kBAAkB;QAACc,QAAQ,EAAE3F,SAAU;QAAA4E,QAAA,EAClE5E,SAAS,gBACNX,OAAA,CAAAE,SAAA;UAAAqF,QAAA,gBACIvF,OAAA;YAAMwF,SAAS,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,iBAEvD;QAAA,eAAE,CAAC,GAEH;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACT5F,OAAA;QAAKwF,SAAS,EAAC,kBAAkB;QAAAD,QAAA,eAC7BvF,OAAA;UAAAuF,QAAA,GAAG,yBAAuB,eAAAvF,OAAA,CAACH,IAAI;YAAC4G,EAAE,EAAC,WAAW;YAACjB,SAAS,EAAC,yBAAyB;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEzB;AAACxF,EAAA,CA5aQD,SAAS;EAAA,QAaGP,WAAW;AAAA;AAAA8G,EAAA,GAbvBvG,SAAS;AA8alB,eAAeA,SAAS;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}