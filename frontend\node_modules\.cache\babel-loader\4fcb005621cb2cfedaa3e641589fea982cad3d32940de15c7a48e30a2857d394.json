{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\NewWelcomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport './NewWelcomePage.css';\n\n// Completely independent welcome page - NO MainLayout or any existing components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewWelcomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [data, setData] = useState({\n    leagues: [],\n    challenges: [],\n    recentBets: []\n  });\n  const [loading, setLoading] = useState({\n    leagues: true,\n    challenges: true,\n    bets: true\n  });\n  const [error, setError] = useState(null);\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  // Hero slider images\n  const sliderImages = [process.env.PUBLIC_URL + '/slider/Slider1.png', process.env.PUBLIC_URL + '/slider/Slider2.png'];\n\n  // Fetch top 7 leagues\n  const fetchLeagues = async () => {\n    try {\n      const response = await axios.get('/handlers/league_management.php');\n      if (response.data.status === 200 && response.data.data) {\n        // Get top 7 leagues by member count\n        const topLeagues = response.data.data.sort((a, b) => (b.member_count || 0) - (a.member_count || 0)).slice(0, 7);\n        setData(prev => ({\n          ...prev,\n          leagues: topLeagues\n        }));\n      } else {\n        // Fallback data if API fails\n        setData(prev => ({\n          ...prev,\n          leagues: getFallbackLeagues()\n        }));\n      }\n    } catch (err) {\n      console.error('Error fetching leagues:', err);\n      // Set fallback data on error\n      setData(prev => ({\n        ...prev,\n        leagues: getFallbackLeagues()\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        leagues: false\n      }));\n    }\n  };\n\n  // Fetch live challenges\n  const fetchChallenges = async () => {\n    try {\n      const response = await axios.get('/handlers/recent_challenges.php');\n      if (response.data.success && response.data.challenges) {\n        setData(prev => ({\n          ...prev,\n          challenges: response.data.challenges.slice(0, 6)\n        }));\n      } else {\n        // Fallback data if API fails\n        setData(prev => ({\n          ...prev,\n          challenges: getFallbackChallenges()\n        }));\n      }\n    } catch (err) {\n      console.error('Error fetching challenges:', err);\n      // Set fallback data on error\n      setData(prev => ({\n        ...prev,\n        challenges: getFallbackChallenges()\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        challenges: false\n      }));\n    }\n  };\n\n  // Fetch recent bets\n  const fetchRecentBets = async () => {\n    try {\n      const response = await axios.get('/handlers/welcome_recent_bets.php');\n      if (response.data.success && response.data.bets) {\n        setData(prev => ({\n          ...prev,\n          recentBets: response.data.bets.slice(0, 6)\n        }));\n      } else {\n        // Fallback data if API fails\n        setData(prev => ({\n          ...prev,\n          recentBets: getFallbackBets()\n        }));\n      }\n    } catch (err) {\n      console.error('Error fetching recent bets:', err);\n      // Set fallback data on error\n      setData(prev => ({\n        ...prev,\n        recentBets: getFallbackBets()\n      }));\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        bets: false\n      }));\n    }\n  };\n  useEffect(() => {\n    fetchLeagues();\n    fetchChallenges();\n    fetchRecentBets();\n  }, []);\n\n  // Auto-advance slider\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % sliderImages.length);\n    }, 5000);\n    return () => clearInterval(interval);\n  }, [sliderImages.length]);\n  const handleChallengeClick = challengeId => {\n    sessionStorage.setItem('redirectAfterLogin', `/user/join-challenge/${challengeId}`);\n    navigate('/login');\n  };\n  const getTeamLogo = (teamName, logoPath = null) => {\n    if (logoPath) {\n      return `${API_BASE_URL}/uploads/teams/${logoPath}`;\n    }\n    if (teamName) {\n      return `${API_BASE_URL}/uploads/teams/${teamName.toLowerCase().replace(/\\s+/g, '_')}.png`;\n    }\n    return '/default-team.png';\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n\n  // Fallback data functions\n  const getFallbackLeagues = () => [{\n    league_id: 1,\n    name: 'Premier League',\n    member_count: 1250,\n    min_bet_amount: 100,\n    max_bet_amount: 10000,\n    icon_path: null\n  }, {\n    league_id: 2,\n    name: 'Champions League',\n    member_count: 980,\n    min_bet_amount: 500,\n    max_bet_amount: 50000,\n    icon_path: null\n  }, {\n    league_id: 3,\n    name: 'La Liga',\n    member_count: 875,\n    min_bet_amount: 200,\n    max_bet_amount: 15000,\n    icon_path: null\n  }, {\n    league_id: 4,\n    name: 'Serie A',\n    member_count: 720,\n    min_bet_amount: 150,\n    max_bet_amount: 12000,\n    icon_path: null\n  }, {\n    league_id: 5,\n    name: 'Bundesliga',\n    member_count: 650,\n    min_bet_amount: 100,\n    max_bet_amount: 8000,\n    icon_path: null\n  }, {\n    league_id: 6,\n    name: 'Ligue 1',\n    member_count: 580,\n    min_bet_amount: 100,\n    max_bet_amount: 7000,\n    icon_path: null\n  }, {\n    league_id: 7,\n    name: 'Europa League',\n    member_count: 520,\n    min_bet_amount: 200,\n    max_bet_amount: 20000,\n    icon_path: null\n  }];\n  const getFallbackChallenges = () => [{\n    challenge_id: 1,\n    team_a: 'Chelsea Fc',\n    team_b: 'Manchester United',\n    odds_team_a: '1.80',\n    odds_draw: '3.20',\n    odds_team_b: '2.10',\n    status: 'Open'\n  }, {\n    challenge_id: 2,\n    team_a: 'Liverpool Fc',\n    team_b: 'Arsenal',\n    odds_team_a: '2.00',\n    odds_draw: '3.40',\n    odds_team_b: '1.90',\n    status: 'Open'\n  }, {\n    challenge_id: 3,\n    team_a: 'Manchester City',\n    team_b: 'Tottenham',\n    odds_team_a: '1.60',\n    odds_draw: '3.80',\n    odds_team_b: '2.40',\n    status: 'Open'\n  }];\n  const getFallbackBets = () => [{\n    bet_id: 'demo001',\n    user1_name: 'DemoUser1',\n    user2_name: 'DemoUser2',\n    bet_amount: 1000,\n    bet_status: 'completed'\n  }, {\n    bet_id: 'demo002',\n    user1_name: 'TestPlayer',\n    user2_name: null,\n    bet_amount: 500,\n    bet_status: 'open'\n  }, {\n    bet_id: 'demo003',\n    user1_name: 'BetMaster',\n    user2_name: 'ProGamer',\n    bet_amount: 2000,\n    bet_status: 'completed'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fresh-welcome-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"fresh-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fresh-header-container\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"fresh-logo\",\n          children: \"FanBet247\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"fresh-nav\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"fresh-nav-link active\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/live-challenges\",\n            className: \"fresh-nav-link\",\n            children: \"Live\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"fresh-nav-link\",\n            children: \"Leagues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/leaderboard\",\n            className: \"fresh-nav-link\",\n            children: \"Leaders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"fresh-nav-link\",\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fresh-auth-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"fresh-btn fresh-btn-login\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"fresh-btn fresh-btn-register\",\n            children: \"Register\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fresh-layout\",\n      children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n        className: \"fresh-sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fresh-sidebar-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Top Leagues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fresh-leagues-list\",\n          children: loading.leagues ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-loading\",\n            children: \"Loading leagues...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this) : data.leagues.map((league, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-league-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-league-rank\",\n              children: [\"#\", index + 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-league-icon\",\n              children: league.icon_path ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `${API_BASE_URL}/uploads/leagues/icons/${league.icon_path}`,\n                alt: league.name,\n                onError: e => e.target.src = '/default-league.png'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fresh-league-placeholder\",\n                children: \"\\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-league-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: league.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [league.member_count || 0, \" members\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"fresh-league-range\",\n                children: [formatCurrency(league.min_bet_amount), \" - \", formatCurrency(league.max_bet_amount), \" FC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this)]\n          }, league.league_id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"fresh-sidebar-cta\",\n          children: \"Login to View All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"fresh-main-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"fresh-hero-section\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-hero-slider\",\n            children: [sliderImages.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `fresh-slide ${index === currentSlide ? 'active' : ''}`,\n              style: {\n                backgroundImage: `url(${image})`\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-slider-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"fresh-slider-btn prev\",\n                onClick: () => setCurrentSlide(prev => prev === 0 ? sliderImages.length - 1 : prev - 1),\n                children: \"\\u276E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"fresh-slider-btn next\",\n                onClick: () => setCurrentSlide(prev => (prev + 1) % sliderImages.length),\n                children: \"\\u276F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-slider-indicators\",\n              children: sliderImages.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `fresh-indicator ${index === currentSlide ? 'active' : ''}`,\n                onClick: () => setCurrentSlide(index)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"fresh-challenges-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"LIVE CHALLENGES\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"fresh-section-link\",\n              children: \"Login to Bet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-challenges-container\",\n            children: loading.challenges ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-loading\",\n              children: \"Loading challenges...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this) : data.challenges.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-empty-state\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No active challenges at the moment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-challenges-grid\",\n              children: data.challenges.map(challenge => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fresh-challenge-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fresh-match-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-team\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: getTeamLogo(challenge.team_a, challenge.team_a_logo),\n                      alt: challenge.team_a,\n                      onError: e => e.target.src = '/default-team.png'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: challenge.team_a\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-vs\",\n                    children: \"VS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-team\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: getTeamLogo(challenge.team_b, challenge.team_b_logo),\n                      alt: challenge.team_b,\n                      onError: e => e.target.src = '/default-team.png'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: challenge.team_b\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fresh-odds\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-odd\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Home\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: challenge.odds_team_a\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-odd\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Draw\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: challenge.odds_draw\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-odd\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Away\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: challenge.odds_team_b\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"fresh-bet-btn\",\n                  onClick: () => handleChallengeClick(challenge.challenge_id),\n                  children: \"LOGIN TO BET\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 23\n                }, this)]\n              }, challenge.challenge_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"fresh-recent-bets-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"RECENT BETS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/recent-bets\",\n              className: \"fresh-section-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-bets-container\",\n            children: loading.bets ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-loading\",\n              children: \"Loading recent bets...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this) : data.recentBets.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-empty-state\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No recent bets to display\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-bets-grid\",\n              children: data.recentBets.map(bet => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fresh-bet-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fresh-bet-ref\",\n                  children: [\"REF: \", bet.bet_id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fresh-bet-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-bet-user\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: bet.user1_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [formatCurrency(bet.bet_amount), \" FC\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-bet-vs\",\n                    children: \"VS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fresh-bet-user\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: bet.user2_name || 'Open'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 380,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [bet.user2_name ? formatCurrency(bet.bet_amount) : 'Waiting', \" FC\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fresh-bet-status\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `fresh-status ${bet.bet_status}`,\n                    children: bet.bet_status.toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 23\n                }, this)]\n              }, bet.bet_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"fresh-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fresh-footer-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fresh-footer-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-footer-brand\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"FanBet247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"18+ Bet Responsibly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-footer-column\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Quick Links\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/live-challenges\",\n                children: \"Live Matches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/upcoming-matches\",\n                children: \"Upcoming\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/leaderboard\",\n                children: \"Leaderboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fresh-footer-column\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/help\",\n                children: \"Help Center\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/responsible-gambling\",\n                children: \"Responsible Gaming\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/terms\",\n                children: \"Terms\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-footer-social\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://twitter.com/fanbet247\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: \"Twitter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://facebook.com/fanbet247\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: \"Facebook\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://t.me/fanbet247\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: \"Telegram\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fresh-footer-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\xA9 2024 FanBet247. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fresh-footer-badges\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2713 SSL Secured\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2713 Licensed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n_s(NewWelcomePage, \"DlF0zW4RuF0Yr/U5Z3qs5cXFiO8=\", false, function () {\n  return [useNavigate];\n});\n_c = NewWelcomePage;\nexport default NewWelcomePage;\nvar _c;\n$RefreshReg$(_c, \"NewWelcomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "axios", "API_BASE_URL", "jsxDEV", "_jsxDEV", "NewWelcomePage", "_s", "navigate", "data", "setData", "leagues", "challenges", "recentBets", "loading", "setLoading", "bets", "error", "setError", "currentSlide", "setCurrentSlide", "sliderImages", "process", "env", "PUBLIC_URL", "fetchLeagues", "response", "get", "status", "topLeagues", "sort", "a", "b", "member_count", "slice", "prev", "getFallbackLeagues", "err", "console", "fetchChallenges", "success", "getFallbackChallenges", "fetchRecentBets", "getFallbackBets", "interval", "setInterval", "length", "clearInterval", "handleChallengeClick", "challengeId", "sessionStorage", "setItem", "getTeamLogo", "teamName", "logoPath", "toLowerCase", "replace", "formatCurrency", "amount", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "league_id", "name", "min_bet_amount", "max_bet_amount", "icon_path", "challenge_id", "team_a", "team_b", "odds_team_a", "odds_draw", "odds_team_b", "bet_id", "user1_name", "user2_name", "bet_amount", "bet_status", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "league", "index", "src", "alt", "onError", "e", "target", "image", "style", "backgroundImage", "onClick", "_", "challenge", "team_a_logo", "team_b_logo", "bet", "toUpperCase", "href", "rel", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/NewWelcomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport axios from '../utils/axiosConfig';\nimport { API_BASE_URL } from '../config';\nimport './NewWelcomePage.css';\n\n// Completely independent welcome page - NO MainLayout or any existing components\n\nconst NewWelcomePage = () => {\n  const navigate = useNavigate();\n  const [data, setData] = useState({\n    leagues: [],\n    challenges: [],\n    recentBets: []\n  });\n  const [loading, setLoading] = useState({\n    leagues: true,\n    challenges: true,\n    bets: true\n  });\n  const [error, setError] = useState(null);\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  // Hero slider images\n  const sliderImages = [\n    process.env.PUBLIC_URL + '/slider/Slider1.png',\n    process.env.PUBLIC_URL + '/slider/Slider2.png'\n  ];\n\n  // Fetch top 7 leagues\n  const fetchLeagues = async () => {\n    try {\n      const response = await axios.get('/handlers/league_management.php');\n      if (response.data.status === 200 && response.data.data) {\n        // Get top 7 leagues by member count\n        const topLeagues = response.data.data\n          .sort((a, b) => (b.member_count || 0) - (a.member_count || 0))\n          .slice(0, 7);\n        setData(prev => ({ ...prev, leagues: topLeagues }));\n      } else {\n        // Fallback data if API fails\n        setData(prev => ({ ...prev, leagues: getFallbackLeagues() }));\n      }\n    } catch (err) {\n      console.error('Error fetching leagues:', err);\n      // Set fallback data on error\n      setData(prev => ({ ...prev, leagues: getFallbackLeagues() }));\n    } finally {\n      setLoading(prev => ({ ...prev, leagues: false }));\n    }\n  };\n\n  // Fetch live challenges\n  const fetchChallenges = async () => {\n    try {\n      const response = await axios.get('/handlers/recent_challenges.php');\n      if (response.data.success && response.data.challenges) {\n        setData(prev => ({ ...prev, challenges: response.data.challenges.slice(0, 6) }));\n      } else {\n        // Fallback data if API fails\n        setData(prev => ({ ...prev, challenges: getFallbackChallenges() }));\n      }\n    } catch (err) {\n      console.error('Error fetching challenges:', err);\n      // Set fallback data on error\n      setData(prev => ({ ...prev, challenges: getFallbackChallenges() }));\n    } finally {\n      setLoading(prev => ({ ...prev, challenges: false }));\n    }\n  };\n\n  // Fetch recent bets\n  const fetchRecentBets = async () => {\n    try {\n      const response = await axios.get('/handlers/welcome_recent_bets.php');\n      if (response.data.success && response.data.bets) {\n        setData(prev => ({ ...prev, recentBets: response.data.bets.slice(0, 6) }));\n      } else {\n        // Fallback data if API fails\n        setData(prev => ({ ...prev, recentBets: getFallbackBets() }));\n      }\n    } catch (err) {\n      console.error('Error fetching recent bets:', err);\n      // Set fallback data on error\n      setData(prev => ({ ...prev, recentBets: getFallbackBets() }));\n    } finally {\n      setLoading(prev => ({ ...prev, bets: false }));\n    }\n  };\n\n  useEffect(() => {\n    fetchLeagues();\n    fetchChallenges();\n    fetchRecentBets();\n  }, []);\n\n  // Auto-advance slider\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % sliderImages.length);\n    }, 5000);\n    return () => clearInterval(interval);\n  }, [sliderImages.length]);\n\n  const handleChallengeClick = (challengeId) => {\n    sessionStorage.setItem('redirectAfterLogin', `/user/join-challenge/${challengeId}`);\n    navigate('/login');\n  };\n\n  const getTeamLogo = (teamName, logoPath = null) => {\n    if (logoPath) {\n      return `${API_BASE_URL}/uploads/teams/${logoPath}`;\n    }\n    if (teamName) {\n      return `${API_BASE_URL}/uploads/teams/${teamName.toLowerCase().replace(/\\s+/g, '_')}.png`;\n    }\n    return '/default-team.png';\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n\n  // Fallback data functions\n  const getFallbackLeagues = () => [\n    { league_id: 1, name: 'Premier League', member_count: 1250, min_bet_amount: 100, max_bet_amount: 10000, icon_path: null },\n    { league_id: 2, name: 'Champions League', member_count: 980, min_bet_amount: 500, max_bet_amount: 50000, icon_path: null },\n    { league_id: 3, name: 'La Liga', member_count: 875, min_bet_amount: 200, max_bet_amount: 15000, icon_path: null },\n    { league_id: 4, name: 'Serie A', member_count: 720, min_bet_amount: 150, max_bet_amount: 12000, icon_path: null },\n    { league_id: 5, name: 'Bundesliga', member_count: 650, min_bet_amount: 100, max_bet_amount: 8000, icon_path: null },\n    { league_id: 6, name: 'Ligue 1', member_count: 580, min_bet_amount: 100, max_bet_amount: 7000, icon_path: null },\n    { league_id: 7, name: 'Europa League', member_count: 520, min_bet_amount: 200, max_bet_amount: 20000, icon_path: null }\n  ];\n\n  const getFallbackChallenges = () => [\n    {\n      challenge_id: 1,\n      team_a: 'Chelsea Fc',\n      team_b: 'Manchester United',\n      odds_team_a: '1.80',\n      odds_draw: '3.20',\n      odds_team_b: '2.10',\n      status: 'Open'\n    },\n    {\n      challenge_id: 2,\n      team_a: 'Liverpool Fc',\n      team_b: 'Arsenal',\n      odds_team_a: '2.00',\n      odds_draw: '3.40',\n      odds_team_b: '1.90',\n      status: 'Open'\n    },\n    {\n      challenge_id: 3,\n      team_a: 'Manchester City',\n      team_b: 'Tottenham',\n      odds_team_a: '1.60',\n      odds_draw: '3.80',\n      odds_team_b: '2.40',\n      status: 'Open'\n    }\n  ];\n\n  const getFallbackBets = () => [\n    {\n      bet_id: 'demo001',\n      user1_name: 'DemoUser1',\n      user2_name: 'DemoUser2',\n      bet_amount: 1000,\n      bet_status: 'completed'\n    },\n    {\n      bet_id: 'demo002',\n      user1_name: 'TestPlayer',\n      user2_name: null,\n      bet_amount: 500,\n      bet_status: 'open'\n    },\n    {\n      bet_id: 'demo003',\n      user1_name: 'BetMaster',\n      user2_name: 'ProGamer',\n      bet_amount: 2000,\n      bet_status: 'completed'\n    }\n  ];\n\n  return (\n    <div className=\"fresh-welcome-page\">\n      {/* Header */}\n      <header className=\"fresh-header\">\n        <div className=\"fresh-header-container\">\n          <Link to=\"/\" className=\"fresh-logo\">\n            FanBet247\n          </Link>\n          <nav className=\"fresh-nav\">\n            <Link to=\"/\" className=\"fresh-nav-link active\">Home</Link>\n            <Link to=\"/live-challenges\" className=\"fresh-nav-link\">Live</Link>\n            <Link to=\"/login\" className=\"fresh-nav-link\">Leagues</Link>\n            <Link to=\"/leaderboard\" className=\"fresh-nav-link\">Leaders</Link>\n            <Link to=\"/about\" className=\"fresh-nav-link\">About</Link>\n          </nav>\n          <div className=\"fresh-auth-buttons\">\n            <Link to=\"/login\" className=\"fresh-btn fresh-btn-login\">Login</Link>\n            <Link to=\"/register\" className=\"fresh-btn fresh-btn-register\">Register</Link>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"fresh-layout\">\n        {/* Sidebar with Top 7 Leagues */}\n        <aside className=\"fresh-sidebar\">\n          <div className=\"fresh-sidebar-header\">\n            <h3>Top Leagues</h3>\n          </div>\n          <div className=\"fresh-leagues-list\">\n            {loading.leagues ? (\n              <div className=\"fresh-loading\">Loading leagues...</div>\n            ) : (\n              data.leagues.map((league, index) => (\n                <div key={league.league_id} className=\"fresh-league-item\">\n                  <div className=\"fresh-league-rank\">#{index + 1}</div>\n                  <div className=\"fresh-league-icon\">\n                    {league.icon_path ? (\n                      <img \n                        src={`${API_BASE_URL}/uploads/leagues/icons/${league.icon_path}`}\n                        alt={league.name}\n                        onError={(e) => e.target.src = '/default-league.png'}\n                      />\n                    ) : (\n                      <div className=\"fresh-league-placeholder\">🏆</div>\n                    )}\n                  </div>\n                  <div className=\"fresh-league-info\">\n                    <h4>{league.name}</h4>\n                    <p>{league.member_count || 0} members</p>\n                    <span className=\"fresh-league-range\">\n                      {formatCurrency(league.min_bet_amount)} - {formatCurrency(league.max_bet_amount)} FC\n                    </span>\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n          <Link to=\"/login\" className=\"fresh-sidebar-cta\">\n            Login to View All\n          </Link>\n        </aside>\n\n        {/* Main Content */}\n        <main className=\"fresh-main-content\">\n          {/* Hero Slider */}\n          <section className=\"fresh-hero-section\">\n            <div className=\"fresh-hero-slider\">\n              {sliderImages.map((image, index) => (\n                <div \n                  key={index}\n                  className={`fresh-slide ${index === currentSlide ? 'active' : ''}`}\n                  style={{ backgroundImage: `url(${image})` }}\n                />\n              ))}\n              <div className=\"fresh-slider-controls\">\n                <button \n                  className=\"fresh-slider-btn prev\"\n                  onClick={() => setCurrentSlide(prev => prev === 0 ? sliderImages.length - 1 : prev - 1)}\n                >\n                  ❮\n                </button>\n                <button \n                  className=\"fresh-slider-btn next\"\n                  onClick={() => setCurrentSlide(prev => (prev + 1) % sliderImages.length)}\n                >\n                  ❯\n                </button>\n              </div>\n              <div className=\"fresh-slider-indicators\">\n                {sliderImages.map((_, index) => (\n                  <button\n                    key={index}\n                    className={`fresh-indicator ${index === currentSlide ? 'active' : ''}`}\n                    onClick={() => setCurrentSlide(index)}\n                  />\n                ))}\n              </div>\n            </div>\n          </section>\n\n          {/* Live Challenges Section */}\n          <section className=\"fresh-challenges-section\">\n            <div className=\"fresh-section-header\">\n              <h2>LIVE CHALLENGES</h2>\n              <Link to=\"/login\" className=\"fresh-section-link\">Login to Bet</Link>\n            </div>\n            <div className=\"fresh-challenges-container\">\n              {loading.challenges ? (\n                <div className=\"fresh-loading\">Loading challenges...</div>\n              ) : data.challenges.length === 0 ? (\n                <div className=\"fresh-empty-state\">\n                  <p>No active challenges at the moment</p>\n                </div>\n              ) : (\n                <div className=\"fresh-challenges-grid\">\n                  {data.challenges.map(challenge => (\n                    <div key={challenge.challenge_id} className=\"fresh-challenge-card\">\n                      <div className=\"fresh-match-info\">\n                        <div className=\"fresh-team\">\n                          <img\n                            src={getTeamLogo(challenge.team_a, challenge.team_a_logo)}\n                            alt={challenge.team_a}\n                            onError={(e) => e.target.src = '/default-team.png'}\n                          />\n                          <span>{challenge.team_a}</span>\n                        </div>\n                        <div className=\"fresh-vs\">VS</div>\n                        <div className=\"fresh-team\">\n                          <img\n                            src={getTeamLogo(challenge.team_b, challenge.team_b_logo)}\n                            alt={challenge.team_b}\n                            onError={(e) => e.target.src = '/default-team.png'}\n                          />\n                          <span>{challenge.team_b}</span>\n                        </div>\n                      </div>\n                      <div className=\"fresh-odds\">\n                        <div className=\"fresh-odd\">\n                          <span>Home</span>\n                          <span>{challenge.odds_team_a}</span>\n                        </div>\n                        <div className=\"fresh-odd\">\n                          <span>Draw</span>\n                          <span>{challenge.odds_draw}</span>\n                        </div>\n                        <div className=\"fresh-odd\">\n                          <span>Away</span>\n                          <span>{challenge.odds_team_b}</span>\n                        </div>\n                      </div>\n                      <button \n                        className=\"fresh-bet-btn\"\n                        onClick={() => handleChallengeClick(challenge.challenge_id)}\n                      >\n                        LOGIN TO BET\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </section>\n\n          {/* Recent Bets Section */}\n          <section className=\"fresh-recent-bets-section\">\n            <div className=\"fresh-section-header\">\n              <h2>RECENT BETS</h2>\n              <Link to=\"/user/recent-bets\" className=\"fresh-section-link\">View All</Link>\n            </div>\n            <div className=\"fresh-bets-container\">\n              {loading.bets ? (\n                <div className=\"fresh-loading\">Loading recent bets...</div>\n              ) : data.recentBets.length === 0 ? (\n                <div className=\"fresh-empty-state\">\n                  <p>No recent bets to display</p>\n                </div>\n              ) : (\n                <div className=\"fresh-bets-grid\">\n                  {data.recentBets.map(bet => (\n                    <div key={bet.bet_id} className=\"fresh-bet-card\">\n                      <div className=\"fresh-bet-ref\">REF: {bet.bet_id}</div>\n                      <div className=\"fresh-bet-details\">\n                        <div className=\"fresh-bet-user\">\n                          <span>{bet.user1_name}</span>\n                          <span>{formatCurrency(bet.bet_amount)} FC</span>\n                        </div>\n                        <div className=\"fresh-bet-vs\">VS</div>\n                        <div className=\"fresh-bet-user\">\n                          <span>{bet.user2_name || 'Open'}</span>\n                          <span>{bet.user2_name ? formatCurrency(bet.bet_amount) : 'Waiting'} FC</span>\n                        </div>\n                      </div>\n                      <div className=\"fresh-bet-status\">\n                        <span className={`fresh-status ${bet.bet_status}`}>\n                          {bet.bet_status.toUpperCase()}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </section>\n        </main>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"fresh-footer\">\n        <div className=\"fresh-footer-container\">\n          <div className=\"fresh-footer-content\">\n            <div className=\"fresh-footer-brand\">\n              <h3>FanBet247</h3>\n              <p>18+ Bet Responsibly</p>\n            </div>\n            <div className=\"fresh-footer-links\">\n              <div className=\"fresh-footer-column\">\n                <h4>Quick Links</h4>\n                <Link to=\"/live-challenges\">Live Matches</Link>\n                <Link to=\"/upcoming-matches\">Upcoming</Link>\n                <Link to=\"/leaderboard\">Leaderboard</Link>\n              </div>\n              <div className=\"fresh-footer-column\">\n                <h4>Support</h4>\n                <Link to=\"/help\">Help Center</Link>\n                <Link to=\"/responsible-gambling\">Responsible Gaming</Link>\n                <Link to=\"/terms\">Terms</Link>\n              </div>\n            </div>\n            <div className=\"fresh-footer-social\">\n              <a href=\"https://twitter.com/fanbet247\" target=\"_blank\" rel=\"noopener noreferrer\">Twitter</a>\n              <a href=\"https://facebook.com/fanbet247\" target=\"_blank\" rel=\"noopener noreferrer\">Facebook</a>\n              <a href=\"https://t.me/fanbet247\" target=\"_blank\" rel=\"noopener noreferrer\">Telegram</a>\n            </div>\n          </div>\n          <div className=\"fresh-footer-bottom\">\n            <p>© 2024 FanBet247. All rights reserved.</p>\n            <div className=\"fresh-footer-badges\">\n              <span>✓ SSL Secured</span>\n              <span>✓ Licensed</span>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default NewWelcomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,sBAAsB;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC;IAC/Ba,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC;IACrCa,OAAO,EAAE,IAAI;IACbC,UAAU,EAAE,IAAI;IAChBI,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAMuB,YAAY,GAAG,CACnBC,OAAO,CAACC,GAAG,CAACC,UAAU,GAAG,qBAAqB,EAC9CF,OAAO,CAACC,GAAG,CAACC,UAAU,GAAG,qBAAqB,CAC/C;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,iCAAiC,CAAC;MACnE,IAAID,QAAQ,CAACjB,IAAI,CAACmB,MAAM,KAAK,GAAG,IAAIF,QAAQ,CAACjB,IAAI,CAACA,IAAI,EAAE;QACtD;QACA,MAAMoB,UAAU,GAAGH,QAAQ,CAACjB,IAAI,CAACA,IAAI,CAClCqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,YAAY,IAAI,CAAC,KAAKF,CAAC,CAACE,YAAY,IAAI,CAAC,CAAC,CAAC,CAC7DC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACdxB,OAAO,CAACyB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAExB,OAAO,EAAEkB;QAAW,CAAC,CAAC,CAAC;MACrD,CAAC,MAAM;QACL;QACAnB,OAAO,CAACyB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAExB,OAAO,EAAEyB,kBAAkB,CAAC;QAAE,CAAC,CAAC,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACrB,KAAK,CAAC,yBAAyB,EAAEoB,GAAG,CAAC;MAC7C;MACA3B,OAAO,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExB,OAAO,EAAEyB,kBAAkB,CAAC;MAAE,CAAC,CAAC,CAAC;IAC/D,CAAC,SAAS;MACRrB,UAAU,CAACoB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExB,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAM4B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,iCAAiC,CAAC;MACnE,IAAID,QAAQ,CAACjB,IAAI,CAAC+B,OAAO,IAAId,QAAQ,CAACjB,IAAI,CAACG,UAAU,EAAE;QACrDF,OAAO,CAACyB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEvB,UAAU,EAAEc,QAAQ,CAACjB,IAAI,CAACG,UAAU,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC;MAClF,CAAC,MAAM;QACL;QACAxB,OAAO,CAACyB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEvB,UAAU,EAAE6B,qBAAqB,CAAC;QAAE,CAAC,CAAC,CAAC;MACrE;IACF,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZC,OAAO,CAACrB,KAAK,CAAC,4BAA4B,EAAEoB,GAAG,CAAC;MAChD;MACA3B,OAAO,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEvB,UAAU,EAAE6B,qBAAqB,CAAC;MAAE,CAAC,CAAC,CAAC;IACrE,CAAC,SAAS;MACR1B,UAAU,CAACoB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEvB,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM8B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,mCAAmC,CAAC;MACrE,IAAID,QAAQ,CAACjB,IAAI,CAAC+B,OAAO,IAAId,QAAQ,CAACjB,IAAI,CAACO,IAAI,EAAE;QAC/CN,OAAO,CAACyB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEtB,UAAU,EAAEa,QAAQ,CAACjB,IAAI,CAACO,IAAI,CAACkB,KAAK,CAAC,CAAC,EAAE,CAAC;QAAE,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACL;QACAxB,OAAO,CAACyB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEtB,UAAU,EAAE8B,eAAe,CAAC;QAAE,CAAC,CAAC,CAAC;MAC/D;IACF,CAAC,CAAC,OAAON,GAAG,EAAE;MACZC,OAAO,CAACrB,KAAK,CAAC,6BAA6B,EAAEoB,GAAG,CAAC;MACjD;MACA3B,OAAO,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEtB,UAAU,EAAE8B,eAAe,CAAC;MAAE,CAAC,CAAC,CAAC;IAC/D,CAAC,SAAS;MACR5B,UAAU,CAACoB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEnB,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAChD;EACF,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACd0B,YAAY,CAAC,CAAC;IACdc,eAAe,CAAC,CAAC;IACjBG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3C,SAAS,CAAC,MAAM;IACd,MAAM6C,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCzB,eAAe,CAACe,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAId,YAAY,CAACyB,MAAM,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACvB,YAAY,CAACyB,MAAM,CAAC,CAAC;EAEzB,MAAME,oBAAoB,GAAIC,WAAW,IAAK;IAC5CC,cAAc,CAACC,OAAO,CAAC,oBAAoB,EAAE,wBAAwBF,WAAW,EAAE,CAAC;IACnFzC,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAM4C,WAAW,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,GAAG,IAAI,KAAK;IACjD,IAAIA,QAAQ,EAAE;MACZ,OAAO,GAAGnD,YAAY,kBAAkBmD,QAAQ,EAAE;IACpD;IACA,IAAID,QAAQ,EAAE;MACZ,OAAO,GAAGlD,YAAY,kBAAkBkD,QAAQ,CAACE,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM;IAC3F;IACA,OAAO,mBAAmB;EAC5B,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;;EAED;EACA,MAAMtB,kBAAkB,GAAGA,CAAA,KAAM,CAC/B;IAAE4B,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE,gBAAgB;IAAEhC,YAAY,EAAE,IAAI;IAAEiC,cAAc,EAAE,GAAG;IAAEC,cAAc,EAAE,KAAK;IAAEC,SAAS,EAAE;EAAK,CAAC,EACzH;IAAEJ,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE,kBAAkB;IAAEhC,YAAY,EAAE,GAAG;IAAEiC,cAAc,EAAE,GAAG;IAAEC,cAAc,EAAE,KAAK;IAAEC,SAAS,EAAE;EAAK,CAAC,EAC1H;IAAEJ,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEhC,YAAY,EAAE,GAAG;IAAEiC,cAAc,EAAE,GAAG;IAAEC,cAAc,EAAE,KAAK;IAAEC,SAAS,EAAE;EAAK,CAAC,EACjH;IAAEJ,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEhC,YAAY,EAAE,GAAG;IAAEiC,cAAc,EAAE,GAAG;IAAEC,cAAc,EAAE,KAAK;IAAEC,SAAS,EAAE;EAAK,CAAC,EACjH;IAAEJ,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE,YAAY;IAAEhC,YAAY,EAAE,GAAG;IAAEiC,cAAc,EAAE,GAAG;IAAEC,cAAc,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAK,CAAC,EACnH;IAAEJ,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE,SAAS;IAAEhC,YAAY,EAAE,GAAG;IAAEiC,cAAc,EAAE,GAAG;IAAEC,cAAc,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAK,CAAC,EAChH;IAAEJ,SAAS,EAAE,CAAC;IAAEC,IAAI,EAAE,eAAe;IAAEhC,YAAY,EAAE,GAAG;IAAEiC,cAAc,EAAE,GAAG;IAAEC,cAAc,EAAE,KAAK;IAAEC,SAAS,EAAE;EAAK,CAAC,CACxH;EAED,MAAM3B,qBAAqB,GAAGA,CAAA,KAAM,CAClC;IACE4B,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,mBAAmB;IAC3BC,WAAW,EAAE,MAAM;IACnBC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,MAAM;IACnB9C,MAAM,EAAE;EACV,CAAC,EACD;IACEyC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,cAAc;IACtBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,MAAM;IACnBC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,MAAM;IACnB9C,MAAM,EAAE;EACV,CAAC,EACD;IACEyC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,iBAAiB;IACzBC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,MAAM;IACnBC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,MAAM;IACnB9C,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMe,eAAe,GAAGA,CAAA,KAAM,CAC5B;IACEgC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE;EACd,CAAC,EACD;IACEJ,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,GAAG;IACfC,UAAU,EAAE;EACd,CAAC,EACD;IACEJ,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,UAAU;IACtBC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACE1E,OAAA;IAAK2E,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjC5E,OAAA;MAAQ2E,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC9B5E,OAAA;QAAK2E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC5E,OAAA,CAACL,IAAI;UAACkF,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAEpC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPjF,OAAA;UAAK2E,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5E,OAAA,CAACL,IAAI;YAACkF,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1DjF,OAAA,CAACL,IAAI;YAACkF,EAAE,EAAC,kBAAkB;YAACF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClEjF,OAAA,CAACL,IAAI;YAACkF,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DjF,OAAA,CAACL,IAAI;YAACkF,EAAE,EAAC,cAAc;YAACF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjEjF,OAAA,CAACL,IAAI;YAACkF,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNjF,OAAA;UAAK2E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC5E,OAAA,CAACL,IAAI;YAACkF,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpEjF,OAAA,CAACL,IAAI;YAACkF,EAAE,EAAC,WAAW;YAACF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETjF,OAAA;MAAK2E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3B5E,OAAA;QAAO2E,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC9B5E,OAAA;UAAK2E,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnC5E,OAAA;YAAA4E,QAAA,EAAI;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNjF,OAAA;UAAK2E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAChCnE,OAAO,CAACH,OAAO,gBACdN,OAAA;YAAK2E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAEvD7E,IAAI,CAACE,OAAO,CAAC4E,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC7BpF,OAAA;YAA4B2E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACvD5E,OAAA;cAAK2E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAAC,GAAC,EAACQ,KAAK,GAAG,CAAC;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDjF,OAAA;cAAK2E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC/BO,MAAM,CAACpB,SAAS,gBACf/D,OAAA;gBACEqF,GAAG,EAAE,GAAGvF,YAAY,0BAA0BqF,MAAM,CAACpB,SAAS,EAAG;gBACjEuB,GAAG,EAAEH,MAAM,CAACvB,IAAK;gBACjB2B,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG;cAAsB;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,gBAEFjF,OAAA;gBAAK2E,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAClD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNjF,OAAA;cAAK2E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5E,OAAA;gBAAA4E,QAAA,EAAKO,MAAM,CAACvB;cAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtBjF,OAAA;gBAAA4E,QAAA,GAAIO,MAAM,CAACvD,YAAY,IAAI,CAAC,EAAC,UAAQ;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzCjF,OAAA;gBAAM2E,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GACjCxB,cAAc,CAAC+B,MAAM,CAACtB,cAAc,CAAC,EAAC,KAAG,EAACT,cAAc,CAAC+B,MAAM,CAACrB,cAAc,CAAC,EAAC,KACnF;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAnBEE,MAAM,CAACxB,SAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBrB,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNjF,OAAA,CAACL,IAAI;UAACkF,EAAE,EAAC,QAAQ;UAACF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAEhD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRjF,OAAA;QAAM2E,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAElC5E,OAAA;UAAS2E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACrC5E,OAAA;YAAK2E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAC/B5D,YAAY,CAACkE,GAAG,CAAC,CAACQ,KAAK,EAAEN,KAAK,kBAC7BpF,OAAA;cAEE2E,SAAS,EAAE,eAAeS,KAAK,KAAKtE,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;cACnE6E,KAAK,EAAE;gBAAEC,eAAe,EAAE,OAAOF,KAAK;cAAI;YAAE,GAFvCN,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGX,CACF,CAAC,eACFjF,OAAA;cAAK2E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpC5E,OAAA;gBACE2E,SAAS,EAAC,uBAAuB;gBACjCkB,OAAO,EAAEA,CAAA,KAAM9E,eAAe,CAACe,IAAI,IAAIA,IAAI,KAAK,CAAC,GAAGd,YAAY,CAACyB,MAAM,GAAG,CAAC,GAAGX,IAAI,GAAG,CAAC,CAAE;gBAAA8C,QAAA,EACzF;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjF,OAAA;gBACE2E,SAAS,EAAC,uBAAuB;gBACjCkB,OAAO,EAAEA,CAAA,KAAM9E,eAAe,CAACe,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAId,YAAY,CAACyB,MAAM,CAAE;gBAAAmC,QAAA,EAC1E;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNjF,OAAA;cAAK2E,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EACrC5D,YAAY,CAACkE,GAAG,CAAC,CAACY,CAAC,EAAEV,KAAK,kBACzBpF,OAAA;gBAEE2E,SAAS,EAAE,mBAAmBS,KAAK,KAAKtE,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACvE+E,OAAO,EAAEA,CAAA,KAAM9E,eAAe,CAACqE,KAAK;cAAE,GAFjCA,KAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGVjF,OAAA;UAAS2E,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBAC3C5E,OAAA;YAAK2E,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC5E,OAAA;cAAA4E,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBjF,OAAA,CAACL,IAAI;cAACkF,EAAE,EAAC,QAAQ;cAACF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACNjF,OAAA;YAAK2E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxCnE,OAAO,CAACF,UAAU,gBACjBP,OAAA;cAAK2E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GACxD7E,IAAI,CAACG,UAAU,CAACkC,MAAM,KAAK,CAAC,gBAC9BzC,OAAA;cAAK2E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC5E,OAAA;gBAAA4E,QAAA,EAAG;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,gBAENjF,OAAA;cAAK2E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnCxE,IAAI,CAACG,UAAU,CAAC2E,GAAG,CAACa,SAAS,iBAC5B/F,OAAA;gBAAkC2E,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBAChE5E,OAAA;kBAAK2E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B5E,OAAA;oBAAK2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB5E,OAAA;sBACEqF,GAAG,EAAEtC,WAAW,CAACgD,SAAS,CAAC9B,MAAM,EAAE8B,SAAS,CAACC,WAAW,CAAE;sBAC1DV,GAAG,EAAES,SAAS,CAAC9B,MAAO;sBACtBsB,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG;oBAAoB;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACFjF,OAAA;sBAAA4E,QAAA,EAAOmB,SAAS,CAAC9B;oBAAM;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNjF,OAAA;oBAAK2E,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClCjF,OAAA;oBAAK2E,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB5E,OAAA;sBACEqF,GAAG,EAAEtC,WAAW,CAACgD,SAAS,CAAC7B,MAAM,EAAE6B,SAAS,CAACE,WAAW,CAAE;sBAC1DX,GAAG,EAAES,SAAS,CAAC7B,MAAO;sBACtBqB,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG;oBAAoB;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACFjF,OAAA;sBAAA4E,QAAA,EAAOmB,SAAS,CAAC7B;oBAAM;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjF,OAAA;kBAAK2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5E,OAAA;oBAAK2E,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxB5E,OAAA;sBAAA4E,QAAA,EAAM;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBjF,OAAA;sBAAA4E,QAAA,EAAOmB,SAAS,CAAC5B;oBAAW;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACNjF,OAAA;oBAAK2E,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxB5E,OAAA;sBAAA4E,QAAA,EAAM;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBjF,OAAA;sBAAA4E,QAAA,EAAOmB,SAAS,CAAC3B;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACNjF,OAAA;oBAAK2E,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxB5E,OAAA;sBAAA4E,QAAA,EAAM;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBjF,OAAA;sBAAA4E,QAAA,EAAOmB,SAAS,CAAC1B;oBAAW;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjF,OAAA;kBACE2E,SAAS,EAAC,eAAe;kBACzBkB,OAAO,EAAEA,CAAA,KAAMlD,oBAAoB,CAACoD,SAAS,CAAC/B,YAAY,CAAE;kBAAAY,QAAA,EAC7D;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAvCDc,SAAS,CAAC/B,YAAY;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwC3B,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGVjF,OAAA;UAAS2E,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAC5C5E,OAAA;YAAK2E,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC5E,OAAA;cAAA4E,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBjF,OAAA,CAACL,IAAI;cAACkF,EAAE,EAAC,mBAAmB;cAACF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACNjF,OAAA;YAAK2E,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAClCnE,OAAO,CAACE,IAAI,gBACXX,OAAA;cAAK2E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GACzD7E,IAAI,CAACI,UAAU,CAACiC,MAAM,KAAK,CAAC,gBAC9BzC,OAAA;cAAK2E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC5E,OAAA;gBAAA4E,QAAA,EAAG;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,gBAENjF,OAAA;cAAK2E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC7BxE,IAAI,CAACI,UAAU,CAAC0E,GAAG,CAACgB,GAAG,iBACtBlG,OAAA;gBAAsB2E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC9C5E,OAAA;kBAAK2E,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,OAAK,EAACsB,GAAG,CAAC5B,MAAM;gBAAA;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtDjF,OAAA;kBAAK2E,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC5E,OAAA;oBAAK2E,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7B5E,OAAA;sBAAA4E,QAAA,EAAOsB,GAAG,CAAC3B;oBAAU;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC7BjF,OAAA;sBAAA4E,QAAA,GAAOxB,cAAc,CAAC8C,GAAG,CAACzB,UAAU,CAAC,EAAC,KAAG;oBAAA;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACNjF,OAAA;oBAAK2E,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtCjF,OAAA;oBAAK2E,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7B5E,OAAA;sBAAA4E,QAAA,EAAOsB,GAAG,CAAC1B,UAAU,IAAI;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACvCjF,OAAA;sBAAA4E,QAAA,GAAOsB,GAAG,CAAC1B,UAAU,GAAGpB,cAAc,CAAC8C,GAAG,CAACzB,UAAU,CAAC,GAAG,SAAS,EAAC,KAAG;oBAAA;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjF,OAAA;kBAAK2E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/B5E,OAAA;oBAAM2E,SAAS,EAAE,gBAAgBuB,GAAG,CAACxB,UAAU,EAAG;oBAAAE,QAAA,EAC/CsB,GAAG,CAACxB,UAAU,CAACyB,WAAW,CAAC;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAjBEiB,GAAG,CAAC5B,MAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkBf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNjF,OAAA;MAAQ2E,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC9B5E,OAAA;QAAK2E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC5E,OAAA;UAAK2E,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC5E,OAAA;YAAK2E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC5E,OAAA;cAAA4E,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBjF,OAAA;cAAA4E,QAAA,EAAG;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACNjF,OAAA;YAAK2E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC5E,OAAA;cAAK2E,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClC5E,OAAA;gBAAA4E,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBjF,OAAA,CAACL,IAAI;gBAACkF,EAAE,EAAC,kBAAkB;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CjF,OAAA,CAACL,IAAI;gBAACkF,EAAE,EAAC,mBAAmB;gBAAAD,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CjF,OAAA,CAACL,IAAI;gBAACkF,EAAE,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNjF,OAAA;cAAK2E,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClC5E,OAAA;gBAAA4E,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBjF,OAAA,CAACL,IAAI;gBAACkF,EAAE,EAAC,OAAO;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnCjF,OAAA,CAACL,IAAI;gBAACkF,EAAE,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DjF,OAAA,CAACL,IAAI;gBAACkF,EAAE,EAAC,QAAQ;gBAAAD,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjF,OAAA;YAAK2E,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC5E,OAAA;cAAGoG,IAAI,EAAC,+BAA+B;cAACX,MAAM,EAAC,QAAQ;cAACY,GAAG,EAAC,qBAAqB;cAAAzB,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7FjF,OAAA;cAAGoG,IAAI,EAAC,gCAAgC;cAACX,MAAM,EAAC,QAAQ;cAACY,GAAG,EAAC,qBAAqB;cAAAzB,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/FjF,OAAA;cAAGoG,IAAI,EAAC,wBAAwB;cAACX,MAAM,EAAC,QAAQ;cAACY,GAAG,EAAC,qBAAqB;cAAAzB,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjF,OAAA;UAAK2E,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC5E,OAAA;YAAA4E,QAAA,EAAG;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7CjF,OAAA;YAAK2E,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC5E,OAAA;cAAA4E,QAAA,EAAM;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BjF,OAAA;cAAA4E,QAAA,EAAM;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC/E,EAAA,CA5aID,cAAc;EAAA,QACDL,WAAW;AAAA;AAAA0G,EAAA,GADxBrG,cAAc;AA8apB,eAAeA,cAAc;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}