{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserLogin.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { useNavigate, Link, useSearchParams } from 'react-router-dom';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction UserLogin() {\n  _s();\n  const [credentials, setCredentials] = useState({\n    usernameOrEmail: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [showOtpVerification, setShowOtpVerification] = useState(false);\n  const [showGoogleAuthVerification, setShowGoogleAuthVerification] = useState(false);\n  const [otpCode, setOtpCode] = useState('');\n  const [googleAuthCode, setGoogleAuthCode] = useState('');\n  const [userEmail, setUserEmail] = useState('');\n  const [userId, setUserId] = useState(null);\n  const [otpExpiry, setOtpExpiry] = useState(null);\n  const [otpCountdown, setOtpCountdown] = useState(0);\n  const [otpResending, setOtpResending] = useState(false);\n  const [notification, setNotification] = useState(null);\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n\n  // Redirect if already logged in\n  useEffect(() => {\n    const userId = localStorage.getItem('userId');\n    if (userId) {\n      navigate('/user/dashboard');\n    }\n  }, [navigate]);\n\n  // Check for logout success parameter\n  useEffect(() => {\n    const logoutParam = searchParams.get('logout');\n    if (logoutParam === 'success') {\n      setNotification({\n        message: 'You have been successfully logged out',\n        type: 'success'\n      });\n\n      // Clear the URL parameter\n      const newUrl = window.location.pathname;\n      window.history.replaceState({}, '', newUrl);\n\n      // Auto-hide notification after 5 seconds\n      setTimeout(() => {\n        setNotification(null);\n      }, 5000);\n    }\n  }, [searchParams]);\n\n  // OTP countdown timer\n  useEffect(() => {\n    let timer;\n    if (otpCountdown > 0) {\n      timer = setTimeout(() => setOtpCountdown(otpCountdown - 1), 1000);\n    }\n    return () => clearTimeout(timer);\n  }, [otpCountdown]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setCredentials(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    if (error) setError('');\n  };\n  const sendOtpCode = async (userId, email) => {\n    try {\n      const response = await axios.post('user_send_otp.php', {\n        userId: userId,\n        email: email\n      });\n      if (!response.data.success) {\n        throw new Error(response.data.message || 'Failed to send OTP');\n      }\n    } catch (err) {\n      console.error('OTP send error:', err);\n      setError('Failed to send OTP. Please try again.');\n      throw err;\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('user_login_enhanced.php', credentials);\n      if (response.data.success) {\n        // Check if additional authentication is required\n        if (response.data.requiresAdditionalAuth) {\n          // Store user data for additional auth steps\n          setUserEmail(response.data.email);\n          setUserId(response.data.userId);\n          if (response.data.authType === 'otp' || response.data.authType === 'email_otp') {\n            // OTP already sent by backend, just show verification form\n            setShowOtpVerification(true);\n            setOtpExpiry(300); // 5 minutes\n            setOtpCountdown(300);\n          } else if (response.data.authType === '2fa' || response.data.authType === 'google_auth') {\n            // Show Google Authenticator verification form\n            setShowGoogleAuthVerification(true);\n          } else if (response.data.authType === 'otp_2fa') {\n            // OTP already sent by backend, start with OTP verification\n            setShowOtpVerification(true);\n            setOtpExpiry(300); // 5 minutes\n            setOtpCountdown(300);\n          }\n        } else {\n          // No additional auth required, proceed with login\n          localStorage.setItem('userId', response.data.userId);\n          localStorage.setItem('username', response.data.username);\n\n          // Get redirect path or default to dashboard\n          const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n          sessionStorage.removeItem('redirectAfterLogin');\n          navigate(redirectPath);\n        }\n      } else {\n        setError(response.data.message || 'Login failed. Please try again.');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response3;\n      console.error('Login error:', err);\n      if ((_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.message) {\n        setError(err.response.data.message);\n      } else if (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401) {\n        setError('Invalid username/email or password');\n      } else if (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 400) {\n        setError('Please enter both username/email and password');\n      } else {\n        setError('An error occurred. Please try again later.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleOtpVerification = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('user_verify_otp.php', {\n        userId: userId,\n        otp_code: otpCode\n      });\n      if (response.data.success) {\n        localStorage.setItem('userId', response.data.userId);\n        localStorage.setItem('username', response.data.username);\n\n        // Get redirect path or default to dashboard\n        const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n        sessionStorage.removeItem('redirectAfterLogin');\n        navigate(redirectPath);\n      } else {\n        setError(response.data.message || 'OTP verification failed. Please try again.');\n      }\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      console.error('OTP verification error:', err);\n      if ((_err$response4 = err.response) !== null && _err$response4 !== void 0 && (_err$response4$data = _err$response4.data) !== null && _err$response4$data !== void 0 && _err$response4$data.message) {\n        setError(err.response.data.message);\n      } else {\n        setError('An error occurred during OTP verification. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleGoogleAuthVerification = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('verify_google_auth.php', {\n        email: userEmail,\n        code: googleAuthCode\n      });\n      if (response.data.success) {\n        localStorage.setItem('userId', response.data.userId);\n        localStorage.setItem('username', response.data.username);\n\n        // Get redirect path or default to dashboard\n        const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n        sessionStorage.removeItem('redirectAfterLogin');\n        navigate(redirectPath);\n      } else {\n        setError(response.data.message || 'Verification failed. Please try again.');\n      }\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      console.error('Google Auth verification error:', err);\n      if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.message) {\n        setError(err.response.data.message);\n      } else {\n        setError('An error occurred during verification. Please try again.');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleResendOtp = async () => {\n    setError('');\n    setOtpResending(true);\n    try {\n      const response = await axios.post('user_send_otp.php', {\n        userId: userId,\n        email: userEmail\n      });\n      if (response.data.success) {\n        setOtpExpiry(300); // 5 minutes\n        setOtpCountdown(300);\n        setError(''); // Clear any previous errors\n      } else {\n        setError(response.data.message || 'Failed to resend OTP. Please try again.');\n      }\n    } catch (err) {\n      var _err$response6, _err$response6$data;\n      console.error('Resend OTP error:', err);\n      if ((_err$response6 = err.response) !== null && _err$response6 !== void 0 && (_err$response6$data = _err$response6.data) !== null && _err$response6$data !== void 0 && _err$response6$data.message) {\n        setError(err.response.data.message);\n      } else {\n        setError('An error occurred while resending OTP. Please try again.');\n      }\n    } finally {\n      setOtpResending(false);\n    }\n  };\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;\n  };\n\n  // Render OTP verification form\n  if (showOtpVerification) {\n    return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n      title: \"OTP Verification\",\n      subtitle: \"Enter the one-time password sent to your email\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 27\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleOtpVerification,\n        className: \"user-auth-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"otpCode\",\n            children: \"OTP Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-auth-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"otpCode\",\n              name: \"otpCode\",\n              value: otpCode,\n              onChange: e => setOtpCode(e.target.value),\n              placeholder: \"Enter 6-digit OTP code\",\n              required: true,\n              autoComplete: \"one-time-code\",\n              className: \"user-auth-otp-input\",\n              maxLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-key\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 21\n        }, this), otpCountdown > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-countdown-timer\",\n          children: [\"OTP expires in: \", formatTime(otpCountdown)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"user-auth-button\",\n          disabled: isLoading,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-auth-loading-spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 33\n            }, this), \"Verifying...\"]\n          }, void 0, true) : 'Verify OTP'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-options center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"user-auth-resend-button\",\n            onClick: handleResendOtp,\n            disabled: otpResending || otpCountdown > 0,\n            children: otpResending ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"user-auth-loading-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 37\n              }, this), \"Resending...\"]\n            }, void 0, true) : 'Resend OTP'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"user-auth-button-secondary\",\n            onClick: () => {\n              setShowOtpVerification(false);\n              setOtpCode('');\n              setError('');\n            },\n            children: \"Back to Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Render Google Authenticator verification form\n  if (showGoogleAuthVerification) {\n    return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n      title: \"Two-Factor Authentication\",\n      subtitle: \"Enter the code from your Google Authenticator app\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 27\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleGoogleAuthVerification,\n        className: \"user-auth-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"googleAuthCode\",\n            children: \"Authentication Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-auth-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"googleAuthCode\",\n              name: \"googleAuthCode\",\n              value: googleAuthCode,\n              onChange: e => setGoogleAuthCode(e.target.value),\n              placeholder: \"Enter 6-digit code\",\n              required: true,\n              autoComplete: \"one-time-code\",\n              className: \"user-auth-otp-input\",\n              maxLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-shield-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-auth-countdown-timer\",\n            children: \"Open your Google Authenticator app and enter the 6-digit code for FanBet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"user-auth-button\",\n          disabled: isLoading,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-auth-loading-spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 33\n            }, this), \"Verifying...\"]\n          }, void 0, true) : 'Verify Code'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"user-auth-button-secondary\",\n            onClick: () => {\n              setShowGoogleAuthVerification(false);\n              setGoogleAuthCode('');\n              setError('');\n            },\n            children: \"Back to Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Render main login form\n  return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n    title: \"Welcome Back\",\n    subtitle: \"Sign in to your account\",\n    variant: \"login\",\n    children: [notification && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `user-auth-notification user-auth-notification-${notification.type}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-notification-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"user-auth-notification-icon\",\n          children: notification.type === 'success' ? '✓' : notification.type === 'error' ? '✗' : 'ℹ'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"user-auth-notification-message\",\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"user-auth-notification-close\",\n          onClick: () => setNotification(null),\n          \"aria-label\": \"Close notification\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 17\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-auth-error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 23\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"user-auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"usernameOrEmail\",\n          children: \"Email or Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"usernameOrEmail\",\n            name: \"usernameOrEmail\",\n            value: credentials.usernameOrEmail,\n            onChange: handleInputChange,\n            placeholder: \"Enter your email or username\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: credentials.password,\n            onChange: handleInputChange,\n            placeholder: \"Enter your password\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-lock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-options end\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/forgot-password\",\n          className: \"user-auth-forgot-password\",\n          children: \"Forgot Password?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"user-auth-button\",\n        disabled: isLoading,\n        children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-auth-loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 29\n          }, this), \"Signing in...\"]\n        }, void 0, true) : 'Sign In'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"user-auth-register-link\",\n            children: \"Create one here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 47\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 400,\n    columnNumber: 9\n  }, this);\n}\n_s(UserLogin, \"n9MK96rSttiULwiznbd4jJIxfJQ=\", false, function () {\n  return [useNavigate, useSearchParams];\n});\n_c = UserLogin;\nexport default UserLogin;\nvar _c;\n$RefreshReg$(_c, \"UserLogin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useNavigate", "Link", "useSearchParams", "UserAuthLayout", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserLogin", "_s", "credentials", "setCredentials", "usernameOrEmail", "password", "error", "setError", "isLoading", "setIsLoading", "showOtpVerification", "setShowOtpVerification", "showGoogleAuthVerification", "setShowGoogleAuthVerification", "otpCode", "setOtpCode", "googleAuthCode", "setGoogleAuthCode", "userEmail", "setUserEmail", "userId", "setUserId", "otpExpiry", "setOtpExpiry", "otpCountdown", "setOtpCountdown", "otpResending", "setOtpResending", "notification", "setNotification", "navigate", "searchParams", "localStorage", "getItem", "logoutParam", "get", "message", "type", "newUrl", "window", "location", "pathname", "history", "replaceState", "setTimeout", "timer", "clearTimeout", "handleInputChange", "e", "name", "value", "target", "prev", "sendOtpCode", "email", "response", "post", "data", "success", "Error", "err", "console", "handleSubmit", "preventDefault", "requiresAdditionalAuth", "authType", "setItem", "username", "redirectPath", "sessionStorage", "removeItem", "_err$response", "_err$response$data", "_err$response2", "_err$response3", "status", "handleOtpVerification", "otp_code", "_err$response4", "_err$response4$data", "handleGoogleAuthVerification", "code", "_err$response5", "_err$response5$data", "handleResendOtp", "_err$response6", "_err$response6$data", "formatTime", "seconds", "mins", "Math", "floor", "secs", "title", "subtitle", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "onChange", "placeholder", "required", "autoComplete", "max<PERSON><PERSON><PERSON>", "disabled", "onClick", "variant", "to", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserLogin.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { useNavigate, Link, useSearchParams } from 'react-router-dom';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\n\nfunction UserLogin() {\n    const [credentials, setCredentials] = useState({ usernameOrEmail: '', password: '' });\n    const [error, setError] = useState('');\n    const [isLoading, setIsLoading] = useState(false);\n    const [showOtpVerification, setShowOtpVerification] = useState(false);\n    const [showGoogleAuthVerification, setShowGoogleAuthVerification] = useState(false);\n    const [otpCode, setOtpCode] = useState('');\n    const [googleAuthCode, setGoogleAuthCode] = useState('');\n    const [userEmail, setUserEmail] = useState('');\n    const [userId, setUserId] = useState(null);\n    const [otpExpiry, setOtpExpiry] = useState(null);\n    const [otpCountdown, setOtpCountdown] = useState(0);\n    const [otpResending, setOtpResending] = useState(false);\n    const [notification, setNotification] = useState(null);\n    const navigate = useNavigate();\n    const [searchParams] = useSearchParams();\n\n    // Redirect if already logged in\n    useEffect(() => {\n        const userId = localStorage.getItem('userId');\n        if (userId) {\n            navigate('/user/dashboard');\n        }\n    }, [navigate]);\n\n    // Check for logout success parameter\n    useEffect(() => {\n        const logoutParam = searchParams.get('logout');\n        if (logoutParam === 'success') {\n            setNotification({\n                message: 'You have been successfully logged out',\n                type: 'success'\n            });\n\n            // Clear the URL parameter\n            const newUrl = window.location.pathname;\n            window.history.replaceState({}, '', newUrl);\n\n            // Auto-hide notification after 5 seconds\n            setTimeout(() => {\n                setNotification(null);\n            }, 5000);\n        }\n    }, [searchParams]);\n\n    // OTP countdown timer\n    useEffect(() => {\n        let timer;\n        if (otpCountdown > 0) {\n            timer = setTimeout(() => setOtpCountdown(otpCountdown - 1), 1000);\n        }\n        return () => clearTimeout(timer);\n    }, [otpCountdown]);\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setCredentials(prev => ({ ...prev, [name]: value }));\n        if (error) setError('');\n    };\n\n    const sendOtpCode = async (userId, email) => {\n        try {\n            const response = await axios.post(\n                'user_send_otp.php',\n                {\n                    userId: userId,\n                    email: email\n                }\n            );\n\n            if (!response.data.success) {\n                throw new Error(response.data.message || 'Failed to send OTP');\n            }\n        } catch (err) {\n            console.error('OTP send error:', err);\n            setError('Failed to send OTP. Please try again.');\n            throw err;\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n\n        try {\n            const response = await axios.post(\n                'user_login_enhanced.php',\n                credentials\n            );\n\n            if (response.data.success) {\n                // Check if additional authentication is required\n                if (response.data.requiresAdditionalAuth) {\n                    // Store user data for additional auth steps\n                    setUserEmail(response.data.email);\n                    setUserId(response.data.userId);\n\n                    if (response.data.authType === 'otp' || response.data.authType === 'email_otp') {\n                        // OTP already sent by backend, just show verification form\n                        setShowOtpVerification(true);\n                        setOtpExpiry(300); // 5 minutes\n                        setOtpCountdown(300);\n                    } else if (response.data.authType === '2fa' || response.data.authType === 'google_auth') {\n                        // Show Google Authenticator verification form\n                        setShowGoogleAuthVerification(true);\n                    } else if (response.data.authType === 'otp_2fa') {\n                        // OTP already sent by backend, start with OTP verification\n                        setShowOtpVerification(true);\n                        setOtpExpiry(300); // 5 minutes\n                        setOtpCountdown(300);\n                    }\n                } else {\n                    // No additional auth required, proceed with login\n                    localStorage.setItem('userId', response.data.userId);\n                    localStorage.setItem('username', response.data.username);\n\n                    // Get redirect path or default to dashboard\n                    const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n                    sessionStorage.removeItem('redirectAfterLogin');\n                    navigate(redirectPath);\n                }\n            } else {\n                setError(response.data.message || 'Login failed. Please try again.');\n            }\n        } catch (err) {\n            console.error('Login error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else if (err.response?.status === 401) {\n                setError('Invalid username/email or password');\n            } else if (err.response?.status === 400) {\n                setError('Please enter both username/email and password');\n            } else {\n                setError('An error occurred. Please try again later.');\n            }\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const handleOtpVerification = async (e) => {\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n\n        try {\n            const response = await axios.post(\n                'user_verify_otp.php',\n                {\n                    userId: userId,\n                    otp_code: otpCode\n                }\n            );\n\n            if (response.data.success) {\n                localStorage.setItem('userId', response.data.userId);\n                localStorage.setItem('username', response.data.username);\n\n                // Get redirect path or default to dashboard\n                const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n                sessionStorage.removeItem('redirectAfterLogin');\n                navigate(redirectPath);\n            } else {\n                setError(response.data.message || 'OTP verification failed. Please try again.');\n            }\n        } catch (err) {\n            console.error('OTP verification error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else {\n                setError('An error occurred during OTP verification. Please try again.');\n            }\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const handleGoogleAuthVerification = async (e) => {\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n\n        try {\n            const response = await axios.post(\n                'verify_google_auth.php',\n                {\n                    email: userEmail,\n                    code: googleAuthCode\n                }\n            );\n\n            if (response.data.success) {\n                localStorage.setItem('userId', response.data.userId);\n                localStorage.setItem('username', response.data.username);\n\n                // Get redirect path or default to dashboard\n                const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';\n                sessionStorage.removeItem('redirectAfterLogin');\n                navigate(redirectPath);\n            } else {\n                setError(response.data.message || 'Verification failed. Please try again.');\n            }\n        } catch (err) {\n            console.error('Google Auth verification error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else {\n                setError('An error occurred during verification. Please try again.');\n            }\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const handleResendOtp = async () => {\n        setError('');\n        setOtpResending(true);\n\n        try {\n            const response = await axios.post(\n                'user_send_otp.php',\n                {\n                    userId: userId,\n                    email: userEmail\n                }\n            );\n\n            if (response.data.success) {\n                setOtpExpiry(300); // 5 minutes\n                setOtpCountdown(300);\n                setError(''); // Clear any previous errors\n            } else {\n                setError(response.data.message || 'Failed to resend OTP. Please try again.');\n            }\n        } catch (err) {\n            console.error('Resend OTP error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else {\n                setError('An error occurred while resending OTP. Please try again.');\n            }\n        } finally {\n            setOtpResending(false);\n        }\n    };\n\n    const formatTime = (seconds) => {\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs < 10 ? '0' : ''}${secs}`;\n    };\n\n    // Render OTP verification form\n    if (showOtpVerification) {\n        return (\n            <UserAuthLayout\n                title=\"OTP Verification\"\n                subtitle=\"Enter the one-time password sent to your email\"\n            >\n                {error && <div className=\"user-auth-error-message\">{error}</div>}\n                <form onSubmit={handleOtpVerification} className=\"user-auth-form\">\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"otpCode\">OTP Code</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                type=\"text\"\n                                id=\"otpCode\"\n                                name=\"otpCode\"\n                                value={otpCode}\n                                onChange={(e) => setOtpCode(e.target.value)}\n                                placeholder=\"Enter 6-digit OTP code\"\n                                required\n                                autoComplete=\"one-time-code\"\n                                className=\"user-auth-otp-input\"\n                                maxLength=\"6\"\n                            />\n                            <i className=\"fas fa-key\"></i>\n                        </div>\n                    </div>\n                    {otpCountdown > 0 && (\n                        <div className=\"user-auth-countdown-timer\">\n                            OTP expires in: {formatTime(otpCountdown)}\n                        </div>\n                    )}\n                    <button type=\"submit\" className=\"user-auth-button\" disabled={isLoading}>\n                        {isLoading ? (\n                            <>\n                                <span className=\"user-auth-loading-spinner\"></span>\n                                Verifying...\n                            </>\n                        ) : (\n                            'Verify OTP'\n                        )}\n                    </button>\n                    <div className=\"user-auth-form-options center\">\n                        <button\n                            type=\"button\"\n                            className=\"user-auth-resend-button\"\n                            onClick={handleResendOtp}\n                            disabled={otpResending || otpCountdown > 0}\n                        >\n                            {otpResending ? (\n                                <>\n                                    <span className=\"user-auth-loading-spinner\"></span>\n                                    Resending...\n                                </>\n                            ) : (\n                                'Resend OTP'\n                            )}\n                        </button>\n                    </div>\n                    <div className=\"user-auth-footer\">\n                        <button\n                            type=\"button\"\n                            className=\"user-auth-button-secondary\"\n                            onClick={() => {\n                                setShowOtpVerification(false);\n                                setOtpCode('');\n                                setError('');\n                            }}\n                        >\n                            Back to Login\n                        </button>\n                    </div>\n                </form>\n            </UserAuthLayout>\n        );\n    }\n\n    // Render Google Authenticator verification form\n    if (showGoogleAuthVerification) {\n        return (\n            <UserAuthLayout\n                title=\"Two-Factor Authentication\"\n                subtitle=\"Enter the code from your Google Authenticator app\"\n            >\n                {error && <div className=\"user-auth-error-message\">{error}</div>}\n                <form onSubmit={handleGoogleAuthVerification} className=\"user-auth-form\">\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"googleAuthCode\">Authentication Code</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                type=\"text\"\n                                id=\"googleAuthCode\"\n                                name=\"googleAuthCode\"\n                                value={googleAuthCode}\n                                onChange={(e) => setGoogleAuthCode(e.target.value)}\n                                placeholder=\"Enter 6-digit code\"\n                                required\n                                autoComplete=\"one-time-code\"\n                                className=\"user-auth-otp-input\"\n                                maxLength=\"6\"\n                            />\n                            <i className=\"fas fa-shield-alt\"></i>\n                        </div>\n                    </div>\n                    <div className=\"user-auth-form-group\">\n                        <div className=\"user-auth-countdown-timer\">\n                            Open your Google Authenticator app and enter the 6-digit code for FanBet247\n                        </div>\n                    </div>\n                    <button type=\"submit\" className=\"user-auth-button\" disabled={isLoading}>\n                        {isLoading ? (\n                            <>\n                                <span className=\"user-auth-loading-spinner\"></span>\n                                Verifying...\n                            </>\n                        ) : (\n                            'Verify Code'\n                        )}\n                    </button>\n                    <div className=\"user-auth-footer\">\n                        <button\n                            type=\"button\"\n                            className=\"user-auth-button-secondary\"\n                            onClick={() => {\n                                setShowGoogleAuthVerification(false);\n                                setGoogleAuthCode('');\n                                setError('');\n                            }}\n                        >\n                            Back to Login\n                        </button>\n                    </div>\n                </form>\n            </UserAuthLayout>\n        );\n    }\n\n    // Render main login form\n    return (\n        <UserAuthLayout\n            title=\"Welcome Back\"\n            subtitle=\"Sign in to your account\"\n            variant=\"login\"\n        >\n            {notification && (\n                <div className={`user-auth-notification user-auth-notification-${notification.type}`}>\n                    <div className=\"user-auth-notification-content\">\n                        <span className=\"user-auth-notification-icon\">\n                            {notification.type === 'success' ? '✓' : notification.type === 'error' ? '✗' : 'ℹ'}\n                        </span>\n                        <span className=\"user-auth-notification-message\">{notification.message}</span>\n                        <button\n                            className=\"user-auth-notification-close\"\n                            onClick={() => setNotification(null)}\n                            aria-label=\"Close notification\"\n                        >\n                            ×\n                        </button>\n                    </div>\n                </div>\n            )}\n            {error && <div className=\"user-auth-error-message\">{error}</div>}\n            <form onSubmit={handleSubmit} className=\"user-auth-form\">\n                <div className=\"user-auth-form-group\">\n                    <label htmlFor=\"usernameOrEmail\">Email or Username</label>\n                    <div className=\"user-auth-input-wrapper\">\n                        <input\n                            type=\"text\"\n                            id=\"usernameOrEmail\"\n                            name=\"usernameOrEmail\"\n                            value={credentials.usernameOrEmail}\n                            onChange={handleInputChange}\n                            placeholder=\"Enter your email or username\"\n                            required\n                        />\n                        <i className=\"fas fa-user\"></i>\n                    </div>\n                </div>\n                <div className=\"user-auth-form-group\">\n                    <label htmlFor=\"password\">Password</label>\n                    <div className=\"user-auth-input-wrapper\">\n                        <input\n                            type=\"password\"\n                            id=\"password\"\n                            name=\"password\"\n                            value={credentials.password}\n                            onChange={handleInputChange}\n                            placeholder=\"Enter your password\"\n                            required\n                        />\n                        <i className=\"fas fa-lock\"></i>\n                    </div>\n                </div>\n                <div className=\"user-auth-form-options end\">\n                    <Link to=\"/forgot-password\" className=\"user-auth-forgot-password\">\n                        Forgot Password?\n                    </Link>\n                </div>\n                <button type=\"submit\" className=\"user-auth-button\" disabled={isLoading}>\n                    {isLoading ? (\n                        <>\n                            <span className=\"user-auth-loading-spinner\"></span>\n                            Signing in...\n                        </>\n                    ) : (\n                        'Sign In'\n                    )}\n                </button>\n                <div className=\"user-auth-footer\">\n                    <p>Don't have an account? <Link to=\"/register\" className=\"user-auth-register-link\">Create one here</Link></p>\n                </div>\n            </form>\n        </UserAuthLayout>\n    );\n}\n\nexport default UserLogin;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,WAAW,EAAEC,IAAI,EAAEC,eAAe,QAAQ,kBAAkB;AACrE,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAO,wBAAwB;AAC/B,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC;IAAEe,eAAe,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EACrF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+B,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMyC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACuC,YAAY,CAAC,GAAGrC,eAAe,CAAC,CAAC;;EAExC;EACAJ,SAAS,CAAC,MAAM;IACZ,MAAM8B,MAAM,GAAGY,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,IAAIb,MAAM,EAAE;MACRU,QAAQ,CAAC,iBAAiB,CAAC;IAC/B;EACJ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACAxC,SAAS,CAAC,MAAM;IACZ,MAAM4C,WAAW,GAAGH,YAAY,CAACI,GAAG,CAAC,QAAQ,CAAC;IAC9C,IAAID,WAAW,KAAK,SAAS,EAAE;MAC3BL,eAAe,CAAC;QACZO,OAAO,EAAE,uCAAuC;QAChDC,IAAI,EAAE;MACV,CAAC,CAAC;;MAEF;MACA,MAAMC,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;MACvCF,MAAM,CAACG,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEL,MAAM,CAAC;;MAE3C;MACAM,UAAU,CAAC,MAAM;QACbf,eAAe,CAAC,IAAI,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;IACZ;EACJ,CAAC,EAAE,CAACE,YAAY,CAAC,CAAC;;EAElB;EACAzC,SAAS,CAAC,MAAM;IACZ,IAAIuD,KAAK;IACT,IAAIrB,YAAY,GAAG,CAAC,EAAE;MAClBqB,KAAK,GAAGD,UAAU,CAAC,MAAMnB,eAAe,CAACD,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;IACrE;IACA,OAAO,MAAMsB,YAAY,CAACD,KAAK,CAAC;EACpC,CAAC,EAAE,CAACrB,YAAY,CAAC,CAAC;EAElB,MAAMuB,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChChD,cAAc,CAACiD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;IACpD,IAAI5C,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EAC3B,CAAC;EAED,MAAM8C,WAAW,GAAG,MAAAA,CAAOjC,MAAM,EAAEkC,KAAK,KAAK;IACzC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,IAAI,CAC7B,mBAAmB,EACnB;QACIpC,MAAM,EAAEA,MAAM;QACdkC,KAAK,EAAEA;MACX,CACJ,CAAC;MAED,IAAI,CAACC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACxB,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACrB,OAAO,IAAI,oBAAoB,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOwB,GAAG,EAAE;MACVC,OAAO,CAACvD,KAAK,CAAC,iBAAiB,EAAEsD,GAAG,CAAC;MACrCrD,QAAQ,CAAC,uCAAuC,CAAC;MACjD,MAAMqD,GAAG;IACb;EACJ,CAAC;EAED,MAAME,YAAY,GAAG,MAAOd,CAAC,IAAK;IAC9BA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBxD,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAM8C,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,IAAI,CAC7B,yBAAyB,EACzBtD,WACJ,CAAC;MAED,IAAIqD,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB;QACA,IAAIH,QAAQ,CAACE,IAAI,CAACO,sBAAsB,EAAE;UACtC;UACA7C,YAAY,CAACoC,QAAQ,CAACE,IAAI,CAACH,KAAK,CAAC;UACjCjC,SAAS,CAACkC,QAAQ,CAACE,IAAI,CAACrC,MAAM,CAAC;UAE/B,IAAImC,QAAQ,CAACE,IAAI,CAACQ,QAAQ,KAAK,KAAK,IAAIV,QAAQ,CAACE,IAAI,CAACQ,QAAQ,KAAK,WAAW,EAAE;YAC5E;YACAtD,sBAAsB,CAAC,IAAI,CAAC;YAC5BY,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YACnBE,eAAe,CAAC,GAAG,CAAC;UACxB,CAAC,MAAM,IAAI8B,QAAQ,CAACE,IAAI,CAACQ,QAAQ,KAAK,KAAK,IAAIV,QAAQ,CAACE,IAAI,CAACQ,QAAQ,KAAK,aAAa,EAAE;YACrF;YACApD,6BAA6B,CAAC,IAAI,CAAC;UACvC,CAAC,MAAM,IAAI0C,QAAQ,CAACE,IAAI,CAACQ,QAAQ,KAAK,SAAS,EAAE;YAC7C;YACAtD,sBAAsB,CAAC,IAAI,CAAC;YAC5BY,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YACnBE,eAAe,CAAC,GAAG,CAAC;UACxB;QACJ,CAAC,MAAM;UACH;UACAO,YAAY,CAACkC,OAAO,CAAC,QAAQ,EAAEX,QAAQ,CAACE,IAAI,CAACrC,MAAM,CAAC;UACpDY,YAAY,CAACkC,OAAO,CAAC,UAAU,EAAEX,QAAQ,CAACE,IAAI,CAACU,QAAQ,CAAC;;UAExD;UACA,MAAMC,YAAY,GAAGC,cAAc,CAACpC,OAAO,CAAC,oBAAoB,CAAC,IAAI,iBAAiB;UACtFoC,cAAc,CAACC,UAAU,CAAC,oBAAoB,CAAC;UAC/CxC,QAAQ,CAACsC,YAAY,CAAC;QAC1B;MACJ,CAAC,MAAM;QACH7D,QAAQ,CAACgD,QAAQ,CAACE,IAAI,CAACrB,OAAO,IAAI,iCAAiC,CAAC;MACxE;IACJ,CAAC,CAAC,OAAOwB,GAAG,EAAE;MAAA,IAAAW,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,cAAA;MACVb,OAAO,CAACvD,KAAK,CAAC,cAAc,EAAEsD,GAAG,CAAC;MAClC,KAAAW,aAAA,GAAIX,GAAG,CAACL,QAAQ,cAAAgB,aAAA,gBAAAC,kBAAA,GAAZD,aAAA,CAAcd,IAAI,cAAAe,kBAAA,eAAlBA,kBAAA,CAAoBpC,OAAO,EAAE;QAC7B7B,QAAQ,CAACqD,GAAG,CAACL,QAAQ,CAACE,IAAI,CAACrB,OAAO,CAAC;MACvC,CAAC,MAAM,IAAI,EAAAqC,cAAA,GAAAb,GAAG,CAACL,QAAQ,cAAAkB,cAAA,uBAAZA,cAAA,CAAcE,MAAM,MAAK,GAAG,EAAE;QACrCpE,QAAQ,CAAC,oCAAoC,CAAC;MAClD,CAAC,MAAM,IAAI,EAAAmE,cAAA,GAAAd,GAAG,CAACL,QAAQ,cAAAmB,cAAA,uBAAZA,cAAA,CAAcC,MAAM,MAAK,GAAG,EAAE;QACrCpE,QAAQ,CAAC,+CAA+C,CAAC;MAC7D,CAAC,MAAM;QACHA,QAAQ,CAAC,4CAA4C,CAAC;MAC1D;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMmE,qBAAqB,GAAG,MAAO5B,CAAC,IAAK;IACvCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBxD,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAM8C,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,IAAI,CAC7B,qBAAqB,EACrB;QACIpC,MAAM,EAAEA,MAAM;QACdyD,QAAQ,EAAE/D;MACd,CACJ,CAAC;MAED,IAAIyC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB1B,YAAY,CAACkC,OAAO,CAAC,QAAQ,EAAEX,QAAQ,CAACE,IAAI,CAACrC,MAAM,CAAC;QACpDY,YAAY,CAACkC,OAAO,CAAC,UAAU,EAAEX,QAAQ,CAACE,IAAI,CAACU,QAAQ,CAAC;;QAExD;QACA,MAAMC,YAAY,GAAGC,cAAc,CAACpC,OAAO,CAAC,oBAAoB,CAAC,IAAI,iBAAiB;QACtFoC,cAAc,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC/CxC,QAAQ,CAACsC,YAAY,CAAC;MAC1B,CAAC,MAAM;QACH7D,QAAQ,CAACgD,QAAQ,CAACE,IAAI,CAACrB,OAAO,IAAI,4CAA4C,CAAC;MACnF;IACJ,CAAC,CAAC,OAAOwB,GAAG,EAAE;MAAA,IAAAkB,cAAA,EAAAC,mBAAA;MACVlB,OAAO,CAACvD,KAAK,CAAC,yBAAyB,EAAEsD,GAAG,CAAC;MAC7C,KAAAkB,cAAA,GAAIlB,GAAG,CAACL,QAAQ,cAAAuB,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcrB,IAAI,cAAAsB,mBAAA,eAAlBA,mBAAA,CAAoB3C,OAAO,EAAE;QAC7B7B,QAAQ,CAACqD,GAAG,CAACL,QAAQ,CAACE,IAAI,CAACrB,OAAO,CAAC;MACvC,CAAC,MAAM;QACH7B,QAAQ,CAAC,8DAA8D,CAAC;MAC5E;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMuE,4BAA4B,GAAG,MAAOhC,CAAC,IAAK;IAC9CA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBxD,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACA,MAAM8C,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,IAAI,CAC7B,wBAAwB,EACxB;QACIF,KAAK,EAAEpC,SAAS;QAChB+D,IAAI,EAAEjE;MACV,CACJ,CAAC;MAED,IAAIuC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB1B,YAAY,CAACkC,OAAO,CAAC,QAAQ,EAAEX,QAAQ,CAACE,IAAI,CAACrC,MAAM,CAAC;QACpDY,YAAY,CAACkC,OAAO,CAAC,UAAU,EAAEX,QAAQ,CAACE,IAAI,CAACU,QAAQ,CAAC;;QAExD;QACA,MAAMC,YAAY,GAAGC,cAAc,CAACpC,OAAO,CAAC,oBAAoB,CAAC,IAAI,iBAAiB;QACtFoC,cAAc,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC/CxC,QAAQ,CAACsC,YAAY,CAAC;MAC1B,CAAC,MAAM;QACH7D,QAAQ,CAACgD,QAAQ,CAACE,IAAI,CAACrB,OAAO,IAAI,wCAAwC,CAAC;MAC/E;IACJ,CAAC,CAAC,OAAOwB,GAAG,EAAE;MAAA,IAAAsB,cAAA,EAAAC,mBAAA;MACVtB,OAAO,CAACvD,KAAK,CAAC,iCAAiC,EAAEsD,GAAG,CAAC;MACrD,KAAAsB,cAAA,GAAItB,GAAG,CAACL,QAAQ,cAAA2B,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAczB,IAAI,cAAA0B,mBAAA,eAAlBA,mBAAA,CAAoB/C,OAAO,EAAE;QAC7B7B,QAAQ,CAACqD,GAAG,CAACL,QAAQ,CAACE,IAAI,CAACrB,OAAO,CAAC;MACvC,CAAC,MAAM;QACH7B,QAAQ,CAAC,0DAA0D,CAAC;MACxE;IACJ,CAAC,SAAS;MACNE,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAM2E,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC7E,QAAQ,CAAC,EAAE,CAAC;IACZoB,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACA,MAAM4B,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,IAAI,CAC7B,mBAAmB,EACnB;QACIpC,MAAM,EAAEA,MAAM;QACdkC,KAAK,EAAEpC;MACX,CACJ,CAAC;MAED,IAAIqC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBnC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;QACnBE,eAAe,CAAC,GAAG,CAAC;QACpBlB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,MAAM;QACHA,QAAQ,CAACgD,QAAQ,CAACE,IAAI,CAACrB,OAAO,IAAI,yCAAyC,CAAC;MAChF;IACJ,CAAC,CAAC,OAAOwB,GAAG,EAAE;MAAA,IAAAyB,cAAA,EAAAC,mBAAA;MACVzB,OAAO,CAACvD,KAAK,CAAC,mBAAmB,EAAEsD,GAAG,CAAC;MACvC,KAAAyB,cAAA,GAAIzB,GAAG,CAACL,QAAQ,cAAA8B,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAc5B,IAAI,cAAA6B,mBAAA,eAAlBA,mBAAA,CAAoBlD,OAAO,EAAE;QAC7B7B,QAAQ,CAACqD,GAAG,CAACL,QAAQ,CAACE,IAAI,CAACrB,OAAO,CAAC;MACvC,CAAC,MAAM;QACH7B,QAAQ,CAAC,0DAA0D,CAAC;MACxE;IACJ,CAAC,SAAS;MACNoB,eAAe,CAAC,KAAK,CAAC;IAC1B;EACJ,CAAC;EAED,MAAM4D,UAAU,GAAIC,OAAO,IAAK;IAC5B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIG,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAGA,IAAI,EAAE;EACnD,CAAC;;EAED;EACA,IAAIlF,mBAAmB,EAAE;IACrB,oBACIb,OAAA,CAACF,cAAc;MACXkG,KAAK,EAAC,kBAAkB;MACxBC,QAAQ,EAAC,gDAAgD;MAAAC,QAAA,GAExDzF,KAAK,iBAAIT,OAAA;QAAKmG,SAAS,EAAC,yBAAyB;QAAAD,QAAA,EAAEzF;MAAK;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChEvG,OAAA;QAAMwG,QAAQ,EAAEzB,qBAAsB;QAACoB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7DlG,OAAA;UAAKmG,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjClG,OAAA;YAAOyG,OAAO,EAAC,SAAS;YAAAP,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzCvG,OAAA;YAAKmG,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBACpClG,OAAA;cACIwC,IAAI,EAAC,MAAM;cACXkE,EAAE,EAAC,SAAS;cACZtD,IAAI,EAAC,SAAS;cACdC,KAAK,EAAEpC,OAAQ;cACf0F,QAAQ,EAAGxD,CAAC,IAAKjC,UAAU,CAACiC,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAC5CuD,WAAW,EAAC,wBAAwB;cACpCC,QAAQ;cACRC,YAAY,EAAC,eAAe;cAC5BX,SAAS,EAAC,qBAAqB;cAC/BY,SAAS,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACFvG,OAAA;cAAGmG,SAAS,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACL5E,YAAY,GAAG,CAAC,iBACb3B,OAAA;UAAKmG,SAAS,EAAC,2BAA2B;UAAAD,QAAA,GAAC,kBACvB,EAACR,UAAU,CAAC/D,YAAY,CAAC;QAAA;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACR,eACDvG,OAAA;UAAQwC,IAAI,EAAC,QAAQ;UAAC2D,SAAS,EAAC,kBAAkB;UAACa,QAAQ,EAAErG,SAAU;UAAAuF,QAAA,EAClEvF,SAAS,gBACNX,OAAA,CAAAE,SAAA;YAAAgG,QAAA,gBACIlG,OAAA;cAAMmG,SAAS,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,gBAEvD;UAAA,eAAE,CAAC,GAEH;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACTvG,OAAA;UAAKmG,SAAS,EAAC,+BAA+B;UAAAD,QAAA,eAC1ClG,OAAA;YACIwC,IAAI,EAAC,QAAQ;YACb2D,SAAS,EAAC,yBAAyB;YACnCc,OAAO,EAAE1B,eAAgB;YACzByB,QAAQ,EAAEnF,YAAY,IAAIF,YAAY,GAAG,CAAE;YAAAuE,QAAA,EAE1CrE,YAAY,gBACT7B,OAAA,CAAAE,SAAA;cAAAgG,QAAA,gBACIlG,OAAA;gBAAMmG,SAAS,EAAC;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,gBAEvD;YAAA,eAAE,CAAC,GAEH;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACNvG,OAAA;UAAKmG,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC7BlG,OAAA;YACIwC,IAAI,EAAC,QAAQ;YACb2D,SAAS,EAAC,4BAA4B;YACtCc,OAAO,EAAEA,CAAA,KAAM;cACXnG,sBAAsB,CAAC,KAAK,CAAC;cAC7BI,UAAU,CAAC,EAAE,CAAC;cACdR,QAAQ,CAAC,EAAE,CAAC;YAChB,CAAE;YAAAwF,QAAA,EACL;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAEzB;;EAEA;EACA,IAAIxF,0BAA0B,EAAE;IAC5B,oBACIf,OAAA,CAACF,cAAc;MACXkG,KAAK,EAAC,2BAA2B;MACjCC,QAAQ,EAAC,mDAAmD;MAAAC,QAAA,GAE3DzF,KAAK,iBAAIT,OAAA;QAAKmG,SAAS,EAAC,yBAAyB;QAAAD,QAAA,EAAEzF;MAAK;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChEvG,OAAA;QAAMwG,QAAQ,EAAErB,4BAA6B;QAACgB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBACpElG,OAAA;UAAKmG,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjClG,OAAA;YAAOyG,OAAO,EAAC,gBAAgB;YAAAP,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3DvG,OAAA;YAAKmG,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBACpClG,OAAA;cACIwC,IAAI,EAAC,MAAM;cACXkE,EAAE,EAAC,gBAAgB;cACnBtD,IAAI,EAAC,gBAAgB;cACrBC,KAAK,EAAElC,cAAe;cACtBwF,QAAQ,EAAGxD,CAAC,IAAK/B,iBAAiB,CAAC+B,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cACnDuD,WAAW,EAAC,oBAAoB;cAChCC,QAAQ;cACRC,YAAY,EAAC,eAAe;cAC5BX,SAAS,EAAC,qBAAqB;cAC/BY,SAAS,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACFvG,OAAA;cAAGmG,SAAS,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNvG,OAAA;UAAKmG,SAAS,EAAC,sBAAsB;UAAAD,QAAA,eACjClG,OAAA;YAAKmG,SAAS,EAAC,2BAA2B;YAAAD,QAAA,EAAC;UAE3C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNvG,OAAA;UAAQwC,IAAI,EAAC,QAAQ;UAAC2D,SAAS,EAAC,kBAAkB;UAACa,QAAQ,EAAErG,SAAU;UAAAuF,QAAA,EAClEvF,SAAS,gBACNX,OAAA,CAAAE,SAAA;YAAAgG,QAAA,gBACIlG,OAAA;cAAMmG,SAAS,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,gBAEvD;UAAA,eAAE,CAAC,GAEH;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACTvG,OAAA;UAAKmG,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC7BlG,OAAA;YACIwC,IAAI,EAAC,QAAQ;YACb2D,SAAS,EAAC,4BAA4B;YACtCc,OAAO,EAAEA,CAAA,KAAM;cACXjG,6BAA6B,CAAC,KAAK,CAAC;cACpCI,iBAAiB,CAAC,EAAE,CAAC;cACrBV,QAAQ,CAAC,EAAE,CAAC;YAChB,CAAE;YAAAwF,QAAA,EACL;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAEzB;;EAEA;EACA,oBACIvG,OAAA,CAACF,cAAc;IACXkG,KAAK,EAAC,cAAc;IACpBC,QAAQ,EAAC,yBAAyB;IAClCiB,OAAO,EAAC,OAAO;IAAAhB,QAAA,GAEdnE,YAAY,iBACT/B,OAAA;MAAKmG,SAAS,EAAE,iDAAiDpE,YAAY,CAACS,IAAI,EAAG;MAAA0D,QAAA,eACjFlG,OAAA;QAAKmG,SAAS,EAAC,gCAAgC;QAAAD,QAAA,gBAC3ClG,OAAA;UAAMmG,SAAS,EAAC,6BAA6B;UAAAD,QAAA,EACxCnE,YAAY,CAACS,IAAI,KAAK,SAAS,GAAG,GAAG,GAAGT,YAAY,CAACS,IAAI,KAAK,OAAO,GAAG,GAAG,GAAG;QAAG;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eACPvG,OAAA;UAAMmG,SAAS,EAAC,gCAAgC;UAAAD,QAAA,EAAEnE,YAAY,CAACQ;QAAO;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9EvG,OAAA;UACImG,SAAS,EAAC,8BAA8B;UACxCc,OAAO,EAAEA,CAAA,KAAMjF,eAAe,CAAC,IAAI,CAAE;UACrC,cAAW,oBAAoB;UAAAkE,QAAA,EAClC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EACA9F,KAAK,iBAAIT,OAAA;MAAKmG,SAAS,EAAC,yBAAyB;MAAAD,QAAA,EAAEzF;IAAK;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAChEvG,OAAA;MAAMwG,QAAQ,EAAEvC,YAAa;MAACkC,SAAS,EAAC,gBAAgB;MAAAD,QAAA,gBACpDlG,OAAA;QAAKmG,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBACjClG,OAAA;UAAOyG,OAAO,EAAC,iBAAiB;UAAAP,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1DvG,OAAA;UAAKmG,SAAS,EAAC,yBAAyB;UAAAD,QAAA,gBACpClG,OAAA;YACIwC,IAAI,EAAC,MAAM;YACXkE,EAAE,EAAC,iBAAiB;YACpBtD,IAAI,EAAC,iBAAiB;YACtBC,KAAK,EAAEhD,WAAW,CAACE,eAAgB;YACnCoG,QAAQ,EAAEzD,iBAAkB;YAC5B0D,WAAW,EAAC,8BAA8B;YAC1CC,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACFvG,OAAA;YAAGmG,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNvG,OAAA;QAAKmG,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBACjClG,OAAA;UAAOyG,OAAO,EAAC,UAAU;UAAAP,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1CvG,OAAA;UAAKmG,SAAS,EAAC,yBAAyB;UAAAD,QAAA,gBACpClG,OAAA;YACIwC,IAAI,EAAC,UAAU;YACfkE,EAAE,EAAC,UAAU;YACbtD,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEhD,WAAW,CAACG,QAAS;YAC5BmG,QAAQ,EAAEzD,iBAAkB;YAC5B0D,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACFvG,OAAA;YAAGmG,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNvG,OAAA;QAAKmG,SAAS,EAAC,4BAA4B;QAAAD,QAAA,eACvClG,OAAA,CAACJ,IAAI;UAACuH,EAAE,EAAC,kBAAkB;UAAChB,SAAS,EAAC,2BAA2B;UAAAD,QAAA,EAAC;QAElE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNvG,OAAA;QAAQwC,IAAI,EAAC,QAAQ;QAAC2D,SAAS,EAAC,kBAAkB;QAACa,QAAQ,EAAErG,SAAU;QAAAuF,QAAA,EAClEvF,SAAS,gBACNX,OAAA,CAAAE,SAAA;UAAAgG,QAAA,gBACIlG,OAAA;YAAMmG,SAAS,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,iBAEvD;QAAA,eAAE,CAAC,GAEH;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACTvG,OAAA;QAAKmG,SAAS,EAAC,kBAAkB;QAAAD,QAAA,eAC7BlG,OAAA;UAAAkG,QAAA,GAAG,yBAAuB,eAAAlG,OAAA,CAACJ,IAAI;YAACuH,EAAE,EAAC,WAAW;YAAChB,SAAS,EAAC,yBAAyB;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEzB;AAACnG,EAAA,CAndQD,SAAS;EAAA,QAcGR,WAAW,EACLE,eAAe;AAAA;AAAAuH,EAAA,GAfjCjH,SAAS;AAqdlB,eAAeA,SAAS;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}