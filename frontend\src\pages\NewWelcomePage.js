import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from '../utils/axiosConfig';
import { API_BASE_URL } from '../config';
import './NewWelcomePage.css';

// Completely independent welcome page - NO MainLayout or any existing components

const NewWelcomePage = () => {
  const navigate = useNavigate();
  const [data, setData] = useState({
    leagues: [],
    challenges: [],
    recentBets: []
  });
  const [loading, setLoading] = useState({
    leagues: true,
    challenges: true,
    bets: true
  });
  const [error, setError] = useState(null);
  const [currentSlide, setCurrentSlide] = useState(0);

  // Hero slider images
  const sliderImages = [
    process.env.PUBLIC_URL + '/slider/Slider1.png',
    process.env.PUBLIC_URL + '/slider/Slider2.png'
  ];

  // Fetch top 7 leagues
  const fetchLeagues = async () => {
    try {
      const response = await axios.get('/handlers/league_management.php');
      if (response.data.status === 200 && response.data.data) {
        // Get top 7 leagues by member count
        const topLeagues = response.data.data
          .sort((a, b) => (b.member_count || 0) - (a.member_count || 0))
          .slice(0, 7);
        setData(prev => ({ ...prev, leagues: topLeagues }));
      } else {
        // Fallback data if API fails
        setData(prev => ({ ...prev, leagues: getFallbackLeagues() }));
      }
    } catch (err) {
      console.error('Error fetching leagues:', err);
      // Set fallback data on error
      setData(prev => ({ ...prev, leagues: getFallbackLeagues() }));
    } finally {
      setLoading(prev => ({ ...prev, leagues: false }));
    }
  };

  // Fetch live challenges
  const fetchChallenges = async () => {
    try {
      const response = await axios.get('/handlers/recent_challenges.php');
      if (response.data.success && response.data.challenges) {
        setData(prev => ({ ...prev, challenges: response.data.challenges.slice(0, 6) }));
      } else {
        // Fallback data if API fails
        setData(prev => ({ ...prev, challenges: getFallbackChallenges() }));
      }
    } catch (err) {
      console.error('Error fetching challenges:', err);
      // Set fallback data on error
      setData(prev => ({ ...prev, challenges: getFallbackChallenges() }));
    } finally {
      setLoading(prev => ({ ...prev, challenges: false }));
    }
  };

  // Fetch recent bets
  const fetchRecentBets = async () => {
    try {
      const response = await axios.get('/handlers/welcome_recent_bets.php');
      if (response.data.success && response.data.bets) {
        setData(prev => ({ ...prev, recentBets: response.data.bets.slice(0, 6) }));
      } else {
        // Fallback data if API fails
        setData(prev => ({ ...prev, recentBets: getFallbackBets() }));
      }
    } catch (err) {
      console.error('Error fetching recent bets:', err);
      // Set fallback data on error
      setData(prev => ({ ...prev, recentBets: getFallbackBets() }));
    } finally {
      setLoading(prev => ({ ...prev, bets: false }));
    }
  };

  useEffect(() => {
    fetchLeagues();
    fetchChallenges();
    fetchRecentBets();
  }, []);

  // Auto-advance slider
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % sliderImages.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [sliderImages.length]);

  const handleChallengeClick = (challengeId) => {
    sessionStorage.setItem('redirectAfterLogin', `/user/join-challenge/${challengeId}`);
    navigate('/login');
  };

  const getTeamLogo = (teamName, logoPath = null) => {
    if (logoPath) {
      return `${API_BASE_URL}/uploads/teams/${logoPath}`;
    }
    if (teamName) {
      return `${API_BASE_URL}/uploads/teams/${teamName.toLowerCase().replace(/\s+/g, '_')}.png`;
    }
    return '/default-team.png';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Fallback data functions
  const getFallbackLeagues = () => [
    { league_id: 1, name: 'Premier League', member_count: 1250, min_bet_amount: 100, max_bet_amount: 10000, icon_path: null },
    { league_id: 2, name: 'Champions League', member_count: 980, min_bet_amount: 500, max_bet_amount: 50000, icon_path: null },
    { league_id: 3, name: 'La Liga', member_count: 875, min_bet_amount: 200, max_bet_amount: 15000, icon_path: null },
    { league_id: 4, name: 'Serie A', member_count: 720, min_bet_amount: 150, max_bet_amount: 12000, icon_path: null },
    { league_id: 5, name: 'Bundesliga', member_count: 650, min_bet_amount: 100, max_bet_amount: 8000, icon_path: null },
    { league_id: 6, name: 'Ligue 1', member_count: 580, min_bet_amount: 100, max_bet_amount: 7000, icon_path: null },
    { league_id: 7, name: 'Europa League', member_count: 520, min_bet_amount: 200, max_bet_amount: 20000, icon_path: null }
  ];

  const getFallbackChallenges = () => [
    {
      challenge_id: 1,
      team_a: 'Chelsea Fc',
      team_b: 'Manchester United',
      odds_team_a: '1.80',
      odds_draw: '3.20',
      odds_team_b: '2.10',
      status: 'Open'
    },
    {
      challenge_id: 2,
      team_a: 'Liverpool Fc',
      team_b: 'Arsenal',
      odds_team_a: '2.00',
      odds_draw: '3.40',
      odds_team_b: '1.90',
      status: 'Open'
    },
    {
      challenge_id: 3,
      team_a: 'Manchester City',
      team_b: 'Tottenham',
      odds_team_a: '1.60',
      odds_draw: '3.80',
      odds_team_b: '2.40',
      status: 'Open'
    }
  ];

  const getFallbackBets = () => [
    {
      bet_id: 'demo001',
      user1_name: 'DemoUser1',
      user2_name: 'DemoUser2',
      bet_amount: 1000,
      bet_status: 'completed'
    },
    {
      bet_id: 'demo002',
      user1_name: 'TestPlayer',
      user2_name: null,
      bet_amount: 500,
      bet_status: 'open'
    },
    {
      bet_id: 'demo003',
      user1_name: 'BetMaster',
      user2_name: 'ProGamer',
      bet_amount: 2000,
      bet_status: 'completed'
    }
  ];

  return (
    <div className="fresh-welcome-page">
      {/* Header */}
      <header className="fresh-header">
        <div className="fresh-header-container">
          <Link to="/" className="fresh-logo">
            FanBet247
          </Link>
          <nav className="fresh-nav">
            <Link to="/" className="fresh-nav-link active">Home</Link>
            <Link to="/live-challenges" className="fresh-nav-link">Live</Link>
            <Link to="/login" className="fresh-nav-link">Leagues</Link>
            <Link to="/leaderboard" className="fresh-nav-link">Leaders</Link>
            <Link to="/about" className="fresh-nav-link">About</Link>
          </nav>
          <div className="fresh-auth-buttons">
            <Link to="/login" className="fresh-btn fresh-btn-login">Login</Link>
            <Link to="/register" className="fresh-btn fresh-btn-register">Register</Link>
          </div>
        </div>
      </header>

      <div className="fresh-layout">
        {/* Sidebar with Top 7 Leagues */}
        <aside className="fresh-sidebar">
          <div className="fresh-sidebar-header">
            <h3>Top Leagues</h3>
          </div>
          <div className="fresh-leagues-list">
            {loading.leagues ? (
              <div className="fresh-loading">Loading leagues...</div>
            ) : (
              data.leagues.map((league, index) => (
                <div key={league.league_id} className="fresh-league-item">
                  <div className="fresh-league-rank">#{index + 1}</div>
                  <div className="fresh-league-icon">
                    {league.icon_path ? (
                      <img 
                        src={`${API_BASE_URL}/uploads/leagues/icons/${league.icon_path}`}
                        alt={league.name}
                        onError={(e) => e.target.src = '/default-league.png'}
                      />
                    ) : (
                      <div className="fresh-league-placeholder">🏆</div>
                    )}
                  </div>
                  <div className="fresh-league-info">
                    <h4>{league.name}</h4>
                    <p>{league.member_count || 0} members</p>
                    <span className="fresh-league-range">
                      {formatCurrency(league.min_bet_amount)} - {formatCurrency(league.max_bet_amount)} FC
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
          <Link to="/login" className="fresh-sidebar-cta">
            Login to View All
          </Link>
        </aside>

        {/* Main Content */}
        <main className="fresh-main-content">
          {/* Hero Slider */}
          <section className="fresh-hero-section">
            <div className="fresh-hero-slider">
              {sliderImages.map((image, index) => (
                <div 
                  key={index}
                  className={`fresh-slide ${index === currentSlide ? 'active' : ''}`}
                  style={{ backgroundImage: `url(${image})` }}
                />
              ))}
              <div className="fresh-slider-controls">
                <button 
                  className="fresh-slider-btn prev"
                  onClick={() => setCurrentSlide(prev => prev === 0 ? sliderImages.length - 1 : prev - 1)}
                >
                  ❮
                </button>
                <button 
                  className="fresh-slider-btn next"
                  onClick={() => setCurrentSlide(prev => (prev + 1) % sliderImages.length)}
                >
                  ❯
                </button>
              </div>
              <div className="fresh-slider-indicators">
                {sliderImages.map((_, index) => (
                  <button
                    key={index}
                    className={`fresh-indicator ${index === currentSlide ? 'active' : ''}`}
                    onClick={() => setCurrentSlide(index)}
                  />
                ))}
              </div>
            </div>
          </section>

          {/* Live Challenges Section */}
          <section className="fresh-challenges-section">
            <div className="fresh-section-header">
              <h2>LIVE CHALLENGES</h2>
              <Link to="/login" className="fresh-section-link">Login to Bet</Link>
            </div>
            <div className="fresh-challenges-container">
              {loading.challenges ? (
                <div className="fresh-loading">Loading challenges...</div>
              ) : data.challenges.length === 0 ? (
                <div className="fresh-empty-state">
                  <p>No active challenges at the moment</p>
                </div>
              ) : (
                <div className="fresh-challenges-grid">
                  {data.challenges.map(challenge => (
                    <div key={challenge.challenge_id} className="fresh-challenge-card">
                      <div className="fresh-match-info">
                        <div className="fresh-team">
                          <img
                            src={getTeamLogo(challenge.team_a, challenge.team_a_logo)}
                            alt={challenge.team_a}
                            onError={(e) => e.target.src = '/default-team.png'}
                          />
                          <span>{challenge.team_a}</span>
                        </div>
                        <div className="fresh-vs">VS</div>
                        <div className="fresh-team">
                          <img
                            src={getTeamLogo(challenge.team_b, challenge.team_b_logo)}
                            alt={challenge.team_b}
                            onError={(e) => e.target.src = '/default-team.png'}
                          />
                          <span>{challenge.team_b}</span>
                        </div>
                      </div>
                      <div className="fresh-odds">
                        <div className="fresh-odd">
                          <span>Home</span>
                          <span>{challenge.odds_team_a}</span>
                        </div>
                        <div className="fresh-odd">
                          <span>Draw</span>
                          <span>{challenge.odds_draw}</span>
                        </div>
                        <div className="fresh-odd">
                          <span>Away</span>
                          <span>{challenge.odds_team_b}</span>
                        </div>
                      </div>
                      <button 
                        className="fresh-bet-btn"
                        onClick={() => handleChallengeClick(challenge.challenge_id)}
                      >
                        LOGIN TO BET
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </section>

          {/* Recent Bets Section */}
          <section className="fresh-recent-bets-section">
            <div className="fresh-section-header">
              <h2>RECENT BETS</h2>
              <Link to="/user/recent-bets" className="fresh-section-link">View All</Link>
            </div>
            <div className="fresh-bets-container">
              {loading.bets ? (
                <div className="fresh-loading">Loading recent bets...</div>
              ) : data.recentBets.length === 0 ? (
                <div className="fresh-empty-state">
                  <p>No recent bets to display</p>
                </div>
              ) : (
                <div className="fresh-bets-grid">
                  {data.recentBets.map(bet => (
                    <div key={bet.bet_id} className="fresh-bet-card">
                      <div className="fresh-bet-ref">REF: {bet.unique_code || bet.bet_id}</div>
                      <div className="fresh-bet-match">
                        {bet.team_a && bet.team_b && (
                          <div className="fresh-bet-teams">
                            <span>{bet.team_a} vs {bet.team_b}</span>
                          </div>
                        )}
                      </div>
                      <div className="fresh-bet-details">
                        <div className="fresh-bet-user">
                          <span>{bet.user1_name}</span>
                          <span>{formatCurrency(bet.amount_user1 || bet.amount)} FC</span>
                          <small>{bet.bet_choice_user1 || 'N/A'}</small>
                        </div>
                        <div className="fresh-bet-vs">VS</div>
                        <div className="fresh-bet-user">
                          <span>{bet.user2_name || 'Open'}</span>
                          <span>{bet.user2_name ? formatCurrency(bet.amount_user2 || bet.amount) : 'Waiting'} FC</span>
                          <small>{bet.bet_choice_user2 || 'N/A'}</small>
                        </div>
                      </div>
                      <div className="fresh-bet-status">
                        <span className={`fresh-status ${bet.bet_status}`}>
                          {bet.bet_status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </section>
        </main>
      </div>

      {/* Footer */}
      <footer className="fresh-footer">
        <div className="fresh-footer-container">
          <div className="fresh-footer-content">
            <div className="fresh-footer-brand">
              <h3>FanBet247</h3>
              <p>The ultimate sports betting platform for fans worldwide.</p>
              <p><strong>18+ Bet Responsibly</strong></p>
            </div>
            <div className="fresh-footer-links">
              <div className="fresh-footer-column">
                <h4>Quick Links</h4>
                <Link to="/live-challenges">Live Matches</Link>
                <Link to="/upcoming-matches">Upcoming</Link>
                <Link to="/leaderboard">Leaderboard</Link>
                <Link to="/leagues">All Leagues</Link>
              </div>
              <div className="fresh-footer-column">
                <h4>Support</h4>
                <Link to="/help">Help Center</Link>
                <Link to="/responsible-gambling">Responsible Gaming</Link>
                <Link to="/terms">Terms & Conditions</Link>
                <Link to="/privacy">Privacy Policy</Link>
              </div>
              <div className="fresh-footer-column">
                <h4>Connect</h4>
                <a href="https://twitter.com/fanbet247" target="_blank" rel="noopener noreferrer">🐦 Twitter</a>
                <a href="https://facebook.com/fanbet247" target="_blank" rel="noopener noreferrer">📘 Facebook</a>
                <a href="https://t.me/fanbet247" target="_blank" rel="noopener noreferrer">📱 Telegram</a>
                <a href="mailto:<EMAIL>">✉️ Email Support</a>
              </div>
            </div>
          </div>
          <div className="fresh-footer-bottom">
            <p>© 2024 FanBet247. All rights reserved.</p>
            <div className="fresh-footer-badges">
              <span>✓ SSL Secured</span>
              <span>✓ Licensed</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default NewWelcomePage;
